<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<script>
    var loggedIn=false;
    var allInstitutes = ''
    var showAllInstitute = false
</script>
<style>
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
.form-group .btn-group {
    width: 100%; }
.form-group .btn-group .multiselect.dropdown-toggle {
    width: 100% !important;
    height: 44px;
    line-height: normal;
    background-color: #FFFFFF;
    text-align: left;
    padding: 12px 0 12px 16px;
    box-shadow: none;
    border: 1px solid #cccccc;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 15px;
    color: #000000;
    font-weight: normal; }
.form-group .btn-group .multiselect-container {
    width: 100%;
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64);
    max-height: 250px;
    overflow: hidden;
    overflow-y: auto; }
.form-group .btn-group .multiselect-container li {
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64); }
.form-group .btn-group .multiselect-container a {
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64); }
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none;
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;
    -webkit-transform: translate(0,-50px);
    transform: translate(0,-50px);
}
.modal-content{
    width: 100%;
}
@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">USAGE REPORT ORGANIZATION WISE</h3>
                    <input type="hidden" name="instituteids1" id="instituteids1">
                    <div class="form-group" id="intrst-area">
                        <label>Organizations</label>
                        <p class="form-group">${instituteName}</p>
                        <div class="form-group" id="intrst-area1"></div>
                        <div class="row m-0 p-0 align-items-center" id='select-all'>
                            <input type="checkbox" class="m-0" onchange="selectAllSelected(this.checked)">
                            <p class="m-0 px-3">Select All</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="form-group">
                                <label for="startDate">From Date</label><br>
                                <input type="text" class="w-100 form-control" id="startDate" name="startDate" placeholder="From Date"   autocomplete="off" >
                            </div>
                            <div class="form-group ml-4">
                                <label for="endDate">To Date</label><br>
                                <input type="text" class="w-100 form-control" id="endDate" name="endDate" placeholder="To Date"  autocomplete="off" >
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-primary"  onclick="getUsageData()">Submit</button>
                    <div style="margin-top: 10px;">
                        <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                        <div id="successmsg" style="display: none"></div>
                        <div style="float: right; display: none;" id="download">
                            <div class="form-group">
                                <button type="button" id="download-btn" class="btn btn-primary " style="border-width: 0px;" >Download</button>
                            </div>
                        </div>
                        <div id="batchUsers" style="display: none"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script>
    $('#startDate, #endDate').datepicker({
        format: 'dd-mm-yyyy',
        //startView: 1,
        //todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });
    function getInstitutes() {
        <g:remoteFunction controller="institute" action="getInstituteNames" onSuccess="showData(data)"/>
    }
    function showData(data){
        var strHtml= "";
        roleArr = data.instituteList;
        if(roleArr != undefined && roleArr.length >0){
            strHtml = strHtml + " <select class=\"form-control sage-input-select\" id=\"selectCheckbox\" multiple=\"multiple\" name=\"interests\">\n" ;
            for(var i = 0 ;i< roleArr.length; i++){
                strHtml = strHtml + "<option value=\""+roleArr[i].id+"\" >" + roleArr[i].name +"</option>\n";
                allInstitutes+=roleArr[i].id+','
            }
            strHtml = strHtml + "</select>";
            document.getElementById("intrst-area1").innerHTML=strHtml;
            $('#selectCheckbox').multiselect();
            // document.getElementById("selectCheckbox").innerHTML=strHtml;
        }
    }
    function getUsageData() {
        var instituteids = "";
        if (($('#selectCheckbox').val()) != null && ($('#selectCheckbox').val()) != undefined && $('#selectCheckbox').val().length>0) {
            for (var i = 0; i < ($('#selectCheckbox').val()).length; i++) {
                instituteids = instituteids + $('#selectCheckbox').val()[i] + ",";
            }
        }else{
            instituteids= document.getElementById("instituteids1").value
        }
        if(showAllInstitute){
            instituteids = allInstitutes
        }
        if (instituteids.includes(",")) {
            instituteids = instituteids.substring(0, instituteids.length - 1);
        }
        // document.getElementById("instituteids1").value = instituteids;
        var StartDate = document.getElementById("startDate").value
        var EndDate = document.getElementById("endDate").value
        var inValidStartDate = false;
        if (document.getElementById("startDate").value != "" &&  document.getElementById("endDate").value != ""){
            var  startDate1 = new Date(StartDate.split('-')[2],StartDate.split('-')[1],StartDate.split('-')[0]);
            var  endDate1 = new Date(EndDate.split('-')[2],EndDate.split('-')[1],EndDate.split('-')[0]);
            if(endDate1.getTime() < startDate1.getTime()) inValidStartDate = true;
        }

        if (instituteids === "") {
            document.getElementById("errormsg").innerHTML = "Please Select Institutes."
            $("#errormsg").show();
            $("#batchUsers").hide();
            $('#download').hide();
        }

        else if (document.getElementById("startDate").value === "" || document.getElementById("endDate").value === "") {
            document.getElementById("errormsg").innerHTML = "Please Select From Date and To Date."
            $("#errormsg").show();
            $("#batchUsers").hide();
            $('#download').hide();
        } else if (inValidStartDate){

            document.getElementById("errormsg").innerHTML="Please enter valid From Date. From Date cannot be greater then To date";
            $("#errormsg").show();
            $("#batchUsers").hide();
            $('#download').hide();
        } else {
            $("#errormsg").hide();
            $('#download').hide();
            <g:remoteFunction controller="institute" action="usageReportCorporateWiseData" params="'startDate='+StartDate+'&instituteids='+instituteids+'&endDate='+EndDate" onSuccess = "showReports(data);"/>
        }
    }
    function showReports(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr= "                    <table class='table table-responsive table-striped table-bordered'>\n" +
            "                        <tr>\n" +
            "                            <th>Organization</th>\n" +
            "                            <th>ISBN</th>\n" +
            "                            <th>Title</th>\n" +
            "                            <th>Total Licenses</th>\n" +
            "                            <th>Accessed by</th>\n" +
            "                        </tr>\n" ;
        if(data.status=="OK"){
            var report = data.results;
            for(var i=0;i<report.length;i++){
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+report[i].institutename+"</td>"+
                    "<td>"+report[i].isbn+"</td>" +
                    "<td>"+report[i].title+"</td>" +
                    "<td>"+(report[i].noOfUsers?report[i].noOfUsers:'')+"</td>" +
                    "<td>"+report[i].noofviews+"</td>" +
                    "</tr>";
            }
            htmlStr += "</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#download').show();
        }else{
            document.getElementById("batchUsers").innerHTML= "No Records Found.";
            $('#download').hide();
        }
        $("#batchUsers").show();
    }
    window.onload=()=>{
        var x="${instituteId}"
        if(x){
            document.getElementById("instituteids1").value=x
            document.getElementById('select-all').classList.add('d-none')
        }else{
            getInstitutes()
            document.getElementById('select-all').classList.remove('d-none')
        }
    };
    $(document).ready(function() {$('#selectCheckbox').multiselect(); });

    $('#download-btn').on('click', function() {
        var ids ='';
        if (($('#selectCheckbox').val()) != null && ($('#selectCheckbox').val()) != undefined && $('#selectCheckbox').val().length>0) {
            for (var i = 0; i < ($('#selectCheckbox').val()).length; i++) {
                ids = ids + $('#selectCheckbox').val()[i] + ",";
            }
        }else{
            ids= document.getElementById("instituteids1").value
        }
        if(showAllInstitute){
            ids = allInstitutes
        }
        if (ids.includes(",")) {
            ids = ids.substring(0, ids.length - 1);
        }
        var startDate=document.getElementById("startDate").value
        var endDate=document.getElementById("endDate").value

        window.location.href = "/institute/usageReportCorporateWiseData?download=true&instituteids="+ids+"&startDate="+startDate+"&endDate="+endDate;
    });
    function selectAllSelected(value){
        if(value){
            document.getElementById('intrst-area1').classList.add('d-none')
            document.getElementById(('instituteids1')).value = allInstitutes
            showAllInstitute = true
        }else{
            document.getElementById('intrst-area1').classList.remove('d-none')
            document.getElementById(('instituteids1')).value = ''
            showAllInstitute=false
        }
    }
</script>

</body>
</html>
