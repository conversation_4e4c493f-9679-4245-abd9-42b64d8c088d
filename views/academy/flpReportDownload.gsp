<%@ page import="java.text.SimpleDateFormat" %>


<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />

<script>
    var loggedIn=false;
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>


<sec:ifLoggedIn>
<script>
    loggedIn=true;
</script>
</sec:ifLoggedIn>

<div class="container">
    <div class="row">
        <div class="col-9">
            <a href="javascript:window.history.back();" class="back-btn ml-0">
                &leftarrow; Back
            </a>
        </div>
    </div>
</div>

<div class="container-fluid adminForm"  >
    <div class='row' >
        <div class='col-md-8 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div class="form-group">
                <h3 class="text-center"><strong>FLP  REGISTRATION  REPORT</strong></h3>
                <div class="d-flex align-items-center">
                    <div class="form-group">
                        <label for="startDate"><b>From Date</b></label><br>
                        <input type="text" class="w-100 form-control" id="startDate" name="startDate" placeholder="From Date" autocomplete="off">
                    </div>

                    <div class="form-group ml-4">
                        <label for="endDate"><b>To Date</b></label><br>
                        <input type="text" class="w-100 form-control" id="endDate" name="endDate" placeholder="To Date"  autocomplete="off" >
                    </div>
                </div>

            </div>
            <button class="btn btn-primary"  id="downloadBtn">Download</button>
        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>


<script>

    var fromDate;
    var endDate;
    var hasError;

    $('#startDate, #endDate').datepicker({
        format: 'dd-mm-yyyy',
        //startView: 1,
        //todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });
    $("#downloadBtn").on("click",function (e){
        hasError = false;
        fromDate = $("#startDate").val();
        endDate = $("#endDate").val();

        if( (fromDate==""||fromDate==null) || (endDate==""||endDate==null) ){
            hasError = true;
            alert("Kindly provide from-date and to-date");
        }
        if(!hasError && fromDate>endDate){
            hasError = true;
            alert("Kindly provide dates properly! From-date can't be greater than To-date");
        }
        if(!hasError){
            window.location.href = "/funlearn/flpReportDownload?poStartDate="+fromDate+"&poEndDate="+endDate+"&downloadFlpReport=true";
        }
        e.preventDefault();
    })

</script>

</body>
</html>