<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}

.exam-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.form-check {
    padding: 8px 0;
}

.form-check-label {
    font-weight: normal;
    cursor: pointer;
}

.form-check-input {
    margin-right: 8px;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Feedback report</h3>
                    <div class="form-group table-responsive" id="intrst-area">&nbsp;&nbsp;
                        <g:if test="${contentExamDtls && contentExamDtls.size() > 0}">
                            <form id="examForm">
                                <div class="mb-3">
                                    <label class="form-check-label">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                        <strong>Select All</strong>
                                    </label>
                                </div>

                                <div class="exam-list">
                                    <g:each in="${contentExamDtls}" var="exam" status="i">
                                        <div class="form-check mb-2">
                                            <label class="form-check-label">
                                                <input type="checkbox" name="examIds" value="${exam.id}" class="form-check-input exam-checkbox">
                                                ${exam.year}${exam.month ? ' ' + exam.month : ''}${exam.shift ? ' ' + exam.shift : ''}
                                            </label>
                                        </div>
                                    </g:each>
                                </div>

                                <div class="mt-3">
                                    <button type="button" id="createChaptersBtn" class="btn btn-primary">Create Chapters</button>
                                </div>

                                <g:hiddenField name="bookId" value="${bookId}"/>
                            </form>
                        </g:if>
                        <g:else>
                            <div class="alert alert-info">
                                <p>No exams available for the selected criteria.</p>
                            </div>
                        </g:else>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
$(document).ready(function() {
    // Select All functionality
    $("#selectAll").change(function() {
        var isChecked = $(this).is(":checked");
        $(".exam-checkbox").prop("checked", isChecked);
    });

    // Individual checkbox change handler
    $(".exam-checkbox").change(function() {
        var totalCheckboxes = $(".exam-checkbox").length;
        var checkedCheckboxes = $(".exam-checkbox:checked").length;

        if (checkedCheckboxes === totalCheckboxes) {
            $("#selectAll").prop("checked", true);
        } else {
            $("#selectAll").prop("checked", false);
        }
    });

    // Create Chapters button click handler
    $("#createChaptersBtn").click(function() {
        var selectedExams = [];
        $(".exam-checkbox:checked").each(function() {
            selectedExams.push($(this).val());
        });

        if (selectedExams.length === 0) {
            alert("Please select at least one exam to create chapters.");
            return;
        }

        // Show loading
        $(".loading-icon").removeClass("hidden");

        // Prepare data for submission
        var bookId = $("input[name='bookId']").val();
        var qPaperIds = selectedExams.join(",");

        // Submit to createChapters method
        $.ajax({
            url: "/contentCreation/createChapters",
            type: "POST",
            data: {
                bookId: bookId,
                qPaperIds: qPaperIds
            },
            success: function(response) {
                $(".loading-icon").addClass("hidden");
                alert("Chapters created successfully!");
                // Optionally redirect or refresh the page
               // window.location.reload();
            },
            error: function(xhr, status, error) {
                $(".loading-icon").addClass("hidden");
                alert("Error creating chapters: " + error);
            }
        });
    });
});
</script>

</body>
</html>
