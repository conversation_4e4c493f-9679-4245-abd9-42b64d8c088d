<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta2/css/all.min.css"/>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<style>
.page-item.active .page-link {
    background-color: #007bff !important;
    color: #ffffff;
}
table td a {
    color: #007bff;
}
div#loading-popup {
    opacity:1;
    z-index: 1000000;
}
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>th:first-child:before {
    top: 16px !important;
}
table.dataTable>tbody>tr.child ul.dtr-details>li {
    border-bottom: none !important;
    padding: 0.2em 0 !important;
}
table.dataTable>tbody>tr.child span.dtr-title {
    min-width: 180px !important;
}
@media screen and (max-width: 767px){
    div.dataTables_wrapper div.dataTables_paginate ul.pagination {
        margin: 10px 0 30px !important;
        justify-content: center !important;
    }
    table.dataTable.nowrap th, table.dataTable.nowrap td {
        white-space: normal !important;
    }
    table.dataTable>tbody>tr.child span.dtr-title {
        min-width: 100% !important;
    }
}
div.dataTables_wrapper div.dataTables_filter label
{
    display: flex;
    justify-content: center;
    align-items: center;
}

table.dataTable {
    width: 100% !important;
}
</style>
<div>
    <div class="container-fluid my-5 px-5 publishing_sales">
        <div  class='px-0' id="bookdtl" style="min-height: calc(100vh - 156px);">
            <div class='col-md-12 main mx-auto p-4'>
                <h3 class="text-center mb-4">Group Admin</h3><br>
                <div id="content-books" class="sale">
                    <div class="form-inline align-items-end">
                        <div class="form-group col-md-3">
                            <label for="poStartDate"><strong>From date</strong></label>
                            <input type="text" class="form-control" id="poStartDate" placeholder="Any" value="" autocomplete="off">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="poEndDate"><strong>To date</strong></label>
                            <input type="text" class="form-control" id="poEndDate" placeholder="Any" value="" autocomplete="off">
                        </div>
                    </div>
                    <div class="form-group col-md-6 mt-2">
                        <button type="button" id="search-btn" onclick="codeSearch()" class="btn btn-lg btn-primary col-3">Search</button>
                    </div>
                    <div style="margin-top: 10px;">
                        <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                        <div id="successmsg" style="display: none"></div>
                    </div>

                    <div id="codeList" class="pb-4" style="display: none;"></div>


                </div>
            </div>
        </div>
    </div>
</div>

<g:render template="/books/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script>
    $('#poStartDate, #poEndDate').datepicker({
        format: 'dd-mm-yyyy',
        startView: 1,
        todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });
    function codeSearch() {
        $('.loading-icon').removeClass('hidden');
        document.getElementById("codeList").innerHTML='';
        if ($.fn.dataTable.isDataTable('#codeList')) {
            $('#codeList').DataTable().destroy();
        }
        $('#codeList').show();

        var inputFromDate=$('#poStartDate').val()
        var inputToDate=$('#poEndDate').val()
        console.log(inputFromDate)
        var inputFromDate1
        var inputToDate2
        var inValidStartDate

        if (inputFromDate != "" &&  inputToDate  != ""){
            inputFromDate1 = new Date(inputFromDate.split('-')[2],inputFromDate.split('-')[1],inputFromDate.split('-')[0]);
            inputToDate2 = new Date(inputToDate.split('-')[2],inputToDate.split('-')[1],inputToDate.split('-')[0]);
            if(inputToDate2.getTime() < inputFromDate1.getTime() ) inValidStartDate = true;
        }
        else{
            document.getElementById("errormsg").innerHTML="Please enter from and to date";
            $("#errormsg").show();
            $('.loading-icon').addClass('hidden');
        }
        var Difference_In_Time = inputToDate2.getTime() - inputFromDate1.getTime();
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);
        if(Difference_In_Days>30){
            document.getElementById("errormsg").innerHTML="Please select a maximum of 30 days.";
            $("#errormsg").show();
            $('.loading-icon').addClass('hidden');
        }
        else if (inValidStartDate){
            document.getElementById("errormsg").innerHTML="Please enter valid From Date. From Date cannot be greater then To date";
            $("#errormsg").show();
            $('.loading-icon').addClass('hidden');
        }
        else{
            var isnum=true;

                if(!isnum){
                    $("#errormsg").show();
                    $('.loading-icon').addClass('hidden');
                }
            }
            if(isnum) {
                $("#errormsg").hide();
                <g:remoteFunction controller="groups" action="getAllGroupDetails" params="'startDate='+inputFromDate+'&endDate='+inputToDate" onSuccess = "codesRecieved(data);"/>
            }

    }
    function codesRecieved(data){
        $('.loading-icon').addClass('hidden');
        if(data.groupList.length> 0 && data.groupList!="No records" ) {
            var htmlStr = "<table class='table table-striped table-bordered'>\n" +
                "           <thead class='bg-primary text-white text-center'> <tr>\n" +
                "                            <th>Id</th>\n" +
                "                            <th>Name</th>\n" +
                "                            <th>Group Type</th>\n" +
                "                            <th>Number of likes</th>\n" +
                "                            <th>Number of posts</th>\n" +
                "                            <th>Number of comments</th>\n" +
                "                            <th>Number of members</th>\n" +
                "                            <th>View Group</th>\n" +
                "                        </tr> </thead>\n";
        }
        if(data.groupList.length> 0 && data.groupList!="No records"){
            var codeList = data.groupList;
            if(codeList.length>0)
                for(i=0;i<codeList.length;i++){
                htmlStr +="<tr>" +
                    "<td style='text-transform:capitalize;'>"+codeList[i].id+"</td>"+
                    "<td>"+codeList[i].name+"</td>"+
                    "<td>"+codeList[i].privacy_type+"</td>"+
                    "<td>"+codeList[i].likesCount+"</td>"+
                    "<td>"+codeList[i].postsCount+"</td>"+
                    "<td>"+codeList[i].commentsCount+"</td>"+
                    "<td>"+codeList[i].membersCount+"</td>"+
                    "<td>"+"<a target='blank' href='/groups/groupDtl?groupId=" + codeList[i].id + "&mode=monitor'>View Group</a></td>";
                htmlStr +="</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("codeList").innerHTML= htmlStr;
            $('#codeList table').DataTable( {
                "ordering": true,
                "language": {
                    "searchPlaceholder": "Name , ID ,  Number of likes , Number of members   etc..."
                }
            });
        }else{
            document.getElementById("codeList").innerHTML= "<h6 class='mt-3'>No records found!</h6>";
        }
    }
</script>