<style type="text/css">

.shine {
    background: #d4d7d9;
    background-image: linear-gradient(to right, #d4d7d9 0%, #e4e9ed 20%, #d4d7d9 40%, #d4d7d9 100%);
    background-repeat: no-repeat;
    background-size: 800px 104px;
    display: inline-block;
    position: relative;

    -webkit-animation-duration: 1s;
    -webkit-animation-fill-mode: forwards;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-name: placeholderShimmer;
    -webkit-animation-timing-function: linear;
}

box {
    height: 104px;
    width: 100%;
}

div.line-wrapper {
    display: inline-flex;
    flex-direction: column;
    margin-left: 25px;
    margin-top: 15px;
    vertical-align: top;
}

lines {
    height: 10px;
    margin-top: 10px;
    width: 100%;
}

photo {
    display: block!important;
    width: 325px;
    height: 30px;
    margin-top: 15px;
}
.mt-20{
    margin-top: 10px;
}

@-webkit-keyframes placeholderShimmer {
    0% {
        background-position: -468px 0;
    }

    100% {
        background-position: 468px 0;
    }
}
.flexAlign{
    display: flex;
    justify-content: center;
}

</style>


<div class="tab-content">

    <div role="tabpanel" class="tab-pane fade in show active" id="all">
        <div class="container">

            <% if(!("evidya".equals(session["entryController"]))){%>
            <div id="allAddButton" class="justify-content-end" style="display: flex;">

                <div class="dropdown">
                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                        <i class="material-icons">
                            add
                        </i>  <span>Add</span>
                    </button>
                    <sec:ifLoggedIn>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="javascript:openVideos();"><span>Add new videos 11(Direct)</span></a>
                            <a class="dropdown-item" href="javascript:searchRelatedVideos();"><span>Add related videos (Recommended)</span></a>
                            <a class="dropdown-item" href="javascript:openSolution();"><span>Add related Ref.link (Recommended)</span></a>
                            <a class="dropdown-item nght" href="javascript:createNewSet();"><span>Add revision</span></a>
                        </div>
                    </sec:ifLoggedIn>
                    <sec:ifNotLoggedIn>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add new videos (Direct)</span></a>
                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add related videos (Recommended)</span></a>
                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add related Ref.link (Recommended)</span></a>
                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add revision</span></a>
                        </div>
                    </sec:ifNotLoggedIn>
                </div>

            </div>
            <%}%>
            <div id="content-data-all" class="col-md-12 col-xs-12 col-sm-12"></div>
            <div id="htmlreadingcontent" class="row quiz-section"></div>

        </div>




    </div>









    <%if("1".equals(""+session["siteId"])){%>
    <div class="sharethis-inline-share-buttons" id="sharethisid"></div>
    <%}%>
</div>
<div class="upload-url">
    <div class="modal fade" id="addFileNotes" role="dialog">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Upload Notes</h4>
                </div>
                <div class="modal-body">
                    <div class="row text-center">
                        <g:uploadForm name="resource3Form" url="[action:'addFile',controller:'resourceCreator']"  method="post">

                            <g:textField id="resourceName" name="resourceName"  placeholder="Name" />
                            <input id="file3" type="file"  name="file"  accept=".pdf,.epub , .jpg , .jpeg, .png, .zip" />

                            <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="notesUploadAlert">
                                ** Enter a name for this reading material.
                            </div>
                            <input type="hidden" name="resourceType" value="Notes">
                            <input type="hidden" name="useType" value="notes">
                            <input type="hidden" name="chapterId">
                            <input type="hidden" name="bookId">
                            <input type="hidden" name="quizMode" value="file">
                            <input type="hidden" name="from" value="book">
                        </g:uploadForm>
                    </div>
                </div>
                <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="userWeblinkUploadAlert">
                    ** Enter Name for the notes
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">CANCEL</button>
                    <button type="button" class="btn " onclick="javascript:uploadNotes();">ADD</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

    var shareThis = document.getElementById("sharethisid");
    shareThis.setAttribute("data-url","https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.href ));

    function openSolution() {
        $('#addWeburl').modal('show');
    }

</script>
