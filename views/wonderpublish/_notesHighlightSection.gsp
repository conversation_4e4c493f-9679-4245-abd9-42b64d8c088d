%{--<asset:javascript src="annotator-full.js"/>--}%
%{--<asset:javascript src="annotator.touch.js"/>--}%
<script src="/assets/annotator-full-whitelabel.js"></script>
<script  src="/assets/annotator.touch-whitelabel.js"></script>
<style>
.annotator-gl {
    background-color: rgba(195, 183, 215, 0.83);
}
#htmlContent .annotator-gl {
    background-color: rgba(195, 183, 215, 0.83);
}
</style>
<%  if(!("sage".equals(session['entryController']))) { %>
<style>
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none;
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;
    -webkit-transform: translate(0,-50px);
    transform: translate(0,-50px);
}
.modal-content{
    width: 100%;
}
@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
       /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<%}%>
<script type="text/javascript">
    function googleTranslateElementInit() {
        new google.translate.TranslateElement({pageLanguage: 'en'}, 'google_translate_element');
    }
</script>

<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

<div class="modal" id="removePhone">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title">Translate</h4>
                <button type="button" class="close" data-dismiss="modal" style='margin-top: -26px;'>&times;</button>
            </div>

            <!-- Modal body -->
            <div class="modal-body text-center">
            <div >Original Text :<h4 id="originalText"></h4></div><br>
            <form>
                <div class="form-group">
                    <label for="languageList" style="display: block;">Translate To</label>
                <select onchange='languageSelected(event)' class="form-control" id="languageList">

                </select>
                </div>
            </form>
            <div >Translated Text :<h4 id="translatedText"></h4></div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">

            </div>

        </div>
    </div>
</div>

<script type="text/javascript">
    function openModal(username){
        oldUsername = username
        $('#removePhone').modal('show');

    }
var annotation;
var resIdVal;

Annotator.Plugin.StoreLogger = function (element) {
  return {
    pluginInit: function () {
      this.annotator
      .subscribe("annotationCreated", function (annotation) {
        setTimeout(function(){
%{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
}, 100);
})
.subscribe("annotationUpdated", function (annotation) {
setTimeout(function(){
%{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
}, 100);
})
.subscribe("annotationDeleted", function (annotation) {
setTimeout(function(){
%{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
}, 100);
});
}
}
};

function loadAnnotator(resId) {
    var bookId="";
    <% if(params.bookId == null || params.bookId == '') { %>
    bookId=urlBookId;
    <% } else {%>
    bookId="${params.bookId}";
    <%}%>
if(annotation !== undefined) {
annotation.annotator('destroy');
}

annotation = $('#htmlreadingcontent').annotator();
resIdVal = resId;

annotation.annotator('addPlugin', 'Store', {
    // The endpoint of the store on your server.
    prefix: serverPath+'/wonderpublish',

    // Attach the uri of the current page to all annotations to allow search.
    annotationData: {
        'uri': resId,
        'bookId' : bookId
			},

			urls: {
				// These are the default URLs.
				create:  '/annotateSave',
				update:  '/annotateUpdate/:id',
				destroy: '/annotateDestroy/:id',
				search:  '/annotateSearch'
			},

			// This will perform a "search" action when the plugin loads. Will
			// request the last 20 annotations for the current url.
			// eg. /store/endpoint/search?limit=20&uri=http://this/document/only
			loadFromSearch: {
				'limit': 100,
				'all_fields': 1,
				'uri': resId,
                'bookId':bookId
			},

			showViewPermissionsCheckbox: true,
			showEditPermissionsCheckbox: true
		});

  annotation.annotator('addPlugin', 'Tags');
  annotation.annotator('addPlugin', 'StoreLogger');
  annotation.annotator().annotator("addPlugin", "Touch");
}

    function findPos(obj) {
        var curtop = 0;
        if (obj.offsetParent) {
            do {
                curtop += obj.offsetTop;
            } while (obj = obj.offsetParent);
            return [curtop];
        }
    }

function onclickRenderedNotes(index){

    // $('#overlay').addClass('d-none');
    // $('.export-notes').addClass('d-none');
    // $('#notesMenu').removeClass('active');

     $(notes[index].highlights)[notes[index].highlights.length-1].setAttribute('id','content'+notes[index].id);
    // $('html, body').animate({
    //     scrollTop: $("#"+"content"+notes[index].id).offset().top
    // }, 2000);
    // location.href="#";
    // location.href="#"+'content'+notes[index].id;
    document.getElementById('content'+notes[index].id).scrollIntoView(false);
    // alert(notes[index].highlights[notes[index].highlights.length-1].innerText);
    // window.find(notes[index].highlights[notes[index].highlights.length-1].innerText);
    // window.scroll(0,findPos(document.getElementById('content'+notes[index].id)));
}
var notes;
var notesToExport;
function renderCreatedNotes(data) {
    notes = data;
    // console.log("renderCreatedNotes========")
    // console.log(notes)
    var htmlStr = '';
    var userSelection = '';
    var userComment = '';
    if(data.length > 0) {
        for(var i = 0; i < notes.length; i++) {
            var highlightsMetaData = notes[i].highlights;
            var quoteAndClassNamesArr =[];
            for(var q =0; q < highlightsMetaData.length; q++){
                if(highlightsMetaData[q].offsetParent != null && highlightsMetaData[q].offsetParent != undefined){
                    quoteAndClassNamesArr.push({"quote":highlightsMetaData[q].innerText,"className":highlightsMetaData[q].offsetParent.className.replace('t',' ')});
                }else{
                    quoteAndClassNamesArr.push({"quote":highlightsMetaData[q].innerText,"className":""});
                }
            }
            userSelection = notes[i].quote;
            userComment = notes[i].text;
            // console.log(notes[i]);
            var scrollElement = JSON.stringify(notes[i]);
            if(userComment != null) {
                htmlStr += "<li onclick='onclickRenderedNotes("+i+")' class='notes-list-item' id='"+ notes[i].id +"'>" +
                    "<div class='notes-list-item-indicator'></div>" +
                    "<div class='individual-note-selection' style='display: none;'>" +
                    "<label class='not-active'>" +
                    "<input type='checkbox' name='chapter' value='Chapter'  id='notescheckbox"+i+"'>" +
                    " <span class='checkmark'></span>\n" +
                    "</label>" +
                    "</div>"+
                    "<div class='notes-created-by-user'>" +
                    " <div >" ;
                for(var k = 0; k<quoteAndClassNamesArr.length; k++){
                    htmlStr += "<span style='font-size: 16px; padding-right: 1px' class='note-of-user "+quoteAndClassNamesArr[k].className +"' id='notes"+i+"'>"+quoteAndClassNamesArr[k].quote+"</span>";
                }
                    htmlStr += "<p class='comment-by-user'>"+userComment+"</p>" +
                     "</div>" +
                     "</div>" +
                    "</li>";
            } else {
                htmlStr += "<li onclick='onclickRenderedNotes("+i+")' class='notes-list-item' id='"+ notes[i].id +"'>" +
                    "<div class='notes-list-item-indicator'></div>" +
                    "<div class='individual-note-selection' style='display: none;'>" +
                    "<label class='not-active'>" +
                    "<input type='checkbox' name='chapter' value='Chapter'  id='notescheckbox"+i+"'>" +
                    " <span class='checkmark'></span>\n" +
                    "</label>" +
                    "</div>"+
                    "<div class='notes-created-by-user'>" +
                    " <div >"  ;
                for(var k = 0; k<quoteAndClassNamesArr.length; k++){
                    htmlStr += "<span  style='font-size: 16px; padding-right: 1px' class='note-of-user "+ quoteAndClassNamesArr[k].className +"' id='notes"+i+"'>"+quoteAndClassNamesArr[k].quote+"</span >";
                }
                htmlStr += "</div> " +
                    "</div>" +
                    "</li>";
            }
        }
        htmlStr += "<div class='export-btn-wrapper' style='display: none;'>" +
            "<a href='javascript:exportToStudySet();' class='export-notes-btn waves-effect'>"+"Export"+"</a>" +
            "</div>";
    } else {
        htmlStr += "<div class='no-notes-created' id='no-notes-created'>"+"No notes created yet."+"</div>";
    }
    document.getElementById('user-notes').innerHTML = htmlStr;
}

function renderedNewHighlightedText(data){
    notes.push(data);
    var index = notes.length;
    index = index -1;
    var htmlStr = "";
    var userSelection = data.quote;
    var userComment = data.text;
    if(userComment != null) {
        htmlStr +=  "<li onclick='onclickRenderedNotes("+index+")' class='notes-list-item' id='"+ data.id +"'>" +
            "<div class='notes-list-item-indicator'></div>" +
            "<div class='individual-note-selection' style='display: none;'>" +
            "<label class='not-active'>" +
            "<input type='checkbox' name='chapter' value='Chapter'  id='notescheckbox"+index+"'>" +
            " <span class='checkmark'></span>\n" +
            "</label>" +
            "</div>"+
            "<div class='notes-created-by-user'>" +
            " <div >"  ;
        for(var k = 0; k<data.quoteAndClassNamesArr.length; k++){
            htmlStr += "<span  style='font-size: 16px; padding-right: 1px' class='note-of-user "+ data.quoteAndClassNamesArr[k].className +"' id='notes"+index+k+"'>"+data.quoteAndClassNamesArr[k].quote+"</span >";
        }
        htmlStr += "<p class='comment-by-user'>"+userComment+"</p>" +
            "</div>" +
            "</div>" +
            "</li>";
    } else {
        htmlStr +=  "<li onclick='onclickRenderedNotes("+index+")' class='notes-list-item' id='"+ data.id +"'>" +
            "<div class='notes-list-item-indicator'></div>" +
            "<div class='individual-note-selection' style='display: none;'>" +
            "<label class='not-active'>" +
            "<input type='checkbox' name='chapter' value='Chapter'  id='notescheckbox"+index+"'>" +
            " <span class='checkmark'></span>\n" +
            "</label>" +
            "</div>"+
            "<div class='notes-created-by-user'>" +
            " <div >"  ;
        for(var k = 0; k<data.quoteAndClassNamesArr.length; k++){
            htmlStr += "<span  style='font-size: 16px; padding-right: 1px' class='note-of-user "+ data.quoteAndClassNamesArr[k].className +"' id='notes"+index+k+"'>"+data.quoteAndClassNamesArr[k].quote+"</span >";
        }
        htmlStr += "</div> " +
            "</div>" +
            "</li>";
    }
    if(document.getElementById('no-notes-created') != null && document.getElementById('no-notes-created') != undefined && document.getElementById('no-notes-created') != '') {
        $('#no-notes-created').html('');
        document.getElementById('user-notes').innerHTML = "<li class='notes-list-item'></li>"+ "<div class='export-btn-wrapper' style='display: none;'>" +
            "<a href='javascript:exportToStudySet();' class='export-notes-btn waves-effect'>"+"Export"+"</a>" +
            "</div>";

    }
    if(document.getElementById(data.id) == null || document.getElementById(data.id) == undefined || document.getElementById(data.id) == '') {
        $(htmlStr).insertAfter($('.notes-list-item').last());
    }else{
        htmlStr = htmlStr.replace("<li class='notes-list-item' id='"+ data.id +"'>","");
        htmlStr = htmlStr.replace("</li>","");
        document.getElementById(data.id).innerHTML = htmlStr;
    }

}
function exportToStudySet(){
    closeNotes();
    var cStr="<div class=\"row justify-content-center\" id=\"revisionTitleInput\">\n" +
        "        <div class=\"study-set-item\">\n" +
        "<span class=\"input-studyset\">\n" +
        "                <textarea class=\"study-set-textarea\" placeholder=\"Revision set name\" id=\"revisionTitle\"></textarea>\n" +
        "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"revisionTitle\">\n" +
        "                                <span class=\"input-label-content input-label-content-login\">Revision Set Name</span>\n" +
        "                            </label>\n" +
        "                        </span>"+
        "        </div>\n" +
        "    </div>\n" ;
    var checkedItemsPresent=false;
    var noOfItemsExported=0;
    for(var i = 0; i < notes.length; i++) {
      if(document.getElementById("notescheckbox"+i) != null && document.getElementById("notescheckbox"+i) != undefined ) {
          if(document.getElementById("notescheckbox"+i).checked){
            noOfItemsExported++;
            checkedItemsPresent = true;
            cStr += "<div class=\"study-set-main\" id=\"noteskeyvalueholder_"+noOfItemsExported+"\">\n" +
                "            <span class=\"term-counter\"></span>\n" +
                "<div class='d-flex justify-content-center bg-revCard flex-wrap'>"+
                "            <div class=\"study-set-item\">\n" +
                "<span class=\"input-studyset termDimen\">\n" +
                "                    <textarea class=\"study-set-textarea\" placeholder=\"Enter Term\" id=\"notesterm"+noOfItemsExported+"\" ></textarea>\n" +
                "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"notesterm"+noOfItemsExported+"\">\n" +
                "                                <span class=\"input-label-content input-label-content-login\">Term</span>\n" +
                "                            </label>\n" +
                "                        </span>"+
                "            </div>\n" +
                "            <div class=\"study-set-item\">\n" +
                "<span class=\"input-studyset\">\n" +
                "                    <textarea class=\"study-set-textarea\" placeholder=\"Enter Definition\" id=\"notesdefinition"+noOfItemsExported+"\">"+notes[i].quote+"</textarea>\n" +
                "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"notesdefinition"+noOfItemsExported+"\">\n" +
                "                            </label>\n" +
                "                        </span>"+
                "            </div>\n" +
                "        </div>"+
                    "</div>";
        }
      }
    }
    if(checkedItemsPresent) {
        cStr +=" <div class=\"add-study-card-btn-wrapper add-card-btn-wrapper-save\">\n" +
            "            <a href=\"javascript:saveKeyValues("+noOfItemsExported+");\" class=\"add-study-card-btn\">\n" +
            "                <span>Save</span>\n" +
            "            </a>\n" +
            "        </div>";
        $("#study-set-wrapper").hide();
        document.getElementById("study-set-wrapper").innerHTML="";
        document.getElementById("study-set-from-notes").innerHTML = cStr;
        $("#study-set-from-notes").show();

        $('#chapter-details-tabs a[href="#studySets"]').tab('show');
        $("#content-data-studyset-nosets").hide();
        $("#content-data-studyset").hide();
        $("#study-set-wrapper-container").show();
        newStudySet=true;
        studySetResId=-1;

    }else{
        alert("Select atleast one item");
    }

}



function displaySelectionCheckbox() {
    if($('.export-study-set').html() == 'Export to Revision') {
        $('.export-study-set').html('Cancel').addClass('cancel-export-study-set');
        $('.comment-by-user').hide();
        $('.notes-list-item-indicator').hide();
        $('.individual-note-selection').show();
        $('.export-btn-wrapper').show();
    } else {
        $('.export-study-set').html('Export to Revision').removeClass('cancel-export-study-set');
        $('.comment-by-user').show();
        $('.notes-list-item-indicator').show();
        $('.individual-note-selection').hide();
        $('.export-btn-wrapper').hide();
    }
}

function languageSelected(event) {
    $.ajax({
        type: "POST",
        headers: { 'Authorization': 'Bearer ************************************************************************************************************************************************************************************************************************************' },
        url: "https://translation.googleapis.com/language/translate/v2\n" +
            "?key=AIzaSyBCrQXrkQEcERPCB2QS8BGShqztBa-DL-w",
        data:{
            "q":$('#originalText').text(),
            "target":event.target.value.trim()
        },
        success: function(data, status){
            $('#translatedText').text(data.data.translations[0].translatedText);
            console.log(data);
        }
    });
    console.log(event.target.value);
}
</script>
