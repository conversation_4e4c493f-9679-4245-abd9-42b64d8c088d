<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>
<style>
    body {
        overflow-x: hidden;
    }
    .bookDetails__container{
        width: calc(100%  - 20%);
        margin: 0 auto;
    }
    @media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px){
        .bookDetails__container{
            width: calc(100%  - 2%);
            margin: 0 auto;
        }
    }
    .relatedTitle{
        margin-left: 30px;
    }

    /* Modern UI Styles */
    .btn-outline-primary-modifier {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        border-width: 1.5px;
        padding: 8px 16px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .btn-outline-primary-modifier:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .btn-outline-primary-modifier.current {
        background-color: var(--primary);
        color: white;
    }

    .filter-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .form-control-modifier {
        border-radius: 8px;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
        border-width: 0.2em;
        margin-right: 0.5rem;
    }

    /* Dashboard Button Styles */
    .btn-dashboard {
        background: linear-gradient(45deg, #4776E6, #8E54E9);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .btn-dashboard:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0,0,0,0.15);
        color: white;
    }

    .btn-dashboard:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
        transition: all 0.6s ease;
    }

    .btn-dashboard:hover:before {
        left: 100%;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        50% {
            box-shadow: 0 4px 20px rgba(71, 118, 230, 0.4);
        }
        100% {
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
    }

    .btn-dashboard {
        animation: pulse 2s infinite;
    }
</style>

<section class="page-main-wrapper mdl-js library my_books mt-5">
    <div class="container px-0">

        <div class="row page_title justify-content-start align-items-center mx-0 px-3 col-12 col-md-10 mx-auto">
            <h4>
                <%if(institutes!=null){%>
                <strong>${institutes[0].name}<%if(!"true".equals(""+session["digitalLibraryLandingPage"])){%> - iBookGPT<%}%></strong>
                <%} else {%>
                <strong>My Books</strong>
                <%}%>
            </h4>
        </div>
        <%if(showAccessCode&&!"Yes".equals(""+session["disableScratchCode"])){%>
        <div class="row justify-content-start align-items-center mx-0 px-3 pt-3 col-12 col-md-10 mx-auto">
            <p>Do you have  scratch/access code? <a href="/wsLibrary/accessCode">Click here</a></p>
        </div>
        <%}%>

        <div id="libraryTabs" class="row mx-0 my-4 px-3 col-12 col-md-10 mx-auto">
        <%if(!"25".equals(""+session["siteId"])&&showMyBooks){%>
            <a id="myEbooks" href="javascript:getMySelfBooks();" class="btn btn-outline-primary btn-outline-primary-modifier fadein-animated mr-2 mb-2">
                <i class="material-icons mr-2" style="vertical-align: text-bottom;">book</i> My Books
            </a>
            <%}%>
            <%if(institutes!=null){
            String instituteName = "";
            %>
            <g:each in="${institutes}" var="institute" status="i">
                <%if(institutes.size()>1) instituteName = institute.name;
                else instituteName="Library Books"%>
                <%if("${institute.batchName}"=="Default" && isLibrarian){%>
                    <a href="javascript:getInsLibraryBooks('${institute.id}','${institute.batchId}',false);" id="inst-${institute.id}" class="btn btn-outline-primary btn-outline-primary-modifier mr-2 mb-2 institute-tab"><i class="material-icons mr-2" style="vertical-align: text-bottom;">local_library</i><span>${instituteName}</span></a>
                <%} else {%>
                <%if("${institute.batchName}"=="Default" && "${institute.fullLibraryView}" != 'false'){%>
                <a href="javascript:getInsLibraryBooks('${institute.id}','${institute.batchId}',false);" id="inst-${institute.id}" class="btn btn-outline-primary btn-outline-primary-modifier mr-2 mb-2 institute-tab"><i class="material-icons mr-2" style="vertical-align: text-bottom;">local_library</i><span>${instituteName}</span></a>
                <%}else if("${institute.batchName}"=="Default" && "${institute.fullLibraryView}" == 'false'){%>
                <a href="javascript:getInsLibraryBooks('${institute.id}','${institute.batchId}',true);" id="inst-${institute.id}" class="btn btn-outline-primary btn-outline-primary-modifier mr-2 mb-2 institute-tab"><i class="material-icons mr-2" style="vertical-align: text-bottom;">local_library</i><span>${instituteName}</span></a>
                <%}%>
                <%}%>
            </g:each>

            <%}%>
        <%if(hasDriveAccess){%>
        <a href="javascript:showCopilot()" class="btn btn-outline-primary btn-outline-primary-modifier fadein-animated mr-2 mb-2"><i class="material-icons mr-2" style="vertical-align: text-bottom;">create</i> Studio</a>
        <%}%>
        <%if(hasRaAccess){%>
        <a href="javascript:showAIRA()" class="btn btn-outline-primary btn-outline-primary-modifier fadein-animated mr-2 mb-2"><i class="material-icons mr-2" style="vertical-align: text-bottom;">psychology</i> Research Assistant</a>
        <%}%>
        <%if(showReferenceSection){%>
        <a href="javascript:showReferenceSection()" class="btn btn-outline-primary btn-outline-primary-modifier fadein-animated mr-2 mb-2"><i class="material-icons mr-2" style="vertical-align: text-bottom;">menu_book</i> Reference Section</a>
        <%}%>


        </div>
        <div id="booksSection">
        <div id="instituteClasses" class="row mx-0 px-3 col-12 col-md-10 mx-auto fadein-animated" style="display: none;">
            <%if(institutes!=null){%>
            <select class="form-control" id="insClassSelect" onchange="javascript:getInsClassBooks();">
            </select>
            <% if(enableTest){
                if(isInstructor){%>
            &nbsp;<a href="/test-generator" class="btn btn-info fadein-animated mr-2">Create Test</a>
            <%}%>
            &nbsp;<a href="/onlineTest/listTests?instituteId=" class="btn btn-primary fadein-animated mr-2">View Tests</a>
            <%}
                if(isInstructor&&enableQuestionPaper){%>

                    &nbsp;<a href="/questionPaper/listQuestionPapers?instituteId=" class="btn btn-secondary fadein-animated mr-2">Question Papers</a>
            <%}}%>

        </div>
        <div id="validityDateNotice" class="row mx-0 px-3 col-12 col-md-10 mx-auto fadein-animated" style="display: none;">
        </div>
        <div id="libraryFilter" class="row mx-0 my-4 px-3 col-12 col-md-10 mx-auto fadein-animated" >
            <div class="card card-modifier col-12 p-4 filter-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0 text-dark d-flex align-items-center">
                        <i class="material-icons mr-2">filter_list</i> Filters
                    </h5>
                    <button onclick="javascript:resetLibraryFilters();" id="resetLibraryFilter" class="btn btn-sm btn-outline-secondary" disabled="disabled">
                        <i class="material-icons" style="font-size: 16px; vertical-align: text-bottom;">refresh</i> Clear Filters
                    </button>
                </div>

                <div class="row align-items-end">
                    <div class="col-md-3 mb-3">
                        <label for="syllabusFilter" class="form-label small text-muted">Syllabus</label>
                        <select id="syllabusFilter" onchange="javascript:syllabusChangeHandler();" class="form-control form-control-modifier">
                            <option value="">Select Syllabus</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="gradeFilter" class="form-label small text-muted">Grade</label>
                        <select id="gradeFilter" onchange="javascript:gradeChangeHandler();" class="form-control form-control-modifier" disabled="disabled" style="display: none">
                            <option value="">Select Grade</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="subjectFilter" class="form-label small text-muted">Subject</label>
                        <select id="subjectFilter" onchange="javascript:filterBooksBySubject();" class="form-control form-control-modifier" disabled="disabled" style="display: none">
                            <option value="">Select Subject</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="librarySearch" class="form-label small text-muted">Search</label>
                        <div class="input-group">
                            <input type="text" class="form-control form-control-modifier" name="librarySearch" id="librarySearch" autocomplete="off" placeholder="Title, ISBN, etc.">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-primary" onclick="javascript:submitLibrarySearch();" id="search-btn-library">
                                    <i class="material-icons" style="font-size: 18px;">search</i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="quick-search hide">
                    <h6 id="librarySearchEmpty" class="text-center mt-3" style="display: none;">No books found!</h6>
                </div>
            </div>
        </div>

        <sec:ifLoggedIn>

        <div id="checkWaitingListBtn" class="col-12 col-md-10 mx-auto queue-list-btn show-library fadein-animated" style="display:none;">
            <%if("Yes".equals(""+session["disableStore"])){%>
            <!-- nothing right now -->
            <%}else{%>
           <!-- <a href="javascript:openQueueList();" class="btn btn-lg float-right">Check Waiting List</a>-->
            <%}%>
        </div>
        </sec:ifLoggedIn>

        <div class="tab-content col-12 col-md-10 pb-4 px-3 mx-auto">
            <div id="books" class="tab-pane active">

                <div class="books-content-wrapper books-list show-library" id="content-data-books">
                    <div id="currently-reading-books"></div>
                    <h4 id="totalBooksCount" class="d-flex align-items-center"></h4>
                    <div id="show-library-books" class="row">

                    </div>
                    <div id='loadMoreButton' class='text-center mt-5 mb-4' style='display: none;'>
                        <button class='btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'>
                            <i class="material-icons mr-2" style="vertical-align: text-bottom;">expand_more</i> <span>Show More</span>
                        </button>
                    </div>
                </div>

            </div>
        </div>
            <%if(instituteHasDriveAccess){%>
            <div class="mx-0 my-3 px-3 col-12 col-md-10 mx-auto fadein-animated" id="sharedFiles" style="display: none">
            <h5>Shared Files</h5>
            <table class="table table-hover" id="sharedFilesTable">
                <thead class="thead-light">
                <tr>
                    <th style="width: 30%;"><a href="#" onclick="sortTable(0)">Name</a></th>
                    <th style="width: 20%;"><a href="#" onclick="sortTable(1)">Date Modified</a></th>
                    <th style="width: 50%;">Shared by</th>
                </tr>
                </thead>
                <tbody id="sharedFilesTableBody">
                <%-- Folders --%>


                <%-- Files --%>

                </tbody>
            </table>
        </div>
            <%}%>
        </div>
        <%if(hasDriveAccess){%>
        <g:render template="/studyMaterial/copilot"></g:render>
        <%}%>
        <%if(hasRaAccess){%>
        <g:render template="/webInteraction/aira"></g:render>
        <%}%>
        <%if(showReferenceSection){%>
        <g:render template="/digitalLibrary/categorySection"></g:render>
        <%}%>

    </div>
</section>

<div class="modal modal-modifier fade" id="deleteBook" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered" role="document">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3">Are you sure?</h5>
                <p id="remove-msg">You want to remove this eBook from your library?</p>

                <div class="d-flex justify-content-center py-3">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-danger btn-danger-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-5 col-md-4 ml-2" onclick="javascript:bookDelete();">Yes, Delete</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div id="bookQueueModal" class="modal modal-modifier fade">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom" role="document">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>
            <div class="modal-body modal-body-modifier text-center">
                <div id="bookValidity" style="display: none;">
                    <h5 class="mt-3">Hey! This eBook is already issued to another student.<br></h5>
                    <p><strong>Don't worry!</strong>  You can still add this eBook to your waiting list and we will notify you soon.</p>
                    <p class="text-danger text-danger-modifier mt-2">Your waiting number <strong id="bookValidityModalText"></strong><br></p>
                    <div class="d-flex justify-content-end justify-content-center py-3">
                        <button type="button" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0" id="addtoQueue" onclick="javascript:addBookToQueue();">Add eBook to waiting list</button>
                    </div>
                </div>
                <div id="bookReturned" style="display: none;">
                    <h5 class="mt-3 text-dark">Thanks! You've successfully returned the book.</h5>
                    <p>Feel free to check other Books to keep your learning game!</p>
                    <div class="d-flex justify-content-center py-3">
                        <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Exit</button>
                        <button type="button" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-5 col-md-4 ml-2" onclick="javascript:window.location.reload();">View Library</button>
                    </div>
                </div>
                <div id="bookAddedQueue" style="display: none;">
                    <div id="bookAddedIcon" class="scaleAnimation correct-animated-icon">
                        <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                            <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                        </svg>
                    </div>
                    <div id="bookExistIcon" class="info-animated-icon" style="display: none;">
                        <div class="modal-info-icon scaleAnimation">
                            <span class="info-icon-body pulseAnimationIns"></span>
                            <span class="info-icon-dot pulseAnimationIns"></span>
                        </div>
                    </div>
                    <h5 id="checkAddedQueue" class="m-0 text-dark">This eBook is added to waiting list.</h5>
                    <div class="d-flex justify-content-center py-3">
                        <button type="button" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0" onclick="javascript:openQueueList();">Check waiting list</button>
                    </div>
                </div>
                <div id="bookRemovedQueue" style="display: none;">
                    <div class="scaleAnimation correct-animated-icon">
                        <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                            <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                        </svg>
                    </div>
                    <h5 class="m-0 text-dark">Book is removed successfully!</h5>
                    <div class="d-flex justify-content-center py-3">
                        <button type="button" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0" data-dismiss="modal">Okay</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>


<%if("true".equals(session["prepjoySite"])){%>
<g:render template="/prepjoy/prepjoy-loader"></g:render>
<% } %>

<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>
    var pageNo = 1;
    var allBooksLoaded = false;
    var libraryBooks;
    var booksCount;
    var urlTag = "";
    var deleteBookId;
    var showInsBooks = false;
    var instituteId;
    var batchId;
    var lastReadBooks;
    var lastReadBookPresent = false;
    var freeBooks = null;
    var paidBooks = null;
    var syllabusSelected = null;
    var gradeSelected = null;
    var filter = null;
    var showPriceTab=false;
    var institutePresent = '${params.instituteId}';
    var urltags;
    var bookIds;
    var bookTitles;
    var batchIds;
    var bookQueueId;
    var libraryMode = '${params.mode}';
    var queueBooksPresent = false;
    var redirectToQueueBooks = false;
    var prepjoySite = "${session['prepjoySite']}";
    var siteName = "";
    var userLibraryValidityExpired = false;
    var testSeries = null;
    var searchBookId="";
    var showRechargeOption = false;
    var firstTimeLoad= true;
    var firstTimeSyllabusChangeCalled=false;
    var tagsReceived = false;
    var booksReceived = false;

    //create a variable called sujectsList to store the subjects list
    var subjectsList = {};


    <%if("true".equals(session["commonWhiteLabel"])){%>
    siteName = "${session['siteName']}";
    <%}else{%>
    siteName = "${session['entryController']}";
    <%}%>

    // GET LAST READ BOOKS
    function getInstituteLastReadBooks(batchId) {
        lastReadBookPresent = false;
        if(!lastReadBookPresent) {
            <g:remoteFunction controller="libraryBooks" action="getInstituteLastReadBooks" params="'batchId='+batchId"  onSuccess='displayLastReadBooks(data);'/>
        }
    }

    // DISPLAY LAST READ BOOKS
    function displayLastReadBooks(data) {
        lastReadBooks = JSON.parse(data.lastReadBooksForInstitute);
        var htmlStr = "";
        var newObj = {};
        var newArr = [];

        if(lastReadBooks == '' || lastReadBooks == null || lastReadBooks == 'null' || lastReadBooks == '[]' || lastReadBooks.length <= 0) {
            lastReadBookPresent = false;
            document.getElementById("currently-reading-books").innerHTML = "";
        } else {
            lastReadBookPresent = true;

            for(let i = 0; i < lastReadBooks.length; i++){
                newObj[lastReadBooks[i]['bookId']] = lastReadBooks[i];
            }
            let keys = Object.keys(newObj);
            for(let j = keys.length - 1; j >= 0; j--){
                newArr.push(newObj[keys[j]]);
            }
            lastReadBooks = newArr;

            htmlStr +="<h4>Currently Reading</h4><div class='row'>";
            for(var books of lastReadBooks){
                htmlStr +="<div class='col-6 col-md-4 col-lg-3 library-book fadein-animated'>"+
                    displayLibraryBooks(books);
                htmlStr +="</div>";
            }
            lastReadBookPresent = false;
            htmlStr +="</div>";

            document.getElementById("currently-reading-books").innerHTML =  htmlStr;
            uncoverBookUI();
        }
    }

    // GET USER SELF BOOKS
    function getUserBooksList(pageNo, allBooksLoaded) {
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <%if(instituteHasDriveAccess){%>
         $("#sharedFiles").hide();
        <%}%>
        if (!allBooksLoaded) {
            <g:remoteFunction controller="libraryBooks" action="getUsersBooks" params="'pageNo='+pageNo" onSuccess='displayBooks(data);'/>
        }
    }

    // GET USER INSTITUTE BOOKS
    function getInstituteBooksList(pageNo, instituteId, batchId, allBooksLoaded, filter, freeBooks, paidBooks, syllabus, grade,testSeries) {
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        if (!allBooksLoaded) {
            <g:remoteFunction controller="libraryBooks" action="getUsersInstituteBooks" params="'batchId='+batchId+'&pageNo='+pageNo+'&filter='+filter+'&freeBooks='+freeBooks+'&paidBooks='+paidBooks+'&syllabus='+syllabus+'&grade='+grade+'&testSeries='+testSeries+'&searchBookId='+searchBookId" onSuccess='displayBooks(data);'/>
        }
        <%if(hasDriveAccess){%>
            <g:remoteFunction controller="studyMaterial" action="getSharedMaterialsForBatch" params="'batchId='+batchId" onSuccess='displaySharedMaterials(data);'/>
        <%}%>
    }

 // GET INSTITUTE LIBRARY BOOKS
    function displaySharedMaterials(data){
        var tableBody = document.getElementById('sharedFilesTableBody');
        tableBody.innerHTML = '';  // Clear existing rows
        var materials = data.sharedMaterials;


        if(materials.length > 0) {

            // Add materials to the table
            materials.forEach(function (material) {
                var materialRow = '<tr>' +
                    '<td><i class="fas fa-file-alt"></i>&nbsp;' +
                    '<a href="javascript:viewMaterial(' + material.materialId + ')">' + material.fileName + '</a>' +
                    '</td>' +
                    '<td>' + formatDate(material.lastUpdated) + '</td>' +
                    '<td>' + material.name + '</td>' +
                    '</tr>';
                tableBody.innerHTML += materialRow;
            });
            $("#sharedFiles").show();
        }
    }
    // GET INSTITUTE CLASS BOOKS
    function getInsClassBooks() {
        $('#resetLibraryFilter').attr('disabled', 'disabled');

        if(document.getElementById("insClassSelect").selectedIndex != 0) {
            let selectedValue = document.getElementById("insClassSelect")[document.getElementById("insClassSelect").selectedIndex].value;
            instituteId = selectedValue.substring(0,selectedValue.indexOf('_'));
            batchId = selectedValue.substring(selectedValue.lastIndexOf('_')+1);
            getInsClassLibraryBooks(instituteId, batchId);
        }
    }

    function createTest(){
        if(document.getElementById("insClassSelect").selectedIndex == 0){
            alert("Please select a class to create test");
            return;
        }else{
            let selectedValue = document.getElementById("insClassSelect")[document.getElementById("insClassSelect").selectedIndex].value;
            instituteId = selectedValue.substring(0,selectedValue.indexOf('_'));
            batchId = selectedValue.substring(selectedValue.lastIndexOf('_')+1);
            window.location.href = "/test-generator?instituteId="+instituteId+"&batchId="+batchId;
        }
    }

    // GET WAITING LIST BOOKS
    function openQueueList() {
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        redirectToQueueBooks = false;
        $("#instituteClasses, #libraryFilter, #checkWaitingListBtn, #loadMoreButton").hide();
        document.getElementById("show-library-books").innerHTML = "";
        document.getElementById("currently-reading-books").innerHTML = "";
        document.getElementById('totalBooksCount').innerHTML = "";
        $("#bookQueueModal").modal("hide");
        <g:remoteFunction controller="wsLibrary" action="getBooksInQueueForUser"  onSuccess='displayQueueBooks(data);'/>
    }

    function displayQueueBooks(data){
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        $('html, body').animate({scrollTop:0}, 'slow');
        var queueBooks = data.QueueBooks;
        var htmlStr = "";
        if(queueBooks.length > 0) {
            queueBooksPresent = true;
            for (var books of queueBooks) {
                htmlStr += "<div class='col-6 col-md-4 col-lg-3 library-book fadein-animated'>" +
                    displayLibraryBooks(books);
                htmlStr += "</div>";
            }
            queueBooksPresent = false;
        } else {
            htmlStr  = "<div class='no-books-available mx-auto px-5'>" +
                "<p class='text-center pt-4'><strong>Congratulations,</strong> there are no Books on your waiting list.</p>" +
                "<div class='text-center'><a href='javascript:backToLibrary();' class='btn btn-lg btn-warning btn-warning-modifier btn-shadow click-here-link mt-3 px-4 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'>Back to Library</a></div>" +
                "</div>";
        }
        document.getElementById("show-library-books").innerHTML = htmlStr;
        document.getElementById('totalBooksCount').innerHTML = "<button class='material-icons border-0 mr-2 go-back-btn' onclick='javascript:backToLibrary();'>keyboard_backspace</button> Waiting List";
        uncoverBookUI();
    }

    // BACK TO LIBRARY FROM WAITING LIST
    function backToLibrary() {
        document.getElementById("inst-"+institutePresent).click();
    }

    // GET INSTITUTE BOOKS TAGS
    function getInstituteBooksTags(batchId) {
        <g:remoteFunction controller="libraryBooks" action="getInstituteBooksTags" params="'batchId='+batchId"  onSuccess='appendSyllabusGradeTags(data);'/>
    }

    // APPENDING SYLLABUS & GRADES INTO SELECT BOX
    let booksTags = '';
    let syllabusSelector = document.getElementById('syllabusFilter');
    let gradeSelector = document.getElementById('gradeFilter');
    let subjectSelector = document.getElementById('subjectFilter');
    function appendSyllabusGradeTags(data) {
        // Appending Syllabus and Grades
        booksTags = JSON.parse(data.booksTags);
        //sort bookTags is json array of syllabus and grades. Sort it by syllabus
        let keys = Object.keys(booksTags);
        keys.sort();
        let sortedBooksTags = {};
        for(let key of keys) {
            sortedBooksTags[key] = booksTags[key];
        }
        booksTags = sortedBooksTags;
        syllabusSelector.options.length = 1;
        gradeSelector.options.length = 1;
        subjectSelector.options.length = 1;
        for(let syllabus in booksTags) {
            const syllabusValues = syllabus;
            const element = document.createElement('option');
            element.textContent = syllabusValues;
            element.value = syllabusValues;
            syllabusSelector.appendChild(element);
        }
        tagsReceived = true;
        if(tagsReceived && booksReceived) triggerSyllabusChange();
    }

    function triggerSyllabusChange()
    {
        if(firstTimeLoad){
            firstTimeLoad = false;
            //check if the syllabus cookie is set and if the syllabus is present in the syllabus list
            let selectedSyllabus = getCookie("selectedSyllabus");
            if(selectedSyllabus!=null&&selectedSyllabus!=""){
                let syllabusFound = false;
                for(let i=0;i<syllabusSelector.options.length;i++){
                    if(syllabusSelector.options[i].value==selectedSyllabus){
                        syllabusSelector.selectedIndex = i;
                        syllabusFound = true;
                        //call the syllabus change handler
                        firstTimeSyllabusChangeCalled = true;
                        syllabusChangeHandler();
                        break;
                    }
                }
                if(!syllabusFound){
                    document.cookie = "selectedSyllabus=; expires=Thu, 01 Jan 1970 00:00:00 UTC";
                    firstTimeLoad = false;
                }
            }
        }
    }

    // FILTER - GET FREE BOOKS
    function getInsFreeBooks() {
        showInsBooks = true;
        allBooksLoaded = false;
        freeBooks = "yes";
        paidBooks = null;
        testSeries = null;
        filter = true;
        pageNo = 1;
        document.getElementById("show-library-books").innerHTML = "";
        getInstituteBooksList(pageNo, instituteId, batchId, allBooksLoaded, filter, freeBooks, paidBooks, syllabusSelected, gradeSelected,testSeries);
        $("#loadMoreButton").hide();
    <!--    $('#freeBooksBtn').addClass('selected').siblings().removeClass('selected');-->
        $('#resetLibraryFilter').removeAttr('disabled');
    }

    // FILTER - GET PAID BOOKS
    function getInsPaidBooks() {
        showInsBooks = true;
        allBooksLoaded = false;
        paidBooks = "yes";
        testSeries = null;
        freeBooks = null;
        filter = true;
        pageNo = 1;
        document.getElementById("show-library-books").innerHTML = "";
        getInstituteBooksList(pageNo, instituteId, batchId, allBooksLoaded, filter, freeBooks, paidBooks, syllabusSelected, gradeSelected,testSeries);
        $("#loadMoreButton").hide();
       // $('#paidBooksBtn').addClass('selected').siblings().removeClass('selected');
        $('#resetLibraryFilter').removeAttr('disabled');
    }

    // FILTER - GET FREE BOOKS
    function getInsTestSeriesBooks() {
        showInsBooks = true;
        allBooksLoaded = false;
        freeBooks = null;
        paidBooks = null;
        testSeries = "yes";
        filter = true;
        pageNo = 1;
        document.getElementById("show-library-books").innerHTML = "";
        getInstituteBooksList(pageNo, instituteId, batchId, allBooksLoaded, filter, freeBooks, paidBooks, syllabusSelected, gradeSelected,testSeries);
        $("#loadMoreButton").hide();
      //  $('#testSeriesBooksBtn').addClass('selected').siblings().removeClass('selected');
        $('#resetLibraryFilter').removeAttr('disabled');
    }

    // FILTER - GET SYLLABUS BOOKS
    function syllabusChangeHandler() {
        let syllabus = syllabusSelector[syllabusSelector.selectedIndex].value;

        //store the selected syllabus in a cookie
        if(syllabusSelector.selectedIndex>0) document.cookie = "selectedSyllabus="+syllabus;
        else document.cookie = "selectedSyllabus=; expires=Thu, 01 Jan 1970 00:00:00 UTC";

        gradeSelector.options.length = 1;
        subjectSelector.options.length = 1;
        //hide the subject filter
        $('#subjectFilter').hide();
        if(syllabus !== "") {
            gradeSelected = null;
            syllabusSelected = syllabus.split('-').pop();
            const gradesList = booksTags[syllabus];
            
            // Check if all grades are numeric
            const allNumeric = gradesList.every(grade => grade !== null && !isNaN(grade));
            
            // Sort numerically if all are numbers, otherwise sort alphabetically
            let sortedGradesList = [...gradesList];
            if (allNumeric) {
                sortedGradesList.sort((a, b) => Number(a) - Number(b));
            } else {
                sortedGradesList.sort();
            }

            for(let grade of sortedGradesList) {
                if(grade!=null) {
                    const element = document.createElement('option');
                    element.textContent = grade;
                    element.value = grade;
                    gradeSelector.appendChild(element);
                }
            }
        } else {
            syllabusSelected = gradeSelected = null;
        }

        showInsBooks = filter = true;
        allBooksLoaded = false;
        pageNo = 1;
        document.getElementById("show-library-books").innerHTML = "";
        $("#loadMoreButton").hide();
        getInstituteBooksList(pageNo, instituteId, batchId, allBooksLoaded, filter, freeBooks, paidBooks, syllabusSelected, gradeSelected,testSeries);
        $('#resetLibraryFilter').removeAttr('disabled');
       if(gradeSelector.options.length>1) {
            $('#gradeFilter').removeAttr('disabled');
            $('#gradeFilter').show();
        }else{
           $('#gradeFilter').hide();
       }

    }

    // FILTER - GET GRADE BOOKS
    function gradeChangeHandler() {
        gradeSelected = gradeSelector[gradeSelector.selectedIndex].value;
        //store the selected grade in a cookie
        if(gradeSelector.selectedIndex>0) document.cookie = "selectedGrade="+gradeSelected;
        else document.cookie = "selectedGrade=; expires=Thu, 01 Jan 1970 00:00:00 UTC";

        if(gradeSelected === "") gradeSelected = null;
        showInsBooks = filter = true;
        allBooksLoaded = false;
        pageNo = 1;
        document.getElementById("show-library-books").innerHTML = "";
        $("#loadMoreButton").hide();
        getInstituteBooksList(pageNo, instituteId, batchId, allBooksLoaded, filter, freeBooks, paidBooks, syllabusSelected, gradeSelected,testSeries);
        $('#resetLibraryFilter').removeAttr('disabled');
    }

    // DISPLAY BOOKS
    function displayBooks(data) {
        <%if(hasDriveAccess){%>
        $("#copilot").hide();
        <%}%>
        <%if(hasRaAccess){%>
        $("#aira").hide();
        <%}%>
        <%if(showReferenceSection){%>
        $("#referenceSection").hide();
        <%}%>
        $("#booksSection").show();
        searchBookId="";
        userLibraryValidityExpired = false;
        $("#validityDateNotice").hide();
        if(data.hasOwnProperty("validityDateOfUser")&&data.validityDateOfUser!=null&&data.validityDateOfUser!="null"){
            if(data.validityOver=="true"){
                userLibraryValidityExpired=true;
                document.getElementById("validityDateNotice").innerHTML="<span style='color:red'>Membership expired on "+data.validityDateOfUser+"</span>";
            }else{
                document.getElementById("validityDateNotice").innerHTML="<span style='color:green'> Membership validity : </span>&nbsp; "+data.validityDateOfUser;
            }
            $("#validityDateNotice").show();
        }

        if(data.searchSuccessful=="false"){
            document.querySelector(".quick-search").classList.replace("hide", "show");
            document.querySelector("#librarySearchEmpty").setAttribute("style", "display: block");
        }

        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        if(showInsBooks) libraryBooks = JSON.parse(data.books);
        else libraryBooks = data.books;

        booksCount = data.count;

        var htmlStr = "";
        var newObj = {};
        var newArr = [];

        if(libraryBooks == '' || libraryBooks == null || libraryBooks == 'null' || libraryBooks == '[]' || libraryBooks.length <= 0) {
            if(!allBooksLoaded && pageNo == 1) {
                $("#totalBooksCount").html("");
                document.getElementById("show-library-books").innerHTML = noBooksUILayer();
            }
            allBooksLoaded = true;
            $("#loadMoreButton").hide();
            <%if(institutes!=null){%>
            if(libraryMode != undefined && libraryMode != "undefined" && libraryMode != "" && !showInsBooks) {
                $("#myEbooks").hide();
                $("#libraryTabs a:nth-child(2) span").trigger("click");
            }
            <%}%>
        } else {

            showPriceTab = libraryBooks[0].showtabs;

            if(!showInsBooks) {
                for (let i = 0; i < libraryBooks.length; i++) {
                    newObj[libraryBooks[i]['id']] = libraryBooks[i];
                }
                let keys = Object.keys(newObj);
                for (let j = keys.length - 1; j >= 0; j--) {
                    newArr.push(newObj[keys[j]]);
                }
                libraryBooks = newArr;
            }
            showRechargeOption = false;
            subjectsList = {};
            for(var books of libraryBooks){
                if(!data.hasOwnProperty("validityDateOfUser")&&(books.bookType == "bookgpt"||books.bookType=="ebookwithai")){
                    showRechargeOption = true;
                }
                var subject = books.subject;
                //if the subject is in the subjectsList, then add the subject to the subject list
                if(subject!=null&&subject!="null"&&subject!=""){
                    if(!subjectsList.hasOwnProperty(subject)){
                        subjectsList[subject] = subject;
                    }
                }
                //remove spaces from subject and make it lower case
                subject = subject.replace(/\s/g, '').toLowerCase();
                htmlStr +="<div class='col-6 col-md-4 col-lg-3 library-book fadein-animated booksbox "+subject+"'>"+
                    displayLibraryBooks(books);
                htmlStr +="</div>";

            }

            document.getElementById("show-library-books").innerHTML +=  htmlStr;
            document.getElementById('totalBooksCount').innerHTML = "Books <p class='pl-2'>(" + booksCount + ")</p>"



            uncoverBookUI();
            $("#loadMoreButton button i").removeClass('spinner-border spinner-border-sm').addClass('mr-2');
            if(booksCount >= 30) {
                $("#loadMoreButton").show();
            } else {
                $("#loadMoreButton").hide();
            }
            booksReceived = true;
            if(tagsReceived && booksReceived&&firstTimeLoad) triggerSyllabusChange();
            else {
                if (firstTimeSyllabusChangeCalled) {
                    firstTimeSyllabusChangeCalled = false;
                        //check if the grade cookie is set and if the grade is present in the grade list
                        let selectedGrade = getCookie("selectedGrade");
                        if (selectedGrade != null&&selectedGrade!="") {
                            let gradeFound = false;
                            for (let i = 0; i < gradeSelector.options.length; i++) {
                                if (gradeSelector.options[i].value == selectedGrade) {
                                    gradeSelector.selectedIndex = i;
                                    gradeFound = true;
                                    //call the grade change handler
                                    gradeChangeHandler();
                                    break;
                                }
                            }
                            if (!gradeFound) {
                                document.cookie = "selectedGrade=; expires=Thu, 01 Jan 1970 00:00:00 UTC";
                            }
                        }

                }
            }
            if(gradeSelector.selectedIndex>0){
                //sort the subject list alphabetically
                let keys = Object.keys(subjectsList);
                keys.sort();
                let sortedSubjectsList = {};
                for(let key of keys) {
                    sortedSubjectsList[key] = subjectsList[key];
                }
                subjectsList = sortedSubjectsList;
                let subjectSelector = document.getElementById('subjectFilter');
                subjectSelector.options.length = 1;
                for(let subject in subjectsList) {
                    const subjectValues = subject;
                    const element = document.createElement('option');
                    element.textContent = subjectValues;
                    element.value = subjectValues;
                    subjectSelector.appendChild(element);
                }
                //show the subject filter
                $('#subjectFilter').show();

                //remove the disabled attribute from the subject filter
                $('#subjectFilter').removeAttr('disabled');
            }
        }
    }

    // PAGINATION OF BOOKS - ON CLICK
    $("#loadMoreButton button").click(function() {
         $(this).find('i').removeClass('mr-2').addClass('spinner-border spinner-border-sm');
        pageNo = pageNo + 1;
        if(showInsBooks) getInstituteBooksList(pageNo, instituteId, batchId, allBooksLoaded, filter, freeBooks, paidBooks, syllabusSelected, gradeSelected,testSeries);
        else getUserBooksList(pageNo, allBooksLoaded);
    });

    // DELETE BOOK POPUP
    function removeFromLibrary(bookId) {
        deleteBookId = bookId;
        $('#deleteBook').modal('show');
    }

    // DELETE BOOK FROM LIBRARY
    function bookDelete() {
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <g:remoteFunction controller="log" action="removeBookFromLibrary" params="'bookId='+deleteBookId" onSuccess="bookRemoved(data);"/>
    }

    // AFTER DELETED
    function bookRemoved(data) {
        if (data.status == "Deleted") {
            $('#deleteBook').modal('hide');
            $('.loading-icon').removeClass('hidden');
            location.reload();
        }
    }

    // RETURN BOOK FROM CURRENTLY READING
    function returnBookFromUser (bookId,returnBatchId) {
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <g:remoteFunction controller="wsLibrary" action="returnBookFromUser" params="'bookId='+bookId+'&batchId='+returnBatchId" onSuccess="bookReturnedResp(data)"/>
    }

    function bookReturnedResp(data) {
        if(data.status == "OK"){
            if (!prepjoySite){
                $('.loading-icon').addClass('hidden');
            }else{
                $('#loading').hide();
            }
            $("#bookQueueModal").modal("show");
            $("#bookReturned").show();
            $("#bookValidity, #bookAddedQueue, #bookRemovedQueue").hide();
            $("#bookQueueModal").on("hidden.bs.modal", function () {
                $('.loading-icon').removeClass('hidden');
                location.reload();
            });
        }
    }

    // MAIN CONTROLLER FOR LIBRARY
    function displayLibraryBooks(books) {
        var bookTitle = books.title || books.bookTitle;
        var bookId = books.id || books.bookId;
        var coverImage = books.coverImage;
        var publisherName = books.publisher;
        var level = books.level;
        var syllabus = books.syllabus;
        var grade = books.grade;
        var bookPrice = books.price;
        var batchId = books.batchId;
        var institutePresent = books.instituteId;
        var deleteBook = '';
        var booksTag = books.bookType;
        var booksTagStr = '';
        var externalLink=books.externalLink;


        //Replacing URL
        if(level != "") {
            if ("School" === level){
                urlTag = replaceAll(syllabus?syllabus:'',' ', '-').toLowerCase();
            } else {
                urlTag = replaceAll(grade?grade:'',' ', '-').toLowerCase();
            }
        } else {
            urlTag = "book";
        }
        var bookUrl = "/" + replaceAll(bookTitle,' ','-').toLowerCase() + "/ebook?bookId=" + bookId + "&siteName="+siteName;
        if("bookgpt"==booksTag||"ebookwithai"==booksTag){
            <%if(isInstructor){%>
            bookUrl = "/wpmain/bookai?siteName=${session["siteName"]}&bookId="+bookId
            <%}else{%>
            bookUrl = "/prompt/bookgpt?siteName=${session["siteName"]}&bookId="+bookId
            <%}%>
        }
        if(queueBooksPresent) {
            bookUrl = "";
        }
        var bookImageSrc = ""
        if (coverImage!=null && coverImage.startsWith("https")) {
            bookImageSrc = coverImage
            bookImageSrc = coverImage.replace("~", ":");
        } else {
            bookImageSrc = "/funlearn/showProfileImage?id="+bookId+"&fileName="+coverImage+"&type=books&imgType=passport";
        }
        //Cover image
        var coverImageStr = "<div class=\"uncover\"><p>"+bookTitle+"</p></div>\n";
        if(coverImage !='' && coverImage != null && coverImage != 'null'){
            if(coverImage.startsWith("https")) {
                coverImageStr =  "<img src='" +coverImage.replace("~", ":")+ "'>";
            }
             else coverImageStr ="<img src='" + bookImageSrc + "'>";
        }

        //Books type tag
        if (booksTag == "print") {
            booksTagStr = "Print Book";
        } else if(booksTag == "ebook"){
            booksTagStr = "eBook";
        }else if(booksTag =="testseries"){
            booksTagStr = "Test Series";
        }else if(booksTag =="onlinecourse") {
            booksTagStr = "Online Course";
        }else if(booksTag =="liveclasses"){
            booksTagStr = "Live Classes";
        }else if(booksTag =="mcqsebook"){
            booksTagStr = "MCQs eBook";
        }else if(booksTag =="previouspapers"){
            booksTagStr = "Previous Papers";
        }else if(booksTag =="bookgpt"||booksTag =="bookGPT") {
            booksTagStr = "iBookGPT";
        }
        else if(booksTag =="ebookwithai"){
                booksTagStr = "eBook (with AI Doubts Solver)";
            }
        else{
            booksTagStr = "eBook";
        }

        var template = "";
        template += bookUILayer(bookId,bookTitle,bookUrl,coverImageStr,publisherName,deleteBook,batchId,institutePresent,booksTagStr,externalLink,booksTag);
        return template;
    }

    // MAIN UI TEMPLATE
    function bookUILayer(bookId,bookTitle,bookUrl,coverImageStr,publisherName,deleteBook,batchId,instituteId,booksTagStr,externalLink,booksTag) {
        if(batchId!="undefined" && batchId!=undefined && batchId!=''){
            if(userLibraryValidityExpired)
                bookUrl = 'javascript:showValidityExpiredMessage();';
            else if(externalLink!=null&&externalLink!='null'&&externalLink!='')
                bookUrl = externalLink;
             else   bookUrl = 'javascript:addBookToUserByUser('+bookId+',"'+batchId+'","'+urlTag+'","'+ replaceAll(bookTitle,' ','-').toLowerCase()+'","'+booksTag+'");';
        }
        else if(batchId!="undefined" && batchId!=undefined && batchId!='' && institutePresent !='undefined' && institutePresent!=undefined && institutePresent!=''){
            bookUrl = "/" + replaceAll(bookTitle,' ','-').toLowerCase() + "/ebook?bookId=" + bookId +"&instituteId=" + instituteId + "&batchId=" + batchId + "&siteName="+siteName;
        }
        var bookUItemplate = "<div class=\"card\">\n" ;
        if(externalLink!=null&&externalLink!='null'&&externalLink!='')
        bookUItemplate += "<a class=\"lib-showcase library_book\" href='" + bookUrl + "' target='_blank'>\n"
        else
        bookUItemplate += "<a class=\"lib-showcase library_book\" href='" + bookUrl + "'>\n"

        bookUItemplate +=     coverImageStr;
        <%if(!"3".equals(""+session["siteId"])){%>
        bookUItemplate +="<h3 class='book-tag'>"+ booksTagStr +"</h3>";
        <%}%>
        bookUItemplate +="</a>\n" +
            "<div class=\"card-body d-flex justify-content-between\">\n";
        if(externalLink!=null&&externalLink!='null'&&externalLink!='')
            bookUItemplate += "<a class=\"card-text library_book\" href='" + bookUrl + "' target='_blank'>" + bookTitle + "</a>\n"
        else
            bookUItemplate += "<a class=\"card-text library_book\" href='" + bookUrl + "'>" + bookTitle + "</a>\n"

        bookUItemplate +=  "</div>" +
            "<small class=\"book-publisher-name\">" + publisherName + "</small>\n"
            + deleteBook +
            "</div>\n";
        return bookUItemplate;

    }

    // EMPTY UI MESSAGE
    function noBooksUILayer() {
        <%if(hasDriveAccess){%>
        $("#copilot").hide();
        <%}%>
        <%if(hasRaAccess){%>
        $("#aira").hide();
        <%}%>
        <%if(showReferenceSection){%>
        $("#referenceSection").hide();
        <%}%>
        <%if(!"Yes".equals(""+session["disableStore"])){%>
        var htmlStr = "<div class='no-books-available mx-auto'>" +
            "<p class='text-center pt-5'>There's always a new beginning.<br><strong>Check some interesting books.</strong></p>";
            <%if(!"25".equals(""+session["siteId"])){%>
            htmlStr += "<div class='text-center'>";
        <%if("true".equals(session["commonWhiteLabel"])){%>
        htmlStr += "<a href='/sp/${session['siteName']}/store' class='btn btn-lg btn-warning btn-warning-modifier btn-shadow click-here-link mt-3 px-4 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'>Browse Books</a>";
        <%}else{%>
        htmlStr += "<a href='/${session['entryController']}/store' class='btn btn-lg btn-warning btn-warning-modifier btn-shadow click-here-link mt-3 px-4 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'>Browse Books</a>";
        <%}%>
        htmlStr += "</div>";
            <%}%>
            htmlStr += "</div>";
        return htmlStr;
        <%}else{%>
        return "";
        <%}%>
    }

    // COLOR FOR NO COVER IMAGE BOOKS
    function uncoverBookUI() {
        var uncover = document.querySelectorAll(".uncover");
        var colors = ['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
        for(var i = 0 ; i < uncover.length; i++) {
            uncover[i].style.background = colors[i%11];
        }
    }

    //ADD BOOK TO USER
    function addBookToUserByUser(bookId,batchId,urltag,bookTitle,bookType){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        if(bookType === "bookGPT"||bookType === "bookgpt"||bookType === "ebookwithai") {
            <%if(isInstructor){%>
            window.location.href = "/wpmain/bookai?bookId=" + bookId + "&siteName=" + siteName;
            <%}else{%>
            window.location.href = "/prompt/bookgpt?bookId=" + bookId + "&siteName=" + siteName;
            <%}%>
        }
        else window.location.href = "/" +  replaceAll(bookTitle,' ','-').toLowerCase() + "/ebook?bookId=" + bookId +"&batchId=" + batchId + "&siteName="+siteName;
    }


    function bookValidityDate(data) {
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        if (data.message == "added") {
            window.location.href =  "/" + bookTitledetails + "/ebook?bookId=" + bookQueueId + "&siteName="+siteName;
        } else if(data.message == "addToQueue") {
            document.getElementById("bookValidityModalText").innerHTML = "#" + data.queuePosition;
            if (data.booksQueueDtl == "Already Exist") {
                $("#bookQueueModal").modal("show");
                $("#bookAddedQueue, #bookExistIcon").show();
                document.getElementById("checkAddedQueue").innerText = "This eBook is already added in waiting list.";
                $("#bookValidity, #bookReturned, #bookRemovedQueue, #bookAddedIcon").hide();
            } else {
                $("#bookQueueModal").modal("show");
                $("#bookValidity").show();
                $("#bookAddedQueue, #bookReturned, #bookRemovedQueue").hide();
            }
        }
    }

    // ADD BOOK TO WAITING LIST
    function addBookToQueue() {
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <g:remoteFunction controller="wsLibrary" action="addInstituteBookToUserQueue"  onSuccess='bookAddedToQueue(data);' params="'batchId='+batchIds+'&bookId='+bookQueueId+'&instituteId='+instituteId"/>
    }

    function bookAddedToQueue(data){
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        $("#bookQueueModal").modal("show");
        $("#bookAddedQueue, #bookAddedIcon").show();
        document.getElementById("checkAddedQueue").innerText = "This eBook is added to your waiting list.";
        $("#bookValidity, #bookReturned, #bookRemovedQueue, #bookExistIcon").hide();
        if(data.status=="Ok") {
            $("#bookQueueModal").modal("hide");
        }
    }

    // REMOVE BOOK FROM WAITING LIST
    function removeBookFromUserQueue(bookId,batchId) {
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <g:remoteFunction controller="wsLibrary" action="removeBookFromUserQueue" params="'bookId='+bookId+'&batchId='+batchId" onSuccess="bookRemovedResp(data)"/>
    }

    function bookRemovedResp(data) {
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        redirectToQueueBooks = true;
        if(data.status=='OK') {
            $("#bookQueueModal").modal("show");
            $("#bookRemovedQueue").show();
            $("#bookValidity, #bookAddedQueue, #bookReturned").hide();
            $("#bookQueueModal").on("hidden.bs.modal", function () {
                if(redirectToQueueBooks) openQueueList();
            });
        }
    }

    // ONCLICK INSTITUTE TABS
    function getInsLibraryBooks(insId, insBatchId,onlyClass) {
        showInsBooks = true;
        allBooksLoaded = filter = false;
        pageNo = 1;
        instituteId = institutePresent = insId;
        batchId = insBatchId;
        freeBooks = paidBooks = syllabusSelected = gradeSelected = testSeries=null;
        document.getElementById("show-library-books").innerHTML = "";
        document.getElementById('totalBooksCount').innerHTML = "";
         firstTimeLoad= true;
         firstTimeSyllabusChangeCalled=false;
         tagsReceived = false;
         booksReceived = false;

           //DYNAMICALLY ADDING INSTITUTE CLASSES
           var classOptions="<option value=''>Select Class</option>";
           var defaultCount = 0;
           var classCount = 0;
           <%if(institutes!=null){%>
           <g:each in="${institutes}" var="institute" status="i">
            <%if("${institute.batchName}"=="Default"){%>
                defaultCount++;
            <%}%>

           <%if("${institute.batchName}"!="Default"){%>
           if ('${institute.id}' == instituteId){
               classOptions += '<option value="${institute.id}_${institute.batchId}">${institute.batchName}</option>';
               $('#insClassSelect').html(classOptions).show();
               classCount++;
           }else if(classCount==0){
               $('#insClassSelect').hide();
           }
           <%}else{%>
                if(classCount==0) $('#insClassSelect').hide();
           <%}%>
           </g:each>
           <%}%>

        if (!onlyClass){
            getInstituteBooksList(pageNo, instituteId, batchId, allBooksLoaded, filter, freeBooks, paidBooks, syllabusSelected, gradeSelected,testSeries);
            getInstituteBooksTags(batchId);
        }else{
            <%if(institutes!=null){%>
            var classList = document.querySelectorAll('#insClassSelect option');
            if(document.getElementById("insClassSelect").selectedIndex == 0) {
                $("#insClassSelect option[value="+classList[1].value+"]").prop('selected', true);
                getInsClassBooks();
            }
            <%}%>
        }

        $("#instituteClasses, #checkWaitingListBtn").show();
        $('#libraryFilter a').removeClass('selected');
        getInstituteLastReadBooks(insBatchId);

        let url = new URL(window.location.href);
        let params = new URLSearchParams(url.search.slice(1));
        params.delete('mode');
        params.set('instituteId', instituteId);
        window.history.replaceState({}, '', window.location.pathname + "?" + params + "" + window.location.hash);
    }

    // ONCLICK INSTITUTE TABS
    function getInsClassLibraryBooks(insId, insBatchId,onlyClass) {
        showInsBooks = true;
        allBooksLoaded = filter = false;
        pageNo = 1;
        instituteId = insId;
        batchId = insBatchId;
        freeBooks = paidBooks = syllabusSelected = gradeSelected = testSeries=null;
        document.getElementById("show-library-books").innerHTML = "";
        document.getElementById('totalBooksCount').innerHTML = "";
        if (!onlyClass){
            getInstituteBooksList(pageNo, instituteId, batchId, allBooksLoaded, filter, freeBooks, paidBooks, syllabusSelected, gradeSelected,testSeries);
            getInstituteBooksTags(batchId);
        }else{
            <%if(institutes!=null){%>
            getInsClassBooks();
            <%}%>
        }
        $("#instituteClasses, #checkWaitingListBtn").show();
        $('#libraryFilter a').removeClass('selected');
    }

    // ONCLICK MY SELF BOOK TAB
    function getMySelfBooks() {
        pageNo = 1;
        showInsBooks = allBooksLoaded = lastReadBookPresent = false;
        document.getElementById("show-library-books").innerHTML = "";
        document.getElementById("currently-reading-books").innerHTML = "";
        getUserBooksList(pageNo, allBooksLoaded);
        $("#instituteClasses, #libraryFilter, #checkWaitingListBtn").hide();
        $('#libraryFilter a').removeClass('selected');
        $('#resetLibraryFilter').attr('disabled', 'disabled');

        let url = new URL(window.location.href);
        let params = new URLSearchParams(url.search.slice(1));
        params.delete('instituteId');
        params.set('mode', 'mybooks');
        window.history.replaceState({}, '', window.location.pathname + "?" + params + "" + window.location.hash);

    }

    function resetLibraryFilters() {
        $('#resetLibraryFilter').attr('disabled', 'disabled');
        $("#loadMoreButton").hide();
        document.querySelector(".quick-search").classList.replace("show", "hide");
        document.querySelector("#librarySearchEmpty").setAttribute("style", "display: none");
        //reset the syllabus and grade cookies
        document.cookie = "selectedSyllabus=; expires=Thu, 01 Jan 1970 00:00:00 UTC";
        document.cookie = "selectedGrade=; expires=Thu, 01 Jan 1970 00:00:00 UTC";
        $('#gradeFilter').hide();
        $('#subjectFilter').hide();
        getInsClassLibraryBooks(instituteId, batchId);
    }

    if(institutePresent != 'undefined' && institutePresent != undefined && institutePresent != '') {
        if(document.getElementById("inst-"+institutePresent)){
            document.getElementById("inst-"+institutePresent).click();
            $("#inst-"+institutePresent).addClass('current').siblings().removeClass('current');
        }

    } else {
        <%if(!"25".equals(""+session["siteId"])){%>
        getMySelfBooks();
        $('#myEbooks').addClass('current').siblings().removeClass('current');
        <%} else {%>
        $("#libraryTabs a:nth-child(1) span").trigger("click");
        $("#libraryTabs a:nth-child(1)").addClass('current').siblings().removeClass('current');
        <%}%>
    }

    $(document).ready(function () {
        $("#libraryTabs a").on("click", function(){
            $("#libraryTabs a.current").removeClass("current");
            $(this).addClass("current");
        });
    });

    function showValidityExpiredMessage(){
        alert("Your library membership is expired. Kindly contact your librarian to renew");
    }

    function showCopilot(){
        $("#booksSection").hide();
        loadFolderData('');
        <%if(hasRaAccess){%>
        $("#aira").hide();
        <%}%>
        $("#copilot").show();
    }

    function showAIRA(){
        $("#booksSection").hide();
        <%if(hasDriveAccess){%>
        $("#copilot").hide();
        <%}%>
        <%if(showReferenceSection){%>
        $("#referenceSection").hide();
        <%}%>
        loadDashboardData();
        $("#aira").show();
    }



    function showReferenceSection(){
        $("#booksSection").hide();
        <%if(hasDriveAccess){%>
        $("#copilot").hide();
        <%}%>
        <%if(hasRaAccess){%>
        $("#aira").hide();
        <%}%>
       loadCategories();
    }

    function showDashboard() {
        window.location.href = "/aireport/dashboard";
    }

    function filterBooksBySubject() {
        var bookCount = 0;
        var subject = document.getElementById('subjectFilter').value;
        //remove spaces and make it lower case
        subject = subject.replace(/\s/g, '').toLowerCase();
        // Select all divs with the class 'booksbox'
        let books = document.querySelectorAll('.booksbox');

        // First, remove 'hidden' from all divs
        books.forEach(book => book.removeAttribute('hidden'));

        // Then, hide those that do not match the given subject
        books.forEach(book => {
            if (!book.classList.contains(subject)) {
                book.setAttribute('hidden', 'true');
            }else {
                bookCount++;
            }
        });
        // assign the book count to the total books count
        document.getElementById('totalBooksCount').innerHTML = "Books <p class='pl-2'>(" + bookCount + ")</p>";
    }

</script>
