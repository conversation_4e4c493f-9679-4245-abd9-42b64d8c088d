<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="/assets/bookgpt/bookcreation.css">
    <title></title>
</head>
<body>
    <div class="container">
        <div>
            <div style="display: grid;grid-template-columns: repeat(2,1fr)">
                <div class="chaptersSelection">
                    <h4>Select Chapters </h4>
                    <label><input type="checkbox" id="selectAll"> Select All</label>
                    <div class="chapterCheckBoxes" id="chapterCheckBoxes"></div>
                </div>
                <div>
                    <h4>Select Resource Type</h4>
                    <div>
                        <label><input type="checkbox" id="type_chapter_snapshot">Chapter Snapshot</label>
                        <label><input type="checkbox" id="type_mcq">MCQs</label>
                        <label><input type="checkbox" id="type_qna">Q&A</label>
                        <label><input type="checkbox" id="type_tricks">Tips & Tricks</label>
                    </div>
                    <div class="btnWrap">
                        <button class="startBtn">Start Creation</button>
                        <button class="resetBtn" disabled>Reset</button>
                        <button class="downloadBtn" disabled>Download PDF</button>
                    </div>
                </div>
            </div>

            <div id="progress-status"></div>
        </div>
       <div class="divider"></div>
        <div id="progress-container">
            <div id="progress-bar"></div>
        </div>
        <article>
            <section id="book_title"></section>
            <section>
                <h2 style="font-size: 1.6rem !important;display: none" id="toc">Table of Content</h2>
                <ul id="book_toc"></ul>
            </section>
        </article>
    </div>
<div id="printableDiv"></div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
</body>

<script>
    const bookId = "${params.bookId}"
    const downloadBtn = document.querySelector('.downloadBtn')
    const resetBtn = document.querySelector('.resetBtn')
    const toc = document.getElementById('toc')
    let bookTempTitle = "";
    let bookData = []


    const createBookUIHandler = (data, typeObj)=>{
        const chapterNames = data.chapterDetails.map((chap)=>chap.chapterName)
        createBookTitle(data.bookTitle)
        createBookToc(chapterNames)
        createChapterContent(data.chapterDetails, typeObj)
    }
    const createBookTitle = (title)=>{
        const book_title = document.getElementById('book_title')
        const h1 = document.createElement('h1')
        bookTempTitle = title
        h1.textContent = title
        h1.style.fontSize = "2rem !important"
        h1.style.textAlign = "center"
        h1.classList.add('title')
        book_title.appendChild(h1)
        document.querySelector('title').textContent = title
    }
    const createBookToc = (chapters)=>{
        const book_toc = document.getElementById('book_toc')
        chapters.forEach((chapter,index)=>{
            const li = document.createElement('li')
            const anchor = document.createElement('a')
            anchor.href = "#chapter_"+(index+1)
            anchor.textContent = chapter
            li.appendChild(anchor)
            book_toc.appendChild(li)
        })
    }

    const snapshotUIHandler = (gptContents)=>{
        const snapshotSection = document.createElement('section')
        const snapshotdiv = document.createElement('div')
        snapshotdiv.innerHTML = gptContents
        renderMathInElement(snapshotdiv, {
            delimiters: [
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true }
            ],
            throwOnError : false
        });
        snapshotdiv.innerHTML = marked.parse(snapshotdiv.innerHTML)
        snapshotSection.appendChild(snapshotdiv)
        return snapshotSection
    }

    const tricksUIHandler = (gptContents)=>{
        const snapshotSection = document.createElement('section')
        const snapshotdiv = document.createElement('div')

        snapshotdiv.innerHTML = gptContents
        renderMathInElement(snapshotdiv, {
            delimiters: [
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true }
            ],
            throwOnError : false
        });
        snapshotdiv.innerHTML = marked.parse(snapshotdiv.innerHTML)
        snapshotSection.appendChild(snapshotdiv)
        return snapshotSection
    }

    const mcqUIHandler = (mcqs)=>{
        const mcqSection = document.createElement('section')
        const mcqTitle = document.createElement('h3')
        mcqTitle.textContent = "Multiple Choice Questions ("+mcqs.length+")"
        mcqSection.appendChild(mcqTitle)
        mcqs.forEach((mcq,index)=>{
            const mcqDiv = document.createElement('div')
            const ques = document.createElement('p')
            const bold = document.createElement('strong')
            bold.textContent = (index+1)+". "+mcq.question
            ques.appendChild(bold)
            mcqDiv.appendChild(ques)

            const option1 = document.createElement('p')
            const option2 = document.createElement('p')
            const option3 = document.createElement('p')
            const option4 = document.createElement('p')
            option1.textContent = "A. "+mcq.option1
            option2.textContent = "B. "+mcq.option2
            option3.textContent = "C. "+mcq.option3
            option4.textContent = "D. "+mcq.option4

            mcqDiv.appendChild(option1)
            mcqDiv.appendChild(option2)
            mcqDiv.appendChild(option3)
            mcqDiv.appendChild(option4)
            const options = [mcq.option1, mcq.option2, mcq.option3, mcq.option4];
            const answers = [mcq.answer1, mcq.answer2, mcq.answer3, mcq.answer4];
            let correctAnswer
            for (let i = 0; i < answers.length; i++) {
                if (answers[i] === "Yes") {
                    correctAnswer = options[i];
                    break;
                }
            }
            const br = document.createElement('br')
            mcqDiv.appendChild(br)

            const answer = document.createElement('p')
            const answerDescription = document.createElement('p')
            const difficultyLevel = document.createElement('p')
            answer.innerHTML = "<strong>Answer: </strong>"+correctAnswer
            answer.innerHTML = "<strong>Difficulty: </strong>"+mcq.difficultyLevel
            answerDescription.innerHTML = "<strong>Explanation: </strong>"+mcq.answerDescription
            mcqDiv.appendChild(answer)
            mcqDiv.appendChild(br)
            mcqDiv.appendChild(answerDescription)
            mcqDiv.appendChild(br)
            mcqDiv.appendChild(difficultyLevel)
            const hr = document.createElement('hr')
            mcqDiv.appendChild(hr)
            renderMathInElement(mcqDiv, {
                delimiters: [
                    { left: "\\(", right: "\\)", display: false },
                    { left: "\\[", right: "\\]", display: true }
                ],
                throwOnError : false
            });
            mcqDiv.innerHTML =  marked.parse(mcqDiv.innerHTML)
            mcqSection.appendChild(mcqDiv)
        })
        return mcqSection
    }

    const qnaUIHandler = (qna)=>{
        const qaSection = document.createElement('section')
        const qaTitle = document.createElement('h3')
        qaTitle.textContent = "Question and Answers ("+qna.length+")"
        qaSection.appendChild(qaTitle)
        qna.forEach((qa,index)=>{
            const qaDiv = document.createElement('div')
            const ques = document.createElement('p')
            const ans = document.createElement('p')
            const diff = document.createElement('p')
            const bold = document.createElement('strong')
            bold.textContent = (index+1)+". "+qa.question
            ans.innerHTML = "<strong>Answer : </strong>"+qa.answer
            diff.innerHTML = "<strong>Difficulty : </strong>"+qa.difficultyLevel
            ques.appendChild(bold)
            qaDiv.appendChild(ques)
            const br = document.createElement('br')
            qaDiv.appendChild(ans)
            qaDiv.appendChild(diff)
            const hr = document.createElement('hr')
            qaDiv.appendChild(hr)
            renderMathInElement(qaDiv, {
                delimiters: [
                    { left: "\\(", right: "\\)", display: false },
                    { left: "\\[", right: "\\]", display: true }
                ],
                throwOnError : false
            });
            qaDiv.innerHTML =  marked.parse(qaDiv.innerHTML)
            qaSection.appendChild(qaDiv)
        })
        return qaSection
    }

    const getChapterSnapshot = async (resId)=>{
        const res = await fetch("/prompt/getGPTsForResource?resId="+resId+"&promptType=chapter_snapshot")
        if(!res.ok){
            console.error("Data is not valid")
            return false
        }
        const data = await res.json()
        return  data
    }

    const getChapterTricks = async (resId)=>{
        const res = await fetch("/prompt/getGPTsForResource?resId="+resId+"&promptType=tricks")
        if(!res.ok){
            console.error("Data is not valid")
            return false
        }
        const data = await res.json()
        return  data
    }

    const getChapterQNA = async (resId)=>{
        const res = await fetch("/prompt/createTest?readingMaterialResId="+resId)
        if(!res.ok){
            console.error("Data is not valid")
            return false
        }
        const data = await res.json()
        return  data
    }

    const createChapterContent = async (chapterDetails, typeObj)=>{
        const article = document.querySelector('article')
        const totalChapters = chapterDetails.length;

        for(let i=0;i<chapterDetails.length;i++){

            const chapterSection = document.createElement('section')
            chapterSection.id = "chapter_"+(i+1)

            const chapterTitle = document.createElement('h2')
            chapterTitle.textContent = "Chapter "+(i+1)+": "+chapterDetails[i].chapterName
            chapterSection.appendChild(chapterTitle)

            if(typeObj.snap){
                const snapshotData = await getChapterSnapshot(chapterDetails[i].resId);
                if(snapshotData){
                    const snapShotContent = snapshotUIHandler(snapshotData.answer);
                    chapterSection.appendChild(snapShotContent)
                }
            }

            if(typeObj.tricks){
                const tricksData = await getChapterTricks(chapterDetails[i].resId);
                if(tricksData){
                    const tricksContent = tricksUIHandler(tricksData.answer);
                    chapterSection.appendChild(tricksContent)
                }
            }


            const qnaData = await getChapterQNA(chapterDetails[i].resId);
            if(qnaData.qna && qnaData.qna.length>0 && typeObj.qna){
                const qnaContent = qnaUIHandler(qnaData.qna)
                chapterSection.appendChild(qnaContent)
            }

            if(qnaData.mcqs && qnaData.mcqs.length>0 && typeObj.mcqs){
                const mcqContent = mcqUIHandler(qnaData.mcqs)
                chapterSection.appendChild(mcqContent)
            }

            article.appendChild(chapterSection)
            const progress = ((i + 1) / totalChapters) * 100;
            const progressBar = document.getElementById('progress-bar');
            progressBar.title = Math.floor(progress) + '%';
            progressBar.style.width = progress + '%';
            if(progress==100){
                document.getElementById('progress-status').textContent = "Book creation is complete!"
                document.getElementById('progress-status').style.display = 'flex'
                downloadBtn.removeAttribute('disabled')
                resetBtn.removeAttribute('disabled')
                setTimeout(()=>{
                    document.getElementById('progress-status').textContent = ""
                    document.getElementById('progress-status').style.display = 'none'
                },4000)
            }
        }
    }
    const printDoc = ()=>{
        const answerPrint = document.querySelector('article');
        const clonedAnswer = answerPrint.cloneNode(true);

        const newWindow = window.open('', '', 'height=600,width=800');
        const externalCssUrl = '/assets/bookgpt/bookcreation.css';
        newWindow.document.write('<html><head>' +
            '<link rel="stylesheet" type="text/css" href="' + externalCssUrl + '">' +
            '<title>'+bookTempTitle+'</title>');

        newWindow.document.write('</head><body>');
        newWindow.document.write(clonedAnswer.outerHTML);

        newWindow.document.write('</body></html>');
        newWindow.document.close();

        newWindow.focus();
        newWindow.print();
    }


    const startCreation = async ()=>{
        const checkboxes = document.querySelectorAll('.option');
        let selected = [];
        checkboxes.forEach((checkbox) => {
            if (checkbox.checked) {
                selected.push(checkbox.value);
            }
        });
        const type_snap = document.getElementById('type_chapter_snapshot').checked
        const type_mcqs = document.getElementById('type_mcq').checked
        const type_qna = document.getElementById('type_qna').checked
        const type_tricks = document.getElementById('type_tricks').checked
        const typeObj = {
            snap:type_snap,
            mcqs:type_mcqs,
            qna:type_qna,
            tricks:type_tricks
        }
        bookData.chapterDetails = bookData.chapterDetails.filter(resource => selected.includes(resource.chapterId.toString()));
        document.getElementById('progress-container').style.display = 'block'
        await createBookUIHandler(bookData, typeObj)
        toc.style.display = "block"
    }

    const resetBook = ()=>{
        const newarticle = document.querySelector('article')
        newarticle.innerHTML = ""
        const titleSec = document.createElement('section')
        const tocSec = document.createElement('section')
        const tocTitle = document.createElement('h2')
        const tocList = document.createElement('ul')
        tocTitle.textContent = 'Table of Content'
        tocTitle.id = 'toc'
        tocTitle.setAttribute('style','font-size: 1.6rem !important;display:none')
        titleSec.id = 'book_title'
        tocList.id = 'book_toc'
        tocSec.appendChild(tocTitle)
        tocSec.appendChild(tocList)
        newarticle.appendChild(titleSec)
        newarticle.appendChild(tocSec)
        const progressBar = document.getElementById('progress-bar');
        progressBar.title = 0+ '%';
        progressBar.style.width = 0+ '%';
    }
    const selectAllChapters = ()=>{
        const checkboxes = document.querySelectorAll('.option');
        checkboxes.forEach(check=>{
            check.checked = !check.checked
        });
    }

    const prepareChapters = async (data)=>{
        bookData = data
        const chapterCheckBoxes = document.getElementById('chapterCheckBoxes')
        const startBtn = document.querySelector('.startBtn')
        data.chapterDetails.forEach(chap=>{
            const input = document.createElement('input')
            const wrap = document.createElement('div')
            const span = document.createElement('span')
            wrap.style.marginBottom="10px"
            input.type = 'checkbox';
            input.value = chap.chapterId
            input.classList.add('option')
            span.textContent = chap.chapterName
            wrap.appendChild(input)
            wrap.appendChild(span)
            chapterCheckBoxes.appendChild(wrap)
        })
        document.getElementById('selectAll').addEventListener('change',selectAllChapters);
        startBtn.addEventListener('click', startCreation)
    }

    const getBookDtl = async()=>{
        const res = await fetch('/autogpt/getChapterDetails?bookId='+bookId)
        const data = await res.json()
        await prepareChapters(data)
        await downloadBtn.addEventListener('click', printDoc)
        await resetBtn.addEventListener('click', resetBook)
    }
    getBookDtl()

</script>
</html>