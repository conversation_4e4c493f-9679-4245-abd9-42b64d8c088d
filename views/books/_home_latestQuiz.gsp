
<div class="card card-modifier border-0 home-welcome home-report latest-quiz-area mt-3">
    <div class="card-body p-3">
        <div class="d-flex justify-content-between retest-hyperlink">
            <p class="card-text">Recently attempted test</p>
        </div>
        <div class="d-flex justify-content-between pt-2">
            <div class="quiz-score">
                <h6 id="quizStatistics"></h6>
            </div>
            <div class="quiz-details retest-hyperlink col">
                <h6 id="quizName"></h6>
                <a href="javascript:openQuiz();" class="btn btn-sm btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect">Test Details</a>
            </div>
        </div>
    </div>
</div>

<script>
    var lastQuizInfo = JSON.parse("${lastQuizInfo}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    document.getElementById("quizStatistics").innerHTML=lastQuizInfo[0].points+"<small>/"+lastQuizInfo[0].noOfQuestions+"</small>";
    document.getElementById("quizName").innerHTML=lastQuizInfo[0].quizName;
    var quizLink = "/prepjoy/prepJoyGame?siteId=${session["sessionId"]}&quizRecId="+lastQuizInfo[0].quizRecId+"&historyPage=true&learn=false";

    function openQuiz(){
        setTimeout(() => {
            window.open(quizLink, '_blank');
        });
    }

</script>
