<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<style>
body {
    padding-top: 20px;
}
/* Additional styling as needed */
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Page Interactions</h3>
                    <div class="container">
                        <h5 class="my-4">Interactions on <a href="${webpageUrl}" target="_blank">${webpageUrl}</a></h5>

                        <div id="interactionContainer" class="card">
                            <div class="card-body">
                                <!-- Interactions will be loaded here via AJAX -->
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/highlight.min.js"></script>

<script type="text/javascript">
    $(document).ready(function() {
        var offset = 0;
        var max = 10;
        var hasMore = true;

        // Load initial interactions
        loadInteractions();

        // Infinite scrolling
        $(window).on('scroll', function() {
            if (!hasMore) return;
            if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
                loadInteractions();
            }
        });

        function loadInteractions() {
            $('#loading').show();
            $.ajax({
                url: '${createLink(controller: 'webInteraction', action: 'loadPageInteractions')}',
                data: {
                    webpageUrl: '${webpageUrl}',
                    offset: offset,
                    max: max
                },
                success: function(data) {
                    // Append interactions to the container
                    appendInteractions(data.interactions);

                    // Update offset
                    offset += max;

                    // Hide loading indicator
                    $('#loading').hide();

                    // Check if more interactions are available
                    if (data.interactions.length < max) {
                        hasMore = false;
                    }
                },
                error: function() {
                    $('#loading').hide();
                    alert('An error occurred while loading interactions.');
                }
            });
        }

        function appendInteractions(interactions) {
            interactions.forEach(function(interaction) {
                var userQueryDiv = $('<div>', { class: 'media mb-4' });
                var userImg = $('<i>', {
                    class: 'mr-3 fa fa-user-circle',
                    alt: 'User',
                    style: 'font-size:30px; color: #007bff;'
                });

                var aiImg = $('<i>', {
                    class: 'mr-3 fa fa-robot',
                    alt: 'AI',
                    style: 'font-size:30px; color: green ;'
                });
                var userBody = $('<div>', { class: 'media-body' });
                var userHeader = $('<h5>', {
                    class: 'mt-0',
                    html: 'You <small class="text-muted">' + interaction.timestamp + '</small>'
                });
                userBody.append(userHeader);
                userBody.append($('<p>').text(interaction.userQuery));
                userQueryDiv.append(userImg);
                userQueryDiv.append(userBody);

                var aiResponseDiv = $('<div>', { class: 'media mb-4' });

                var aiBody = $('<div>', { class: 'media-body' });
                var aiHeader = $('<h5>', { class: 'mt-0', text: 'AI' });
                aiBody.append(aiHeader);
                aiBody.html(marked.parse(interaction.aiResponse))
                aiResponseDiv.append(aiImg);
                aiResponseDiv.append(aiBody);

                $('#interactionContainer .card-body').append(userQueryDiv);
                $('#interactionContainer .card-body').append(aiResponseDiv);
                $('#interactionContainer .card-body').append($('<hr/>'));
            });
        }
    });
</script>
</body>
</html>
