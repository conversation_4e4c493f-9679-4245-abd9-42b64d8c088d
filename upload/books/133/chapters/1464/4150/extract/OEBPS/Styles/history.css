body {
font-family:"Arial";
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

#prelims
{
	line-height:200%;
}
#prelims .char-style-override-21, .char-style-override-26
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#00aeef;
}
#prelims .char-style-override-2
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#00aeef;
}
* {
margin:0;
padding:0;
}

.image {
text-align:center;
}

.author {
text-align:right;
}
.chapterHeading {
font-size:160%;
margin-bottom:20px;
text-align:center;
text-transform:uppercase;

}
h3
{
color:#AA0078;
font-size:1.3em;
}
.Heading {
font-size:160%;
margin-bottom:20px;
text-align:center;
}

.chapterNumber {
	font-size: 125%;
	
	text-align:center;
}

.subHeading {
font-size:125%;
margin-bottom:20px;
text-align:center;
text-transform:uppercase;
}

.topicHeading {
font-size:115%;
margin-bottom:20px;
text-transform:uppercase;
}

.center {
	text-align: center;
	
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding:15px;
margin: 5px 5px 5px 5px;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding:15px;
margin: 5px 5px 5px 5px;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:65%;
line-height:110%;
font-weight:bold;

font-size:1.5em;

color:#fff;

}

div.chapter_pos div

{

background:#9D538E;

padding:10px;

width:34%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.lining_box
{
border:2px solid #AA0078;
padding:15px;
border-radius:15px;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
max-width:100%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:100%;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

h2
{
color:#fff;
font-size:1.5em;
background:#AA0078;
padding:10px;
}


h4
{
	color:#d1640f;
	font-size:1.3em;
	margin-top:40px;
	margin-bottom:40px;
}

.footer

{

display:none;

}

table td

{

padding:10px;

}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.cover_img_small

{

width:50%;

}
@media only screen and (max-width: 767px) {


div.chapter_pos


{

top:50%;

font-size:1em;

}

div.chapter_pos div


{

width:70%;



}

.cover_img_small

{

width:90%;

}

}
