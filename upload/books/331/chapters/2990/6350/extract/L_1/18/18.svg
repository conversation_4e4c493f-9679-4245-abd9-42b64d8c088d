<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 935 1210" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<style type="text/css"><![CDATA[
.g0_18{
fill: #B8282E;
}
.g1_18{
fill: none;
stroke: #B8282E;
stroke-width: 6.233333;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g2_18{
fill: #F0F1F0;
}
.g3_18{
fill: #FFFFFF;
}
.g4_18{
fill: #231F20;
}
.g5_18{
fill: #ECF3F2;
}
.g6_18{
fill: #D2DAD9;
}
.g7_18{
fill: #536261;
}
.g8_18{
fill: #F9F4E0;
}
.g9_18{
fill: #D5222D;
}
.g10_18{
fill: #EE2028;
}
.g11_18{
fill: #AE2930;
}
.g12_18{
fill: #BFC7C7;
}
.g13_18{
fill: #F15C5A;
}
.g14_18{
fill: #EA2E33;
}
.g15_18{
fill: none;
stroke: #231F20;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g16_18{
fill: #2E3092;
}
.g17_18{
fill: none;
stroke: #2E3092;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g18_18{
fill: none;
stroke: #231F20;
stroke-width: 0.9166667;
stroke-linecap: butt;
stroke-linejoin: miter;
}
]]></style>
</defs>
<path fill-rule="evenodd" d="M725.3,111.5H826.7v-29H725.3v29Z" class="g0_18" />
<path d="M203.7,82.5V1120.9" class="g1_18" />
<path fill-rule="evenodd" d="M134.6,181.7L117.8,107.4l-7.3,-7.8L68.8,83.1l-13,71.2l78.8,27.4Z" class="g2_18" />
<path fill-rule="evenodd" d="M133.8,178.5L117.4,109.8l-7.5,-8.3L71.4,83.5L58.5,152.7l75.3,25.8Z" class="g3_18" />
<path fill-rule="evenodd" d="M117.4,107.6l.2,.4l15.7,68.4l-.5,-.3L116.9,107.9l.2,.3l.3,-.6Z" class="g4_18" />
<path fill-rule="evenodd" d="M109.9,100l.1,.1l7.4,7.5l-.3,.7l-7.5,-7.7l.2,.2l.1,-.8Z" class="g4_18" />
<path fill-rule="evenodd" d="M70.5,83.7L109.9,100l-.1,.8L70.4,84.6l.1,-.9Z" class="g4_18" />
<path fill-rule="evenodd" d="M58.3,150.6L70.5,84l.7,.5L59.3,150.9l-1,-.3Z" class="g4_18" />
<path fill-rule="evenodd" d="M133.4,176.4L59,150.8V150l74.3,25.8l.1,.6Z" class="g4_18" />
<path fill-rule="evenodd" d="M117.2,108.1l-.4,-.4l-.5,-.6l-.5,-.5l-.4,-.4l-.5,-.5l-.4,-.4l-.5,-.6l-.6,-.5l-.4,-.4l-.5,-.5l-.4,-.5l-.5,-.4l-.4,-.5l-.5,-.5l-.4,-.4l-.4,-.5v.6l.1,.6l.1,.5l.2,.7v.6l.1,.7l.1,.5l.1,.6l.5,.2l.4,.2l.3,.1l.4,.2l.5,.2l.4,.1l.4,.2l.4,.2l.5,.2l.4,.1l.4,.2l.4,.2l.5,.2l.3,.1l.4,.2l.4,.2Z" class="g5_18" />
<path fill-rule="evenodd" d="M109.4,100.4l.6,-.1l.5,.5l.4,.4l.4,.5l.5,.4l.4,.4l.6,.5l.4,.5l.5,.5l.4,.5l.5,.4l.5,.5l.4,.5l.5,.5l.5,.5l.5,.5l.5,.4l-.4,.6l-.5,-.5l-.5,-.6l-.4,-.4l-.5,-.4l-.4,-.5l-.6,-.5l-.4,-.5l-.5,-.5l-.5,-.4l-.4,-.5l-.5,-.5l-.4,-.4l-.5,-.6l-.4,-.4l-.6,-.4l-.4,-.6l.6,-.1l-.8,-.2Z" class="g4_18" />
<path fill-rule="evenodd" d="M110.6,105.8l-.3,-.5l-.2,-.7V104l-.1,-.5l-.2,-.7l-.1,-.6v-.5l-.1,-.6l-.2,-.7l.8,.2l.1,.5v.6l.2,.7l.1,.5v.6l.1,.7l.2,.6l.1,.5l-.3,-.3l-.1,.8Z" class="g4_18" />
<path fill-rule="evenodd" d="M117.5,107.8l-.3,.7l-.4,-.2l-.4,-.1L116,108l-.4,-.1l-.4,-.2l-.4,-.2l-.5,-.2l-.4,-.1l-.4,-.2l-.5,-.2l-.4,-.2l-.4,-.2l-.4,-.1l-.3,-.2L111,106l-.4,-.2l.1,-.8l.4,.2l.4,.1l.3,.2l.5,.1l.4,.2l.4,.2l.4,.2l.5,.1l.4,.2l.4,.2l.4,.2l.5,.2l.4,.1l.3,.2l.4,.1l.5,.2l-.2,.7l.4,-.6Z" class="g4_18" />
<path fill-rule="evenodd" d="M115,114.3L94.8,105.9L74.4,97.4l-.3,1.8l20.7,8.6l20.7,8.6l-.5,-2.1Z" class="g6_18" />
<path fill-rule="evenodd" d="M116.2,120.2L95,111.4L73.6,102.6l-.2,2l21.6,9l21.7,9l-.5,-2.4Z" class="g6_18" />
<path fill-rule="evenodd" d="M117.5,126.8L95.2,117.6L72.8,108.3l-.3,2.3L95.2,120l22.7,9.4l-.4,-2.6Z" class="g6_18" />
<path fill-rule="evenodd" d="M118.8,134.1L95.4,124.3L71.9,114.6l-.4,2.5L95.4,127l23.9,9.9l-.5,-2.8Z" class="g6_18" />
<path fill-rule="evenodd" d="M120.2,142L95.6,131.8L70.9,121.6l-.4,2.8l25.1,10.4l25.3,10.5l-.7,-3.3Z" class="g6_18" />
<path fill-rule="evenodd" d="M121.9,150.9L95.8,140.1l-26,-10.8l-.4,3.2l26.6,11l26.6,11l-.7,-3.6Z" class="g6_18" />
<path fill-rule="evenodd" d="M94.7,107.3l-.9,1.4l-1.5,-2.4v-.2l-.1,-.2v-.1l.1,-.1l.1,-.1l.1,-.1h.1l.2,-.1l2.6,.5l-.7,1.4Z" class="g7_18" />
<path fill-rule="evenodd" d="M93.8,108.7l4.2,7.2l3.4,-3.6l1.7,-5.4l-7.7,-1l-1.6,2.8Z" class="g8_18" />
<path fill-rule="evenodd" d="M101.9,109.6h-.4l-.3,.1l-.2,.1l-.4,.2l-.2,.2l-.2,.2l-.2,.2l-.1,.4l-.2,.2l-.1,.3v.4l-.1,.3v1.3l27.7,19.1l2.3,-3.9L101.9,109.6Z" class="g9_18" />
<path fill-rule="evenodd" d="M101.7,109.5v-.2l-.1,-.2v-.6l.1,-.2l.1,-.2v-.3l.1,-.2l.3,-.2l.1,-.1l.1,-.1l.2,-.1l.1,-.1l.2,-.1h.4L131,126.1l-1.5,2.6L101.7,109.5Z" class="g10_18" />
<path fill-rule="evenodd" d="M98.1,116l-.1,-.3v-.2l-.2,-.3v-.4l.1,-.2l.1,-.3l.2,-.2l.1,-.1l.1,-.2l.2,-.1l.1,-.1l.2,-.1l.2,-.1l.3,-.1l.2,.1l27.7,19.1l-1.5,2.6L98.1,116Z" class="g11_18" />
<path fill-rule="evenodd" d="M131,126.1l-5.2,9l5,3.5l5.2,-9l-5,-3.5Z" class="g5_18" />
<path fill-rule="evenodd" d="M126.7,133.4l-1,1.7l4.6,3.1l1,-1.7l-4.6,-3.1Z" class="g12_18" />
<path fill-rule="evenodd" d="M140.1,134.7l-3.3,5.6l-.3,.3l-.2,.2l-.3,.2l-.2,.2l-.4,.1h-.6l-.3,-.2l-3.7,-2.5l5.3,-9l3.6,2.5h.1l.1,.2l.2,.1l.1,.1v.2l.1,.2v.3l.1,.1v.3l-.1,.2v.3l-.1,.2l-.1,.2v.2Z" class="g13_18" />
<path fill-rule="evenodd" d="M131.9,136.8l3.5,2.6l.3,.1l.3,.2h.7l.3,-.1l.2,-.2l.2,-.1l-.6,1.1l-.3,.3l-.3,.2l-.3,.2l-.4,.1l-.3,.1h-.3l-.4,-.2l-.2,-.2l-3.5,-2.3l1.1,-1.8Z" class="g14_18" />
<path fill-rule="evenodd" d="M136.2,140.4h-.1l1.9,-3.2l.6,.4l-1.8,3.3l-.1,.1l-.5,-.6Z" class="g4_18" />
<path fill-rule="evenodd" d="M134.8,140.9l-.2,-.1l.2,.1h.2l.1,-.1l.2,.1l.2,-.1l.3,-.1l.2,-.1l.2,-.2l.5,.6l-.3,.2l-.3,.2l-.3,.1l-.2,.1h-1.2l-.1,-.2l.5,-.5Z" class="g4_18" />
<path fill-rule="evenodd" d="M98.3,115.9l-.1,-.2l36.6,25.2l-.5,.5l-36.4,-25l-.2,-.3l.6,-.2Z" class="g4_18" />
<path fill-rule="evenodd" d="M92.6,106.1v0l5.7,9.8l-.6,.2L92,106.4l.6,-.3Z" class="g4_18" />
<path fill-rule="evenodd" d="M92.6,106v.1l-.6,.3v-.1l-.2,-.2v-.4l.2,-.1l-.1,-.1l.7,.5Z" class="g4_18" />
<path fill-rule="evenodd" d="M92.7,105.9v0h-.1v.1l-.7,-.5l.2,-.1l.2,-.1l.2,-.1h.2v.7Z" class="g4_18" />
<path fill-rule="evenodd" d="M103,107.3l.3,.1L92.7,105.9v-.7l10.6,1.4l.2,.1l-.5,.6Z" class="g4_18" />
<path fill-rule="evenodd" d="M139.5,132.3l.1,.1L103,107.3l.5,-.6l36.4,25.1l.2,.1l-.6,.4Z" class="g4_18" />
<path fill-rule="evenodd" d="M139.9,134.1h-.1l.1,-.4V133l-.2,-.2v-.1l-.1,-.1l-.1,-.3l.6,-.4l.1,.3l.1,.2l.1,.2l.1,.3v.3l.1,.4l-.1,.3v.5l-.1,.1l-.5,-.4Z" class="g4_18" />
<path fill-rule="evenodd" d="M138,137.2v0l1.9,-3.1l.5,.4l-1.8,3.1l-.6,-.4Z" class="g4_18" />
<path fill-rule="evenodd" d="M123.2,160.9L95.7,149.5L68.4,138.1l-.5,3.3L96,153.1l27.8,11.5l-.6,-3.7Z" class="g6_18" />
<path d="M132.4,169.8h68.2M55,169.8H97.2" class="g15_18" />
<path fill-rule="evenodd" d="M53.9,1157.8h50.2v-24.9H53.9v24.9Z" class="g16_18" />
<path d="M755.7,1156.8h75.5m-735.1,0h15.4m4.7,0h15.4m4.8,0h15.4m4.8,0H172m4.7,0h15.4m4.8,0h15.4m4.8,0h15.4m4.7,0h15.4m4.8,0h15.4m4.8,0H293m4.7,0h15.4m4.8,0h15.4m4.8,0h15.4m4.7,0h15.4m4.8,0h15.4m4.8,0H414m4.4,0h15.4m4.4,0h15.4m4.4,0h15.4m4.4,0h15.4m4.4,0H513m4.4,0h15.4m4.3,0h15.4m4.4,0h15.4m4.4,0h15.4m4.4,0h15.4m4.4,0h15.4m4.4,0h15.4m4.4,0h15.4m4.4,0h15.4m4.4,0h15.4m4.4,0h15.4m4.4,0h15.4" class="g17_18" />
<path d="M349.4,196.9H275m253,0H442.9" class="g18_18" />
</svg>