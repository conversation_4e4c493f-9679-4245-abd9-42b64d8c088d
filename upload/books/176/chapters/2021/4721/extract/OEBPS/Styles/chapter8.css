@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}
.author {
text-align:right;
}

.chapterHeading {
font-size:160%;
color: gray;
margin-bottom:20px;
}
p.Quschan {
	color:#000000;
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.167em;
}
p.Headline {
	color:#f58220;
	font-family:"Walkman-Chanakya-905", serif;
	font-size:2.5em;
	font-weight:bold;
}
span.char-style-override-1 {
	color:#008fd5;
}
p.para-style-override-4 {
	color:#007dc5;
	font-size:5em;
	text-align:center;
	text-indent:0px;
}
p.para-style-override-10 {
	color:#007dc5;
	font-size:1.750em;
	text-align:center;
	text-indent:0px;
}
p.Headsub1 {
	color:#000000;
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.167em;
	font-style:italic;
	font-weight:bold;
}
p.Quschan-H-Sub {
	color:#f58220;
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.333em;
	font-weight:bold;
}
.chapterNumber {
	font-size: 125%;
}

.subHeading {
color:#ce1337;
font-size:125%;
}

.center {
	text-align: center;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:30%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
p
{
	margin-top:10px;
}
h2
{
	color:#FFFFFF;
    font-size:1.5em;
    background:#00984A;
    padding:10px;
}
h4
{
	color:#00984A;
    font-size:1.3em;
}
.footer
{
	display:none;
}
table td
{
	padding:10px;
}
.conc
{
	color:#006699;
}
.englishMeaning
{
	font-family:arial;
}
.right
{
	display:inline;
	float:right;
	clear:both;
}
.bold
{
	font-size:115%;
	font-family: Walkman-Chanakya-905;
		font-weight:bold;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}
.center
{
	text-align:center;
}
.right
{
	text-align:right;
}
.background
{
	background:#999;
	font-weight:bold;
	
}
.superscript{
position:relative;
top:-15%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
bottom:-25%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}
.work
{
	font-size:105% ;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:70%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#CF5300;
width:30%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#FFCC00;

font-weight:normal;

}

.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
#prelims .para-style-override-19
{
	font-weight:bold;
}
#prelims .Hading, #prelims .char-style-override-13, #prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-11, #prelims .char-style-override-15
{
	font-style:italic;
}
#prelims .char-style-override-32
{
	
	color:rgb(236, 0, 140); 
}
#prelims img
{
	width:100%;
} @media only screen and (max-width: 767px) {

div.chapter_pos

{

text-align: center;
position:absolute;
top:40%;
line-height:100%;
font-weight:bold;

font-size:0.8em;

color:#fff;

}

div.chapter_pos div span

{

font-size:0.5em;

color:#eaeaea;

font-weight:normal;

}
   
}
.box{
background-color:#BADBC4;
padding: 15px;
font-size:0.9em;
line-height:120%;
width:40%;
}
.lining_box
{
border:2px solid #008ED3;
padding:15px;
border-radius:15px;
}
.activityBox{
background-color:#ECD7C7;
padding: 15px;
font-size:0.9em;
line-height:120%;
}


.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:40%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}