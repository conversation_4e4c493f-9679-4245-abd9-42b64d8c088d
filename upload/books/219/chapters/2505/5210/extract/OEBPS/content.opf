<?xml version="1.0" encoding="utf-8"?>
<package version="3.0" unique-identifier="bookid" prefix="ibooks: http://vocabulary.itunes.apple.com/rdf/ibooks/vocabulary-extensions-1.0/" xmlns="http://www.idpf.org/2007/opf">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <meta name="generator" content="Adobe InDesign 12.0" />
    <meta property="ibooks:specified-fonts">true</meta>
    <dc:title>ch9</dc:title>
    <dc:date>2017-07-29T10:42:39Z</dc:date>
    <dc:language>en-GB</dc:language>
    <dc:language>en-US</dc:language>
    <meta property="dcterms:modified">2017-08-30T15:03:53Z</meta>
    <dc:identifier id="bookid">urn:uuid:29d919dd-24f5-4384-be78-b447c9dc299b</dc:identifier>
    <meta name="Sigil version" content="0.9.7" />
    <meta name="cover" content="Cover-JeevVigyan.png" />
  </metadata>
  <manifest>
    <item id="ch9" href="Text/ch9.xhtml" media-type="application/xhtml+xml"/>
    <item id="ncx" href="toc.ncx" media-type="application/x-dtbncx+xml"/>
    <item id="toc" href="Text/toc.xhtml" media-type="application/xhtml+xml" properties="nav"/>
    <item id="idGeneratedStyles.css" href="Styles/idGeneratedStyles.css" media-type="text/css"/>
    <item id="x9.1a.png" href="Images/9.1a.png" media-type="image/png"/>
    <item id="x9.1b.png" href="Images/9.1b.png" media-type="image/png"/>
    <item id="x9.3a.png" href="Images/9.3a.png" media-type="image/png"/>
    <item id="x9.3b.png" href="Images/9.3b.png" media-type="image/png"/>
    <item id="x9.3c.png" href="Images/9.3c.png" media-type="image/png"/>
    <item id="mulea.png" href="Images/mulea.png" media-type="image/png"/>
    <item id="wcb.ttf" href="Fonts/wcb.ttf" media-type="application/x-font-ttf"/>
    <item id="wcbi.ttf" href="Fonts/wcbi.ttf" media-type="application/x-font-ttf"/>
    <item id="wcn.ttf" href="Fonts/wcn.ttf" media-type="application/x-font-ttf"/>
    <item id="wcni.ttf" href="Fonts/wcni.ttf" media-type="application/x-font-ttf"/>
    <item id="Cover-JeevVigyan.png" href="Images/Cover-JeevVigyan.png" media-type="image/png" properties="cover-image"/>
    <item id="cover.xhtml" href="Text/cover.xhtml" media-type="application/xhtml+xml" properties="svg"/>
  </manifest>
  <spine toc="ncx">
    <itemref idref="cover.xhtml"/>
    <itemref idref="ch9"/>
    <itemref idref="toc"/>
  </spine>
  <guide>
    <reference type="cover" title="Cover" href="Text/cover.xhtml"/>
  </guide>
</package>
