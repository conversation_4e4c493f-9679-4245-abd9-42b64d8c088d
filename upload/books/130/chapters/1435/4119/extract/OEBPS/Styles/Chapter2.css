   html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}

.subjectHead {
text-align:right;
text-transform:uppercase;
font-size:150%;
margin-bottom:3%;
color:rgb(222,118,28);
}

.chapterText {
font-size:130%;
}

.mainHead {
font-size:120%;
font-weight:bold;
margin:2% 0;
}

.activity {
font-size:120%;
color:rgb(0, 174, 239);
margin:2% 0;
}

.endnote {
font-size:95%;
padding:2%;
}

.questions {
font-size:125%;
margin:2% 0;
color:rgb(222,118,28);
}

.exercises {
color:rgb(46, 49, 146);
font-size:115%;
margin:2% 0;
}
.center {
	text-align: center;
}
p.Heading {
	color:#00AEEF;
font-size:1.3em;
	text-transform: uppercase;
	font-weight: bold;
}
p.subHeading {
	color:#00AEEF;
	font-weight: bold;
font-size:1.1em;

}
p.furtherReading {
	color:#00AEEF;
}
.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
	background-color:#66CCFF;
	border:2px solid #66CCFF;
	padding:15px;
	border-radius:10px;
}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
p
{
	margin-top:10px;
}
h2
{
	color:#fff;
	background-color:#1874CD;
	padding:15px;
	font-size:1.5em;
	font-weight:bold;
}
h4
{
	color:#000;
	
	font-size:1.3em;
	font-weight:bold;
}
.footer
{
	display:none;
}
table td
{
	padding:10px;
}
.conc
{
	color:#006699;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:40%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#421C52;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
.bold
{
	font-weight:bold;
}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:40%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}

#prelims
{
	line-height:200%;
}
#prelims .para-style-override-21
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#00aeef;
}
#prelims .char-style-override-7
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#00aeef;
}
.lining_box
{
	border:2px solid #CCE;
	padding:15px;
	border-radius:5px;
}
.aHeading {
	color: #000;
	text-transform: uppercase;
	font-weight: bold;
	text-align: center;
}

.ebox
{
	border:2px solid #CCE;
	background-color: #CCE;
	padding:15px;
	border-radius:5px;
}

.sbox{
	background-color:#99CCFF;
	border:2px solid #99CCFF;
	padding:15px;
	border-radius:10px;
}
.rbox{
	background-color:#79BEDB;
	border:2px solid #79BEDB;
	padding:15px;
	border-radius:10px;
}

.exercise
{
	border:2px solid #D4D7FE;
	background-color: #D4D7FE;
	padding:15px;
	border-radius:5px;
}
img
{
max-width:100%;
}
p.imgs img, div.imgs div
{
position:relative;
top:20px;
}
p.imgs1 img, div.imgs div
{
position:relative;
top:10px;
}