<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 909 1286" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<style type="text/css"><![CDATA[
.g0_1{
fill: #ED028C;
}
.g1_1{
fill: none;
stroke: #ED028C;
stroke-width: 1.5155555;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g2_1{
fill: none;
stroke: #939598;
stroke-width: 6.106528;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g3_1{
fill: #F0F1F0;
}
.g4_1{
fill: #FFFFFF;
}
.g5_1{
fill: #231F20;
}
.g6_1{
fill: #ECF3F2;
}
.g7_1{
fill: #D2DAD9;
}
.g8_1{
fill: #536261;
}
.g9_1{
fill: #F9F4E0;
}
.g10_1{
fill: #D5212D;
}
.g11_1{
fill: #EE2028;
}
.g12_1{
fill: #AE2931;
}
.g13_1{
fill: #BFC7C7;
}
.g14_1{
fill: #F15C59;
}
.g15_1{
fill: #EA2E33;
}
.g16_1{
fill: none;
stroke: #231F20;
stroke-width: 1.5155555;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g17_1{
fill: #B8282E;
stroke: #B8282E;
stroke-width: 1.5155555;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
.g18_1{
fill: #B8282E;
stroke: #231F20;
stroke-width: 0.77916664;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
]]></style>
</defs>
<path d="M816.9,1189.1h50.3v-21.7H816.9v21.7Z" class="g0_1" />
<path d="M97.2,1188.1h770" class="g1_1" />
<path d="M662.3,116.6V1126.7" class="g2_1" />
<path fill-rule="evenodd" d="M762.9,243.8l23.3,-95.3l10.2,-10.1l58.1,-21.1l18,91.3L762.9,243.8Z" class="g3_1" />
<path fill-rule="evenodd" d="M764,239.7l22.8,-88.1l10.3,-10.7l53.7,-23.2l17.9,88.9L764,239.7Z" class="g4_1" />
<path fill-rule="evenodd" d="M786.8,148.8l-.3,.4L764.6,237l.7,-.4l22.1,-87.5l-.2,.4l-.4,-.7Z" class="g5_1" />
<path fill-rule="evenodd" d="M797.2,139h-.1l-10.3,9.8l.4,.7l10.4,-9.7l-.3,.2l-.1,-1Z" class="g5_1" />
<path fill-rule="evenodd" d="M852.1,118l-54.9,21l.1,1l54.9,-20.9l-.1,-1.1Z" class="g5_1" />
<path fill-rule="evenodd" d="M869,203.9L852.1,118.5L851,119l16.7,85.2l1.3,-.3Z" class="g5_1" />
<path fill-rule="evenodd" d="M764.5,237L868,204.1l.1,-1L764.7,236.2l-.2,.8Z" class="g5_1" />
<path fill-rule="evenodd" d="M787,149.3l.6,-.5l.7,-.8l.7,-.5l.6,-.6l.7,-.7l.5,-.5l.8,-.7l.7,-.6l.5,-.5l.8,-.8l.5,-.5l.7,-.6l.6,-.6l.7,-.6l.6,-.6l.6,-.6l-.1,.8l-.1,.8l-.1,.6l-.3,.9l-.1,.8l-.1,.8l-.1,.6l-.2,.9l-5.2,2l-.6,.2l-3.4,1.3Z" class="g6_1" />
<path fill-rule="evenodd" d="M797.9,139.5l-.8,-.2l-.7,.6l-.6,.5l-.6,.7l-.7,.6l-.6,.5l-.7,.6l-.5,.7l-.8,.5l-.5,.7l-.7,.6l-.8,.6l-.5,.7l-.7,.5l-.7,.8l-.6,.5l-.7,.6l.5,.7l.7,-.6l.7,-.7l.6,-.5l.7,-.6l.6,-.7l.7,-.6l.5,-.7l.8,-.5l.7,-.6l.5,-.7l.8,-.6l.5,-.5l.7,-.7l.6,-.5l.7,-.6l.6,-.7l-.8,-.2l1.1,-.2Z" class="g5_1" />
<path fill-rule="evenodd" d="M796.2,146.4l.5,-.6l.2,-.9l.1,-.8l.1,-.6l.2,-.9l.1,-.8l.1,-.7l.1,-.7l.3,-.9l-1.1,.2l-.1,.7l-.1,.8l-.2,.8l-.1,.7l-.1,.8l-.1,.8l-.3,.9l-.1,.6l.5,-.5v1.1Z" class="g5_1" />
<path fill-rule="evenodd" d="M786.7,149l.3,.9l5.8,-2.2l3.4,-1.3v-1.1l-7.6,2.9l-.4,.2l-1.2,.5l.2,.8l-.5,-.7Z" class="g5_1" />
<path fill-rule="evenodd" d="M790,157.3l56.6,-21.6l.4,2.2L789.5,160l.5,-2.7Z" class="g7_1" />
<path fill-rule="evenodd" d="M788.5,164.9l59.2,-22.6l.3,2.6l-60.2,23l.7,-3Z" class="g7_1" />
<path fill-rule="evenodd" d="M786.7,173.4l62.1,-23.8l.4,2.9L786,176.7l.7,-3.3Z" class="g7_1" />
<path fill-rule="evenodd" d="M784.8,182.7l65.3,-25l.5,3.1l-66.5,25.5l.7,-3.6Z" class="g7_1" />
<path fill-rule="evenodd" d="M782.8,192.9l68.7,-26.3l.6,3.6l-70.2,26.9l.9,-4.2Z" class="g7_1" />
<path fill-rule="evenodd" d="M780.5,204.3L853,176.6l.6,4l-74,28.3l.9,-4.6Z" class="g7_1" />
<path fill-rule="evenodd" d="M818.4,148.4l1.2,1.8l2.1,-3.2v-.3l.2,-.1v-.2l-.2,-.1l-.1,-.1l-.2,-.2h-.1l-.3,-.1l-3.6,.7l1,1.8Z" class="g8_1" />
<path fill-rule="evenodd" d="M819.6,150.2l-5.8,9.2l-4.7,-4.7l-2.5,-6.8l10.8,-1.3l2.2,3.6Z" class="g9_1" />
<path fill-rule="evenodd" d="M808.4,151.2l.5,.1l.4,.1l.3,.1l.5,.3l.3,.3l.6,.5l.2,.4l.3,.3l.1,.4v.5l.2,.4l-.1,.4v1.2l-38.6,24.6l-3.2,-5.1l38.5,-24.5Z" class="g10_1" />
<path fill-rule="evenodd" d="M808.6,151.2v-.3l.1,-.3v-.7l-.1,-.3l-.2,-.3l.1,-.3l-.2,-.3l-.3,-.2l-.2,-.2l-.1,-.1l-.3,-.1l-.2,-.2l-.3,-.1l-.3,.1h-.1l-38.7,24.5l2.1,3.3l38.7,-24.5Z" class="g11_1" />
<path fill-rule="evenodd" d="M813.7,159.4l.1,-.3v-.3l.2,-.3v-.6l-.1,-.3l-.2,-.2l-.1,-.3l-.2,-.2l-.1,-.2l-.3,-.2l-.2,-.1l-.3,-.1l-.3,-.1l-.3,-.1h-.3L773,180.6l2.1,3.4l38.6,-24.6Z" class="g12_1" />
<path fill-rule="evenodd" d="M767.8,172.4l7.3,11.6l-7,4.4l-7.2,-11.5l6.9,-4.5Z" class="g6_1" />
<path fill-rule="evenodd" d="M773.9,181.9l1.3,2l-6.4,4l-1.4,-2l6.5,-4Z" class="g13_1" />
<path fill-rule="evenodd" d="M755.2,183.5l4.6,7.2l.3,.4l.3,.3l.5,.2l.3,.3l.5,.1l.4,-.1h.5l.3,-.2l5.2,-3.3l-7.4,-11.5l-5,3.3h-.2l-.1,.1l-.2,.2l-.1,.2v.3l-.2,.1v.5l-.2,.1v.3l.2,.3v.5l.1,.2l.2,.2v.3Z" class="g14_1" />
<path fill-rule="evenodd" d="M766.6,186.2l-4.9,3.3l-.4,.2l-.4,.2h-.3l-.8,.1l-.3,-.2l-.3,-.2l-.3,-.2l.9,1.4l.3,.5l.5,.2l.5,.3l.4,.1l.5,.1l.4,-.1l.5,-.2l.3,-.3l4.9,-3l-1.5,-2.2Z" class="g15_1" />
<path fill-rule="evenodd" d="M760.6,190.8h.2l-2.7,-4.1l-.8,.5l2.5,4.2l.2,.2l.6,-.8Z" class="g5_1" />
<path fill-rule="evenodd" d="M762.6,191.5l.2,-.2l-.2,.2h-.3l-.1,-.1h-.4l-.3,-.1l-.6,-.3l-.3,-.2l-.6,.8l.3,.2l.4,.3l.5,.1l.3,.1l.5,.1h.3l.3,-.1h.5l.1,-.2l-.6,-.6Z" class="g5_1" />
<path fill-rule="evenodd" d="M813.4,159.3l.1,-.2l-50.9,32.4l.6,.6L814,160l.1,-.3l-.7,-.4Z" class="g5_1" />
<path fill-rule="evenodd" d="M821.3,146.8l-7.9,12.5l.7,.4l8.1,-12.6l-.9,-.3Z" class="g5_1" />
<path fill-rule="evenodd" d="M821.3,146.6v.2l.9,.3V147l.1,-.2v-.3l.1,-.1v-.2l-.2,-.1V146l-.9,.6Z" class="g5_1" />
<path fill-rule="evenodd" d="M821.1,146.5h.2v.1l.9,-.6l-.1,-.2l-.4,-.1l-.3,-.1h-.3v.9Z" class="g5_1" />
<path fill-rule="evenodd" d="M806.8,148.3l-.4,.2l14.7,-2v-.9l-14.6,1.8l-.3,.2l.6,.7Z" class="g5_1" />
<path fill-rule="evenodd" d="M756,180.4l-.1,.2l50.9,-32.3l-.6,-.7l-50.8,32.2l-.2,.1l.8,.5Z" class="g5_1" />
<path fill-rule="evenodd" d="M755.5,182.7h.2l-.2,-.4v-.9l.2,-.3v-.2l.1,-.2l.2,-.3l-.8,-.5l-.1,.3l-.2,.3l-.1,.3l-.2,.4v.4l-.2,.5l.2,.4v.6l.1,.1l.8,-.5Z" class="g5_1" />
<path fill-rule="evenodd" d="M758.1,186.7l-2.6,-4l-.8,.5l2.6,4l.8,-.5Z" class="g5_1" />
<path fill-rule="evenodd" d="M778.7,217.1L855,187.9l.6,4.2l-77.8,29.8l.9,-4.8Z" class="g7_1" />
<path d="M664.4,225.5H768.2m53.9,0h45.2" class="g16_1" />
<image preserveAspectRatio="none" x="97" y="114" width="550" height="28" xlink:href="/funlearn/downloadEpubImage?source=upload/books/349/chapters/3063/6544/extract/Chapter-1(1)/1/img/1.png" />
<path fill-rule="evenodd" d="M154.8,837.7h98.1V868H154.8V837.7Z" class="g17_1" />
<path fill-rule="evenodd" d="M343.4,235.9c1.2,-1.2,2.8,-1.9,4.6,-1.9h49.8c1.8,0,3.4,.7,4.6,1.9c1.3,1.3,1.9,2.8,1.9,4.6v48.2c0,1.7,-.6,3.3,-1.9,4.5c-1.2,1.3,-2.8,1.9,-4.6,1.9H348c-1.8,0,-3.4,-.6,-4.6,-1.9c-1.3,-1.2,-1.9,-2.8,-1.9,-4.5V240.5c0,-1.8,.6,-3.3,1.9,-4.6Z" class="g18_1" />
<path d="M92,155.2H654V107.9H92v47.3Z" class="g4_1" />
<image preserveAspectRatio="none" x="462" y="481" width="185" height="256" xlink:href="/funlearn/downloadEpubImage?source=upload/books/349/chapters/3063/6544/extract/Chapter-1(1)/1/img/2.png" />
<image preserveAspectRatio="none" x="97" y="815" width="59" height="58" xlink:href="/funlearn/downloadEpubImage?source=upload/books/349/chapters/3063/6544/extract/Chapter-1(1)/1/img/3.png" />
</svg>