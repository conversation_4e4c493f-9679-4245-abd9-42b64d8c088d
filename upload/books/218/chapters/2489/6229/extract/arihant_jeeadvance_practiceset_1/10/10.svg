<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 909 1286" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<clipPath id="c0_10"><path d="M614.7,143.3l0,-21.1l237.4,0l0,21.1Z" /></clipPath>
<style type="text/css"><![CDATA[
.g1_10{
fill: #231F20;
}
.g2_10{
fill: none;
stroke: #231F20;
stroke-width: 0.62058336;
stroke-linecap: butt;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g3_10{
fill: none;
stroke: #231F20;
stroke-width: 0.62058336;
stroke-linecap: round;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g4_10{
fill: none;
stroke: #231F20;
stroke-width: 1.2411814;
stroke-linecap: round;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g5_10{
fill: none;
stroke: #231F20;
stroke-width: 0.57294786;
stroke-linecap: butt;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g6_10{
fill: none;
stroke: #231F20;
stroke-width: 0.57294786;
stroke-linecap: round;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g7_10{
fill: none;
stroke: #231F20;
stroke-width: 1.1459104;
stroke-linecap: round;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g8_10{
fill: none;
stroke: #231F20;
stroke-width: 0.28626034;
stroke-linecap: butt;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g9_10{
fill: none;
stroke: #A8AAAD;
stroke-width: 0.9168042;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g10_10{
fill: none;
stroke: #A8AAAD;
stroke-width: 0.76393545;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
]]></style>
</defs>
<path fill-rule="evenodd" d="M159.5,243l42.7,0l-42.7,0Z" class="g1_10" />
<path d="M159.5,243l42.7,0" class="g2_10" />
<path fill-rule="evenodd" d="M281.9,243l42.7,0l-42.7,0Z" class="g1_10" />
<path d="M281.9,243l42.7,0" class="g2_10" />
<path fill-rule="evenodd" d="M409.3,243l42.7,0l-42.7,0Z" class="g1_10" />
<path d="M409.3,243l42.7,0" class="g2_10" />
<path fill-rule="evenodd" d="M637.2,242.5l2.2,-1.2l-2.2,1.2Z" class="g1_10" />
<path d="M637.2,242.5l2.2,-1.2" class="g3_10" />
<path fill-rule="evenodd" d="M639.4,241.6l3.1,5.5l-3.1,-5.5Z" class="g1_10" />
<path d="M639.4,241.6l3.1,5.5" class="g4_10" />
<path fill-rule="evenodd" d="M642.8,247.1l4,-16.3l-4,16.3Z" class="g1_10" />
<path d="M642.8,247.1l4,-16.3" class="g3_10" />
<path fill-rule="evenodd" d="M646.8,230.8l8.3,0l-8.3,0Z" class="g1_10" />
<path d="M646.8,230.8l8.3,0" class="g3_10" />
<path fill-rule="evenodd" d="M114.1,291l10.3,0l-10.3,0Z" class="g1_10" />
<path d="M114.1,291l10.3,0" class="g5_10" />
<path fill-rule="evenodd" d="M271.2,303.5l1.9,-1.1l-1.9,1.1Z" class="g1_10" />
<path d="M271.2,303.5l1.9,-1.1" class="g6_10" />
<path fill-rule="evenodd" d="M273.1,302.7l2.7,5l-2.7,-5Z" class="g1_10" />
<path d="M273.1,302.7l2.7,5" class="g7_10" />
<path fill-rule="evenodd" d="M276.1,307.7L279.6,293l-3.5,14.7Z" class="g1_10" />
<path d="M276.1,307.7L279.6,293" class="g6_10" />
<path fill-rule="evenodd" d="M279.6,293l9.2,0l-9.2,0Z" class="g1_10" />
<path d="M279.6,293l9.2,0" class="g6_10" />
<path fill-rule="evenodd" d="M261.8,291l28.2,0l-28.2,0Z" class="g1_10" />
<path d="M261.8,291l28.2,0" class="g5_10" />
<path fill-rule="evenodd" d="M410.4,303.5l1.8,-1.1l-1.8,1.1Z" class="g1_10" />
<path d="M410.4,303.5l1.8,-1.1" class="g6_10" />
<path fill-rule="evenodd" d="M412.2,302.7l2.7,5l-2.7,-5Z" class="g1_10" />
<path d="M412.2,302.7l2.7,5" class="g7_10" />
<path fill-rule="evenodd" d="M415.2,307.7L418.8,293l-3.6,14.7Z" class="g1_10" />
<path d="M415.2,307.7L418.8,293" class="g6_10" />
<path fill-rule="evenodd" d="M418.8,293l9.1,0l-9.1,0Z" class="g1_10" />
<path d="M418.8,293l9.1,0" class="g6_10" />
<path fill-rule="evenodd" d="M408.9,291l20.2,0l-20.2,0Z" class="g1_10" />
<path d="M408.9,291l20.2,0" class="g5_10" />
<path fill-rule="evenodd" d="M568.6,303.5l1.9,-1.1l-1.9,1.1Z" class="g1_10" />
<path d="M568.6,303.5l1.9,-1.1" class="g6_10" />
<path fill-rule="evenodd" d="M570.5,302.7l2.7,5l-2.7,-5Z" class="g1_10" />
<path d="M570.5,302.7l2.7,5" class="g7_10" />
<path fill-rule="evenodd" d="M573.5,307.7L577,293l-3.5,14.7Z" class="g1_10" />
<path d="M573.5,307.7L577,293" class="g6_10" />
<path fill-rule="evenodd" d="M577,293l9.2,0l-9.2,0Z" class="g1_10" />
<path d="M577,293l9.2,0" class="g6_10" />
<path fill-rule="evenodd" d="M559.2,291l28.1,0l-28.1,0Z" class="g1_10" />
<path d="M559.2,291l28.1,0" class="g5_10" />
<path fill-rule="evenodd" d="M181.5,635.1l18.7,0l-18.7,0Z" class="g1_10" />
<path d="M181.5,635.1l18.7,0" class="g2_10" />
<path fill-rule="evenodd" d="M228.6,635.1l18.8,0l-18.8,0Z" class="g1_10" />
<path d="M228.6,635.1l18.8,0" class="g2_10" />
<path fill-rule="evenodd" d="M274,610.5l18.8,0l-18.8,0Z" class="g1_10" />
<path d="M274,610.5l18.8,0" class="g8_10" />
<path fill-rule="evenodd" d="M329.3,610.5l18.8,0l-18.8,0Z" class="g1_10" />
<path d="M329.3,610.5l18.8,0" class="g8_10" />
<path fill-rule="evenodd" d="M264.6,635.1l101,0l-101,0Z" class="g1_10" />
<path d="M264.6,635.1l101,0" class="g2_10" />
<path fill-rule="evenodd" d="M392.2,610.5l18.8,0l-18.8,0Z" class="g1_10" />
<path d="M392.2,610.5l18.8,0" class="g8_10" />
<path fill-rule="evenodd" d="M447.5,610.5l18.8,0l-18.8,0Z" class="g1_10" />
<path d="M447.5,610.5l18.8,0" class="g8_10" />
<path fill-rule="evenodd" d="M382.8,635.1l102.2,0l-102.2,0Z" class="g1_10" />
<path d="M382.8,635.1l102.2,0" class="g2_10" />
<path fill-rule="evenodd" d="M202.2,686.5l40.1,0l-40.1,0Z" class="g1_10" />
<path d="M202.2,686.5l40.1,0" class="g5_10" />
<path fill-rule="evenodd" d="M498.4,686.5l40.1,0l-40.1,0Z" class="g1_10" />
<path d="M498.4,686.5l40.1,0" class="g5_10" />
<path fill-rule="evenodd" d="M567.1,686.5l16.1,0l-16.1,0Z" class="g1_10" />
<path d="M567.1,686.5l16.1,0" class="g5_10" />
<path fill-rule="evenodd" d="M200,736l40.1,0L200,736Z" class="g1_10" />
<path d="M200,736l40.1,0" class="g5_10" />
<path fill-rule="evenodd" d="M264.2,736l16,0l-16,0Z" class="g1_10" />
<path d="M264.2,736l16,0" class="g5_10" />
<path fill-rule="evenodd" d="M498.7,736l40.1,0l-40.1,0Z" class="g1_10" />
<path d="M498.7,736l40.1,0" class="g5_10" />
<path fill-rule="evenodd" d="M562.9,736l16.1,0l-16.1,0Z" class="g1_10" />
<path d="M562.9,736l16.1,0" class="g5_10" />
<path fill-rule="evenodd" d="M148.7,853l1.9,-1.1l-1.9,1.1Z" class="g1_10" />
<path d="M148.7,853l1.9,-1.1" class="g6_10" />
<path fill-rule="evenodd" d="M150.6,852.2l2.7,5l-2.7,-5Z" class="g1_10" />
<path d="M150.6,852.2l2.7,5" class="g7_10" />
<path fill-rule="evenodd" d="M153.6,857.2l3.5,-14.7l-3.5,14.7Z" class="g1_10" />
<path d="M153.6,857.2l3.5,-14.7" class="g6_10" />
<path fill-rule="evenodd" d="M157.1,842.5l9.2,0l-9.2,0Z" class="g1_10" />
<path d="M157.1,842.5l9.2,0" class="g6_10" />
<path fill-rule="evenodd" d="M147.3,840.5l20.2,0l-20.2,0Z" class="g1_10" />
<path d="M147.3,840.5l20.2,0" class="g5_10" />
<path fill-rule="evenodd" d="M296.5,853l1.8,-1.1l-1.8,1.1Z" class="g1_10" />
<path d="M296.5,853l1.8,-1.1" class="g6_10" />
<path fill-rule="evenodd" d="M298.3,852.2l2.7,5l-2.7,-5Z" class="g1_10" />
<path d="M298.3,852.2l2.7,5" class="g7_10" />
<path fill-rule="evenodd" d="M301.3,857.2l3.6,-14.7l-3.6,14.7Z" class="g1_10" />
<path d="M301.3,857.2l3.6,-14.7" class="g6_10" />
<path fill-rule="evenodd" d="M304.9,842.5l9.1,0l-9.1,0Z" class="g1_10" />
<path d="M304.9,842.5l9.1,0" class="g6_10" />
<path fill-rule="evenodd" d="M295,840.5l20.2,0l-20.2,0Z" class="g1_10" />
<path d="M295,840.5l20.2,0" class="g5_10" />
<path fill-rule="evenodd" d="M443.6,853l1.8,-1.1l-1.8,1.1Z" class="g1_10" />
<path d="M443.6,853l1.8,-1.1" class="g6_10" />
<path fill-rule="evenodd" d="M445.4,852.2l2.8,5l-2.8,-5Z" class="g1_10" />
<path d="M445.4,852.2l2.8,5" class="g7_10" />
<path fill-rule="evenodd" d="M448.4,857.2L452,842.5l-3.6,14.7Z" class="g1_10" />
<path d="M448.4,857.2L452,842.5" class="g6_10" />
<path fill-rule="evenodd" d="M452,842.5l9.2,0l-9.2,0Z" class="g1_10" />
<path d="M452,842.5l9.2,0" class="g6_10" />
<path fill-rule="evenodd" d="M442.1,840.5l20.2,0l-20.2,0Z" class="g1_10" />
<path d="M442.1,840.5l20.2,0" class="g5_10" />
<path fill-rule="evenodd" d="M114.4,1088.7l1.8,-1l-1.8,1Z" class="g1_10" />
<path d="M114.4,1088.7l1.8,-1" class="g6_10" />
<path fill-rule="evenodd" d="M116.2,1087.9l2.8,5.1l-2.8,-5.1Z" class="g1_10" />
<path d="M116.2,1087.9l2.8,5.1" class="g7_10" />
<path fill-rule="evenodd" d="M119.2,1093l3.6,-14.8l-3.6,14.8Z" class="g1_10" />
<path d="M119.2,1093l3.6,-14.8" class="g6_10" />
<path fill-rule="evenodd" d="M122.8,1078.2l9.1,0l-9.1,0Z" class="g1_10" />
<path d="M122.8,1078.2l9.1,0" class="g6_10" />
<path d="M55.7,150.4l797.5,0" class="g9_10" />
<path d="M734.4,123.1l0,6.4l-0.9,0c-0.5,-2,-1.3,-3.5,-2.3,-4.4c-1,-0.8,-2.5,-1.3,-4.3,-1.3l-1.2,0l0,8l0.1,0c1.2,0,2,-0.3,2.6,-0.9c0.6,-0.6,1,-1.5,1.1,-2.8l0.8,0l0,8.1l-0.8,0C729.4,135,729,134,728.4,133.4c-0.6,-0.6,-1.5,-0.9,-2.6,-0.9l-0.1,0l0,8.3l1.6,0c1.7,0,3.1,-0.5,4.2,-1.5c1,-1.1,1.9,-2.7,2.4,-4.8l0.9,0l0,7.1l-16.2,0l0,-0.8l2.1,0l0,-17l-2.1,0l0,-0.7l15.8,0Z" class="g1_10" />
<g clip-path="url(#c0_10)">
<path d="M708.6,123.8l-4.3,0l0,-0.7l12.4,0l0,0.7l-2.2,0l0,11.6c0,2.3,-0.6,4.8,-1.8,5.8c-3.5,3.3,-13.8,3,-12.7,-3.6c0.6,-3.3,5.6,-4,5.6,-0.6c0,0.5,-0.1,1,-0.3,1.3c-0.3,0.4,-0.5,0.7,-0.9,0.9c-0.2,0.1,-0.4,0.2,-0.8,0.3c-0.4,0.1,-0.6,0.2,-0.6,0.4c0,0.2,0.1,0.3,0.3,0.5c0.2,0.1,0.5,0.1,0.8,0.1c2.6,0,8.4,-3,4.5,-5.8l0,-10.9Z" class="g1_10" />
</g>
<path d="M753.7,123.1l0,6.4l-0.8,0c-0.5,-2,-1.3,-3.5,-2.3,-4.4c-1.1,-0.8,-2.5,-1.3,-4.4,-1.3l-1.1,0l0,8l0.1,0c1.1,0,2,-0.3,2.6,-0.9c0.6,-0.6,1,-1.5,1.1,-2.8l0.8,0l0,8.1l-0.8,0c-0.1,-1.2,-0.5,-2.2,-1.1,-2.8c-0.6,-0.6,-1.5,-0.9,-2.6,-0.9l-0.1,0l0,8.3l1.5,0c1.7,0,3.1,-0.5,4.2,-1.5c1.1,-1.1,1.9,-2.7,2.5,-4.8l0.8,0l0,7.1l-16.1,0l0,-0.8l2.1,0l0,-17l-2.1,0l0,-0.7l15.7,0Z" class="g1_10" />
<path d="M55.8,941.7l687.4,0" class="g10_10" />
</svg>