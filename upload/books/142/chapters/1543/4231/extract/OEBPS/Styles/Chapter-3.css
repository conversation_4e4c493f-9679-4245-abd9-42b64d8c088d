html, body {
font-family:"arial";
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.subheading {
color:#FF0000;
font-size:120%;
font-weight:bold;
}

.subhead2
{
	color:#FF6600;
	font-weight:bold;
}
.credit
{
	display:none;
}
span.first-letter {
	color:#00aeef;
}
p.activityTitle {
	font-size:1.333em;
	font-weight:bold;
	line-height:1.3;
	text-align:right;
}
p.heading {
	color:#231f20;
	font-size:1.5em;
}
span.char-style-override-5 {
	color:#231f20;
}
p.BOX-TITLE-BIO {
	font-size:1em;
	font-weight:bold;
	line-height:1.3;
}
p.sub-heading-chart-titles {
	color:#00aeef;
	font-size:1em;
	font-weight:bold;
}
p.ques1 {
	color:#00aeef;
	font-size:3.333em;
}
p.REF-TITLE {
	color:#00aeef;
	font-size:0.917em;
	font-weight:bold;
}
/*              NEW CSS           */



div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 97%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#333399;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.lining_box
{
border:1px solid #000;
padding:5px;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:30%;
}
.glossary
{
	margin-left:0px;
	width:20%;
}

.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

h2

{

color:#006699;

}

h4

{

color:#d1640f;

}

.footer

{

display:none;

}

table td

{

padding:10px;

}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}


div.chapter_pos div span

{

font-size:0.5em;

color:#eaeaea;

font-weight:normal;

}
   
}

#prelims .char-style-override-15, #prelims .char-style-override-19
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#af7b0a;
}
#prelims .char-style-override-2
{
	font-style:italic;
}
#prelims span.char-style-override-4 {
	color:#231f20;
	font-size:100%;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:#FD3F9C;
padding:10px;
}
/* Chapter number */
h4
{
color:#FD3F9C;
font-size:1.5em;
}
/* Concept Heading */
.ConceptHeading
{
color:#FD3F9C;
font-size:1.3em;
font-weight:bold;
margin-top:40px;
}
/* Sub Heading */
.SubHeading
{
font-size:1.1em;
font-weight:bold;
color:#FD3F9C;
margin-top:30px;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.caption
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
text-align:center;
}
.box{
background-color:#9FDAF4;
padding: 15px;
font-size:0.9em;
line-height:120%;
border-radius:15px;
	margin: 1%;
}
table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
.lining_box2
{
background-color:#FFFACD;
border:2px solid #FFA500;
padding:15px;
border-radius:15px;
font-size:0.9em;
margin:20px 0px;
}

.cover_img_small
{
width:50%;
}

.lini
{
border:2px solid #FD3F9C;
padding:15px;
border-radius:15px;
}
.question
{
background-color:#F5F5F5;
border:2px solid #FFA500;
padding:15px;
border-radius:15px;
}
.bg
{
background-color:#BEBEBE;
}
.lining_box5
{
background-color:#FBC1D7;
padding:15px;
border-radius:15px;
margin:20px 0px 20px 0px;
font-size:0.9em;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:10%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}