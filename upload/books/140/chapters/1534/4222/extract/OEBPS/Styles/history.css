body {
font-family:"Arial", sans-serif;
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}

.chapterHeading {
font-size:160%;
color: gray;
margin-bottom:20px;
}

.chapterNumber {
	font-size: 125%;
	font-family:"Arial", sans-serif;
}

.subHeading {
color:rgb(0,0,0);
font-size:1.3em;
text-transform:uppercase;
padding: 1.5%;
background: #D0D1D3;
margin: 1% 0;
border-right: solid 50px #F7941D;
}

.sub {
	color:#F7941D;
	font-size:1.1em;
	text-transform:uppercase;
	font-weight:bold;
}

.author {
	text-align:right
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
background-color:#DCDDDF;
padding: 15px;
border-radius:5px;
}

.activityBox{
background-color:rgba(255,255,255);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#333;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:45%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

h2

{

color:#FFF;
font-size:1.5em;
padding:15px;
background-color:#F7941D;

}

h4

{
color:#000;
font-size:1.3em;
padding:15px;

}

.footer

{

display:none;

}

table, td

{

padding:10px;
border-collapse: collapse;

}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:10%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}

#prelims .char-style-override-15, #prelims .char-style-override-19
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#af7b0a;
}
.char-style-override-2
{
	font-style:italic;
}
.pbox{
background-color:#FFCF79;
padding: 15px;
border-radius:5px;
}
.phead {
font-size:1.2em;
color: #FFF;
text-align: center;
background-color:#F7931D;
padding: 5px;
border-radius:5px;
font-weight:bold;
}
.bghead {
font-size:1.2em;
color: #FFF;
text-align: right;
background-color:#808285;
padding: 5px;
border-radius:5px;
font-weight:bold;
text-transform:uppercase;
}
.abox{
background-color:#DCDDDF;
padding: 15px;
border-radius:5px;
}
.aohead {
font-size:1.2em;
color: #FFF;
text-align: right;
background-color:#F89D33;
padding: 5px;
border-radius:5px;
font-weight:bold;
}
.abox1{
background-color:#FDC689;
padding: 15px;
border-radius:5px;
}
.ebox{
background-color:#FEECD7;
padding: 15px;
border-radius:5px;
}
.exercise {
background-color:#FFDFB8;
padding: 15px;
border-radius:5px;
}
.lining_box
{
border:3px solid #FF8362;
padding:15px;
border-radius:5px;
}
.lining_box1
{
border:3px solid #089DE3;
padding:15px;
border-radius:5px;
}