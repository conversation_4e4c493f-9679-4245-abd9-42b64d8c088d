
@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:bold;

src : url("../Fonts/wcb.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:normal;

src : url("../Fonts/wcn.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}


html, body {

font-family:"Walkman-Chanakya-905";

}


body {

font-size:120%;

line-height:150%;

padding:2%;

text-align:justify;

}


* {

margin:0;

padding:0;

}


.image {

text-align:center;

}

.author {

text-align:right;

}


.chapterHeading {

font-size:160%;

color: gray;

margin-bottom:20px;

}


.chapterNumber {

font-size: 125%;

}


.subHeading {

color:#ce1337;

font-size:125%;

}


.center {

text-align: center;

}


.excercise {

text-transform:uppercase;

font-weight:bold;

margin:1% 0%;

}


.box{

background-color:#FFEACC;
font-size:0.9em;
padding:15px;

}
.box_blue{
background-color:#C7EAFB;
font-size:0.9em;
padding:15px;
}


.activityBox{

background-color:rgba(206, 19, 55, 0.4);

padding: 5px 5px 5px 5px;

margin: 5px 5px 5px 5px;

}


.newWordsBox{

background-color:rgba(252, 187, 118, 0.4);

padding: 5px 5px 5px 5px;

margin: 5px 5px 5px 5px;

}
.clear
{
	clear:both;
}
p.para-style-override-1 {

color:#00aeef;

font-family:"Walkman-Chanakya-905", serif;

font-weight:bold;

text-align:center;

}

img

{

margin-left: auto;

margin-right: auto;

display: block;

width:50%;
max-width:100%;

}

img.img_wid
{
	margin-left: 20px;
    margin-right: none;
	display: inline;
	width:auto;
position:relative;
top:5px;
height:25px;
}

ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

h2

{

color:#fff;

font-size:1.5em;

background:#EC008C;

padding:10px;

}

/* Chapter number */

h4

{

color:#d1640f;

font-size:1.3em;

}

/* Concept Heading */

.ConceptHeading

{

color:#d1640f;

font-size:1.3em;

font-weight:bold;

margin-top:20px;

}

/* Sub Heading */

.SubHeading

{

color:#d1640f;

font-size:1.1em;

font-weight:bold;

}

/* Sub Heading 2*/

.SubHeading2

{

color:#d1640f;

font-size:1em;

font-weight:bold;

}

/* Hightlisght Boxes */

.NewWordBox{

background-color:#F7E7BD;

padding: 15px;

font-size:0.9em;

line-height:120%;

}

.activityBox{

background-color:rgba(206, 19, 55, 0.4);

padding: 15px;

font-size:0.9em;

line-height:120%;

}

.textHeading{
color:#EC008C;
font-size:1.3em;
font-weight:bold;
margin-bottom:20px;
}
.textSmall{
color:#EC008C;
font-size:1.3em;
font-weight:bold;
}
.textPoem{
color:#EC008C;
font-size:1.1em;
font-weight:bold;
}
.lining_box
{
border:2px solid #EC008C;
padding:15px;
border-radius:15px;
}
.lining_box_slat
{
border:2px solid #000;
padding:15px;
border-radius:1px;
}




}.footer

{

display:none;

}

table td

{

padding:10px;

}

.conc

{

color:#006699;

}

.englishMeaning

{

font-family:arial;
position:relative;

color:red;
}

.right

{

display:inline;

float:right;

clear:both;

}

.bold

{

font-size:115%;

font-family: Walkman-Chanakya-905;

font-weight:bold;

}

.italic

{

font-weight:bold;

font-size:100%;

color:#03C;

}

.center

{

text-align:center;

}

.right

{

text-align:right;

}

.background

{

background:#999;

font-weight:bold;

}

.superscript{

position:relative;

top:-15%;

font-size: 85%;

font-family:Arial, Helvetica, sans-serif;

}


.subscript{

position:relative;

bottom:-25%;

font-size: 85%;

font-family:Arial, Helvetica, sans-serif;

}

.work

{

font-size:105% ;

}

div.layout

{

text-align: center;

}

div.chapter_pos


{


text-align: center;


width: 96%;


position:absolute;


top:60%;


font-weight:bold;


font-size:28px;


color:#fff;


}


div.chapter_pos div


{


background:#CF5300;


padding:10px;


width:40%;


margin:auto;

opacity:0.9;


}


div.chapter_pos div span


{


font-size:0.7em;


color:#FFCC00;


font-weight:normal;


}


.note

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

}

.underline_txt

{

font-decoration:underline;

}

.bold_txt

{

font-weight:bold;

}

.center_element

{

margin:auto;

}

.italics_txt

{

font-style:italic;

}

.block_element

{

display:block;

}

.img_rt

{

float:right;

clear:both;

}

.img_lft

{

float:left;

}




.cover_img_small

{

width:50%;

}

@media only screen and (max-width: 767px) {


div.chapter_pos


{

top:50%;

font-size:1em;

}

div.chapter_pos div


{

width:80%;

}

.cover_img_small

{

width:90%;

}

}


table

{

    width:100%;

    border:1px solid #000;

    border-collapse:collapse;

}
h3
{
color:rgb(0, 174, 239);
font-size:1.3em;
}

td

{

    padding:10px;

    border:1px solid #000;

    border-collapse:collapse;

}