<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title>Cover</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000001.jpg');"> </div>
    <div class="pos fs0" style="left: 216px; top: 130px; color: #9966FF;">Unit-9</div>
    <div class="pos fs1" style="left: 131px; top: 348px; ">Do you have a grandparent who does things for you?</div>
    <div class="pos fs1" style="left: 131px; top: 386px; ">Let’s read about the Granny in this poem.</div>
    <div class="pos fs2" style="left: 131px; top: 431px; color: #00FFFF;">GrannyGrannyPleaseCombmyHair</div>
    <div class="pos fs3" style="left: 128px; top: 531px; ">Granny Granny</div>
    <div class="pos fs3" style="left: 128px; top: 578px; ">Please combmy hair.</div>
    <div class="pos fs3" style="left: 128px; top: 625px; ">You always take your time,</div>
    <div class="pos fs3" style="left: 128px; top: 671px; ">You always take such care.</div>
    <div class="pos fs3" style="left: 128px; top: 738px; ">You putme to sit on a cushion</div>
    <div class="pos fs3" style="left: 128px; top: 784px; ">Between your knees;</div>
    <div class="pos fs3" style="left: 128px; top: 831px; ">You rub a little coconut oil,</div>
    <div class="pos fs3" style="left: 128px; top: 878px; ">Parting gentle as a breeze.</div>
    <div class="pos fs3" style="left: 128px; top: 944px; ">... Granny</div>
    <div class="pos fs3" style="left: 128px; top: 991px; ">You have all the time in the world,</div>
    <div class="pos fs3" style="left: 128px; top: 1038px; ">Andwhen you’re finished</div>
    <div class="pos fs3" style="left: 128px; top: 1085px; ">You always turnmyhead and say,</div>
    <div class="pos fs3" style="left: 128px; top: 1132px; ">“Now, who’s a nice girl?”</div>
    <div class="pos fs3" style="left: 124px; top: 1297px; color: #00FF00;">New words</div>
    <div class="pos fs3" style="left: 124px; top: 1344px; ">care, cushion, knees, gentle, breeze, world, nice</div>
    <div class="pos fs4" style="left: 770px; top: 1279px; ">– Grace Nichols</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000002.jpg');"> </div>
    <div class="pos fs5" style="left: 303px; top: 180px; color: #00A650;">Reading is fun</div>
    <div class="pos fs6" style="left: 197px; top: 237px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 197px; top: 296px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 197px; top: 354px; color: #00A650;">R</div>
    <div class="pos fs5" style="left: 231px; top: 239px; color: #231F20;">What does the little girl want her Granny to do?</div>
    <div class="pos fs5" style="left: 231px; top: 297px; color: #231F20;">What does Granny rub on her hair?</div>
    <div class="pos fs5" style="left: 231px; top: 356px; color: #231F20;">Does the little girl love her ‘Granny’?</div>
    <div class="pos fs5" style="left: 313px; top: 473px; color: #00A650;">Let’s talk</div>
    <div class="pos fs6" style="left: 197px; top: 530px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 197px; top: 588px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 197px; top: 647px; color: #00A650;">R</div>
    <div class="pos fit fs5" style="left: 231px; top: 531px; width: 600px; color: #231F20;">
      <span class="just">Do your grandparents live with you?</span>
    </div>
    <div class="pos fit fs5" style="left: 231px; top: 590px; width: 600px; color: #231F20;">
      <span class="just">Do you spend some time with them?</span>
    </div>
    <div class="pos fs5" style="left: 231px; top: 648px; color: #231F20;">How do you help them?</div>
    <div class="pos fs5" style="left: 329px; top: 825px; color: #00A650;">Let’s share</div>
    <div class="pos fs6" style="left: 197px; top: 901px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 197px; top: 960px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 197px; top: 1018px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 197px; top: 1077px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 197px; top: 1182px; color: #00A650;">R</div>
    <div class="pos fs7" style="left: 209px; top: 1325px; color: #231F20;">142</div>
    <div class="pos fs5" style="left: 231px; top: 903px; color: #231F20;">What do you call ‘Grandmother’ in your language?</div>
    <div class="pos fs5" style="left: 231px; top: 961px; color: #231F20;">What do you call ‘Grandfather’ in your language?</div>
    <div class="pos fs5" style="left: 231px; top: 1020px; color: #231F20;">Do you know any old people? Can you name them?</div>
    <div class="pos fs5" style="left: 231px; top: 1078px; color: #231F20;">How can you make them happy? Discuss</div>
    <div class="pos fs5" style="left: 231px; top: 1125px; color: #231F20;">with your class.</div>
    <div class="pos fs5" style="left: 231px; top: 1184px; color: #231F20;">Have you ever gone out with your grandparents?</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000003.jpg');"> </div>
    <div class="pos fs5" style="left: 262px; top: 188px; color: #00A650;">Word building</div>
    <div class="pos fs6" style="left: 129px; top: 230px; color: #00A650;">R</div>
    <div class="pos fs5" style="left: 163px; top: 231px; color: #231F20;">Make new words using letters from the word</div>
    <div class="pos fs5" style="left: 449px; top: 278px; color: #231F20;">. You may use the letters in any</div>
    <div class="pos fs5" style="left: 163px; top: 325px; color: #231F20;">order. One has been done for you.</div>
    <div class="pos fs8" style="left: 537px; top: 400px; color: #231F20;">Rent</div>
    <div class="pos fs5" style="left: 163px; top: 278px; color: #EC008C;">GRANDPARENTS</div>
    <div class="pos fs9" style="left: 448px; top: 673px; color: #EC008C;">GRANDPA RENT S</div>
    <div class="pos fs6" style="left: 129px; top: 838px; color: #00A650;">R</div>
    <div class="pos fs5" style="left: 163px; top: 840px; color: #231F20;">Sometimes we replace the names of people</div>
    <div class="pos fs5" style="left: 163px; top: 886px; color: #231F20;">with another word, instead of repeating</div>
    <div class="pos fs5" style="left: 260px; top: 933px; color: #231F20;">the name. Replace the names in the</div>
    <div class="pos fs5" style="left: 268px; top: 980px; color: #231F20;">sentences using a word from the box.</div>
    <div class="pos fs5" style="left: 442px; top: 1046px; color: #EC008C;">It, I, He, She, My, You</div>
    <div class="pos fs5" style="left: 163px; top: 1113px; color: #231F20;">1. Meena is playing with a doll. _____ is a girl.</div>
    <div class="pos fs5" style="left: 163px; top: 1171px; color: #231F20;">2. Ram is climbing a tree. _____ is a boy.</div>
    <div class="pos fs5" style="left: 163px; top: 1230px; color: #231F20;">3. Do not eat that mango. _____ is not ripe.</div>
    <div class="pos fs7" style="left: 949px; top: 1325px; color: #231F20;">143</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000004.jpg');"> </div>
    <div class="pos fs5" style="left: 329px; top: 189px; color: #00A650;">Let’s write</div>
    <div class="pos fs5" style="left: 197px; top: 267px; color: #231F20;">Suppose you go to stay with your grandparents</div>
    <div class="pos fs5" style="left: 197px; top: 313px; color: #231F20;">for a week. Make a list of things that you would</div>
    <div class="pos fs5" style="left: 197px; top: 360px; color: #231F20;">like to take with you for them. Do not forget to</div>
    <div class="pos fs5" style="left: 197px; top: 407px; color: #231F20;">put</div>
    <div class="pos fs5" style="left: 264px; top: 407px; color: #EC008C;" id="w1x">a,</div>
    <div class="pos fs5" style="left: 305px; top: 407px; color: #EC008C;" id="w2x">an</div>
    <div class="pos fs5" style="left: 402px; top: 407px; color: #EC008C;" id="w3x">the</div>
    <div class="pos fs5" style="left: 465px; top: 407px; color: #231F20;" id="w4x">before</div>
    <div class="pos fs5" style="left: 577px; top: 407px; color: #231F20;" id="w5x">each</div>
    <div class="pos fs5" style="left: 664px; top: 407px; color: #231F20;" id="w6x">thing.</div>
    <div class="pos fs5" style="left: 358px; top: 407px; color: #231F20;">or</div>
    <div class="pos fs5" style="left: 197px; top: 465px; color: #231F20;">_____________________</div>
    <div class="pos fs5" style="left: 197px; top: 524px; color: #231F20;">_____________________</div>
    <div class="pos fs5" style="left: 197px; top: 582px; color: #231F20;">_____________________</div>
    <div class="pos fs5" style="left: 197px; top: 641px; color: #231F20;">_____________________</div>
    <div class="pos fs5" style="left: 197px; top: 699px; color: #231F20;">_____________________</div>
    <div class="pos fs5" style="left: 666px; top: 464px; color: #231F20;">_____________________</div>
    <div class="pos fs5" style="left: 666px; top: 522px; color: #231F20;">_____________________</div>
    <div class="pos fs5" style="left: 666px; top: 581px; color: #231F20;">_____________________</div>
    <div class="pos fs5" style="left: 666px; top: 639px; color: #231F20;">_____________________</div>
    <div class="pos fs5" style="left: 666px; top: 698px; color: #231F20;">_____________________</div>
    <div class="pos fs5" style="left: 197px; top: 894px; color: #231F20;">Make a</div>
    <div class="pos fs5" style="left: 197px; top: 941px; color: #231F20;">grandparents.</div>
    <div class="pos fs5" style="left: 327px; top: 894px; color: #00AEEF;">Thank you</div>
    <div class="pos fs5" style="left: 457px; top: 1000px; color: #A3238E;">You need –</div>
    <div class="pos fs10" style="left: 536px; top: 813px; color: #FFFFFF;">Fun Time</div>
    <div class="pos fs5" style="left: 514px; top: 894px; color: #231F20;">card for your</div>
    <div class="pos fs11" style="left: 775px; top: 986px; -webkit-transform: rotate(2.0deg); color: #FFFFFF;">T</div>
    <div class="pos fs5" style="left: 457px; top: 1058px; color: #231F20;">Chart paper of any</div>
    <div class="pos fs5" style="left: 457px; top: 1105px; color: #231F20;">colour of 9 inches x</div>
    <div class="pos fs7" style="left: 209px; top: 1325px; color: #231F20;">144</div>
    <div class="pos fs11" style="left: 986px; top: 1043px; -webkit-transform: rotate(2.0deg); color: #FFFFFF;">Y</div>
    <div class="pos fs5" style="left: 457px; top: 1152px; color: #231F20;">12 inches, sketch pens, sticker,</div>
    <div class="pos fs5" style="left: 457px; top: 1199px; color: #231F20;">design block.</div>
    <div class="pos fs11" style="left: 1034px; top: 1045px; color: #FFFFFF;">o</div>
    <div class="pos fs11" style="left: 824px; top: 989px; -webkit-transform: rotate(10.0deg); color: #FFFFFF;">h</div>
    <div class="pos fs11" style="left: 870px; top: 998px; -webkit-transform: rotate(17.5deg); color: #FFFFFF;">a k</div>
    <div class="pos fs11" style="left: 920px; top: 1016px; -webkit-transform: rotate(31.0deg); color: #FFFFFF;">n</div>
    <div class="pos fs11" style="left: 1078px; top: 1046px; -webkit-transform: rotate(4.0deg); color: #FFFFFF;">u</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000005.jpg');"> </div>
    <div class="pos fs5" style="left: 129px; top: 189px; color: #A3238E;">Make it this way –</div>
    <div class="pos fs5" style="left: 129px; top: 247px; color: #231F20;">1. Fold the paper into two.</div>
    <div class="pos fs5" style="left: 129px; top: 306px; color: #231F20;">2. Draw or stick pictures on it.</div>
    <div class="pos fs5" style="left: 129px; top: 364px; color: #231F20;">3. Write a message inside.</div>
    <div class="pos fs5" style="left: 183px; top: 411px; color: #231F20;">Begin with</div>
    <div class="pos fs5" style="left: 368px; top: 411px; color: #F5821F;">Dear</div>
    <div class="pos fs5" style="left: 183px; top: 458px; color: #F5821F;">Grandfather/Grandmother</div>
    <div class="pos fs5" style="left: 129px; top: 575px; color: #00A650;">Activity time</div>
    <div class="pos fs5" style="left: 625px; top: 458px; color: #231F20;">.</div>
    <div class="pos fs5" style="left: 573px; top: 653px; color: #231F20;">This is an outline of</div>
    <div class="pos fs5" style="left: 573px; top: 699px; color: #231F20;">Meena’s grandfather. Add</div>
    <div class="pos fs5" style="left: 573px; top: 746px; color: #231F20;">his hat, tie, moustache,</div>
    <div class="pos fs5" style="left: 573px; top: 793px; color: #231F20;">glasses, stick, etc. Write</div>
    <div class="pos fs5" style="left: 573px; top: 840px; color: #231F20;">each word in the given</div>
    <div class="pos fs5" style="left: 573px; top: 887px; color: #231F20;">box. Colour the picture</div>
    <div class="pos fs5" style="left: 573px; top: 933px; color: #231F20;">and write his name.</div>
    <div class="pos fs7" style="left: 949px; top: 1325px; color: #231F20;">145</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000006.jpg');"> </div>
    <div class="pos fs12" style="left: 553px; top: 180px; color: #EC008C;">A Picnic</div>
    <div class="pos fs5" style="left: 197px; top: 249px; color: #231F20;">This is a family picnic scene. Give names to all the</div>
    <div class="pos fs5" style="left: 197px; top: 296px; color: #231F20;">people. Look at the picture and make sentences</div>
    <div class="pos fs5" style="left: 197px; top: 343px; color: #231F20;">using phrases given in the box.</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000007.jpg');"> </div>
    <div class="pos fs5" style="left: 222px; top: 218px; color: #231F20;">eating food together, picking up wrappers or</div>
    <div class="pos fs5" style="left: 226px; top: 265px; color: #231F20;">waste papers, serving food, playing football,</div>
    <div class="pos fs5" style="left: 180px; top: 312px; color: #231F20;">talking to elderly people, fishing with grandfather</div>
    <div class="pos fs7" style="left: 949px; top: 1325px; color: #231F20;">147</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000008.jpg');"> </div>
    <div class="pos fs13" style="left: 195px; top: 171px; color: #ED1651;">The Magic Porridge Pot</div>
    <div class="pos fs5" style="left: 197px; top: 268px; color: #231F20;">Once, there was</div>
    <div class="pos fs5" style="left: 197px; top: 314px; color: #231F20;">a little girl</div>
    <div class="pos fs5" style="left: 197px; top: 361px; color: #231F20;">named Tara. She lived</div>
    <div class="pos fs5" style="left: 197px; top: 408px; color: #231F20;">with her mother. They</div>
    <div class="pos fs5" style="left: 197px; top: 455px; color: #231F20;">were very poor.</div>
    <div class="pos fs5" style="left: 250px; top: 502px; color: #231F20;">One day she went to a</div>
    <div class="pos fs5" style="left: 197px; top: 548px; color: #231F20;">forest. There she met an</div>
    <div class="pos fs5" style="left: 197px; top: 595px; color: #231F20;">old woman. The old woman</div>
    <div class="pos fs5" style="left: 197px; top: 642px; color: #231F20;">gave her a pot. She said,</div>
    <div class="pos fs5" style="left: 197px; top: 689px; color: #231F20;">“This is a magic pot. It will cook</div>
    <div class="pos fs5" style="left: 197px; top: 736px; color: #231F20;">porridge for you when you say,</div>
    <div class="pos fs5" style="left: 197px; top: 782px; color: #231F20;">‘Cook-Pot-Cook’. It will stop making</div>
    <div class="pos fs5" style="left: 197px; top: 829px; color: #231F20;">porridge when you say, ‘Stop-Pot-Stop’.”</div>
    <div class="pos fs5" style="left: 250px; top: 876px; color: #231F20;">Tara was very happy. She ran to her mother</div>
    <div class="pos fs5" style="left: 197px; top: 923px; color: #231F20;">and said, “Mother, we will no longer be hungry</div>
    <div class="pos fs5" style="left: 197px; top: 970px; color: #231F20;">as I have got a magic pot.”</div>
    <div class="pos fs5" style="left: 250px; top: 1016px; color: #231F20;">Tara said to the pot, “Cook-Pot-Cook” and</div>
    <div class="pos fs5" style="left: 197px; top: 1063px; color: #231F20;">the pot cooked porridge. Her mother was very</div>
    <div class="pos fs5" style="left: 197px; top: 1110px; color: #231F20;">happy and they both ate porridge.</div>
    <div class="pos fs5" style="left: 250px; top: 1157px; color: #231F20;">One day, when Tara had gone out, her</div>
    <div class="pos fs5" style="left: 197px; top: 1204px; color: #231F20;">mother felt hungry. She said to the pot, “Cook-</div>
    <div class="pos fs5" style="left: 197px; top: 1250px; color: #231F20;">Pot-Cook.” The pot started cooking porridge.</div>
    <div class="pos fs7" style="left: 209px; top: 1325px; color: #231F20;">148</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000009.jpg');"> </div>
    <div class="pos fs5" style="left: 129px; top: 219px; color: #231F20;">After eating it her mother said, “Do not</div>
    <div class="pos fs5" style="left: 129px; top: 266px; color: #231F20;">cook Pot.” But the pot went on cooking.</div>
    <div class="pos fs5" style="left: 182px; top: 313px; color: #231F20;">Soon the porridge started spilling on the</div>
    <div class="pos fs5" style="left: 129px; top: 359px; color: #231F20;">floor. Mother called out again, “Wait – do not</div>
    <div class="pos fs5" style="left: 129px; top: 406px; color: #231F20;">cook anymore.” But the pot did not stop.</div>
    <div class="pos fs5" style="left: 182px; top: 453px; color: #231F20;">Mother ran out of the house and the porridge</div>
    <div class="pos fs5" style="left: 129px; top: 500px; color: #231F20;">followed her. Soon there was porridge everywhere.</div>
    <div class="pos fs5" style="left: 129px; top: 547px; color: #231F20;">The whole village saw it. They ran to eat the porridge.</div>
    <div class="pos fs5" style="left: 182px; top: 594px; color: #231F20;">When Tara came back, she saw that the road was</div>
    <div class="pos fs5" style="left: 129px; top: 640px; color: #231F20;">full of porridge. She ran home as fast as she could.</div>
    <div class="pos fs5" style="left: 129px; top: 687px; color: #231F20;">She heard her mother shout, “Tara the pot is cooking</div>
    <div class="pos fs5" style="left: 129px; top: 734px; color: #231F20;">and it will not stop.”</div>
    <div class="pos fs5" style="left: 182px; top: 781px; color: #231F20;">Tara called out, “Stop-Pot-Stop” and it stopped</div>
    <div class="pos fs5" style="left: 129px; top: 828px; color: #231F20;">cooking porridge.</div>
    <div class="pos fs14" style="left: 664px; top: 873px; color: #231F20;">– A folk tale</div>
    <div class="pos fs5" style="left: 129px; top: 928px; color: #00A650;">New words</div>
    <div class="pos fs5" style="left: 129px; top: 975px; color: #231F20;">magic, cook, porridge, spilling,</div>
    <div class="pos fs5" style="left: 129px; top: 1022px; color: #231F20;">whole, village</div>
    <div class="pos fs7" style="left: 949px; top: 1325px; color: #231F20;">149</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000010.jpg');"> </div>
    <div class="pos fs5" style="left: 299px; top: 180px; color: #00A650;">Reading is fun</div>
    <div class="pos fs6" style="left: 193px; top: 257px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 193px; top: 315px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 193px; top: 374px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 193px; top: 432px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 193px; top: 491px; color: #00A650;">R</div>
    <div class="pos fs5" style="left: 226px; top: 258px; color: #231F20;">Where did Tara go one day?</div>
    <div class="pos fs5" style="left: 226px; top: 317px; color: #231F20;">What did the old woman give Tara?</div>
    <div class="pos fs5" style="left: 226px; top: 375px; color: #231F20;">What did the magic pot cook?</div>
    <div class="pos fs5" style="left: 226px; top: 434px; color: #231F20;">Who said, “Do not cook Pot”?</div>
    <div class="pos fs5" style="left: 226px; top: 492px; color: #231F20;">Why was there so much porridge on the road?</div>
    <div class="pos fs5" style="left: 325px; top: 609px; color: #00A650;">Let’s talk</div>
    <div class="pos fs6" style="left: 193px; top: 695px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 193px; top: 754px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 193px; top: 812px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 193px; top: 871px; color: #00A650;">R</div>
    <div class="pos fs5" style="left: 226px; top: 697px; color: #231F20;">What do you eat for your breakfast?</div>
    <div class="pos fs5" style="left: 226px; top: 755px; color: #231F20;">Would you like to eat wheat porridge?</div>
    <div class="pos fs5" style="left: 226px; top: 814px; color: #231F20;">Have you seen any magic? Tell us about it.</div>
    <div class="pos fs5" style="left: 226px; top: 872px; color: #231F20;">What do you call magic in your own language?</div>
    <div class="pos fs5" style="left: 325px; top: 989px; color: #00A650;">Say aloud</div>
    <div class="pos fs5" style="left: 239px; top: 1067px; color: #231F20;">pot</div>
    <div class="pos fs5" style="left: 239px; top: 1126px; color: #231F20;">dot</div>
    <div class="pos fs5" style="left: 239px; top: 1184px; color: #231F20;">cot</div>
    <div class="pos fs5" style="left: 239px; top: 1243px; color: #231F20;">hot</div>
    <div class="pos fs7" style="left: 209px; top: 1325px; color: #231F20;">150</div>
    <div class="pos fs5" style="left: 434px; top: 1067px; color: #231F20;">caught</div>
    <div class="pos fs5" style="left: 434px; top: 1126px; color: #231F20;">bought</div>
    <div class="pos fs5" style="left: 434px; top: 1184px; color: #231F20;">taught</div>
    <div class="pos fs5" style="left: 434px; top: 1243px; color: #231F20;">fought</div>
    <div class="pos fs5" style="left: 674px; top: 1067px; color: #231F20;">cook</div>
    <div class="pos fs5" style="left: 674px; top: 1126px; color: #231F20;">hook</div>
    <div class="pos fs5" style="left: 674px; top: 1184px; color: #231F20;">book</div>
    <div class="pos fs5" style="left: 674px; top: 1243px; color: #231F20;">took</div>
    <div class="pos fs5" style="left: 879px; top: 1067px; color: #231F20;">boon</div>
    <div class="pos fs5" style="left: 879px; top: 1126px; color: #231F20;">soon</div>
    <div class="pos fs5" style="left: 879px; top: 1184px; color: #231F20;">moon</div>
    <div class="pos fs5" style="left: 879px; top: 1243px; color: #231F20;">spoon</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000011.jpg');"> </div>
    <div class="pos fs5" style="left: 258px; top: 180px; color: #00A650;">Let’s write</div>
    <div class="pos fs6" style="left: 126px; top: 257px; color: #00A650;">R</div>
    <div class="pos fs5" style="left: 159px; top: 257px; color: #231F20;">F</div>
    <div class="pos fs5" style="left: 181px; top: 257px; color: #231F20;">i</div>
    <div class="pos fs5" style="left: 191px; top: 257px; color: #231F20;">l</div>
    <div class="pos fs5" style="left: 201px; top: 257px; color: #231F20;">l</div>
    <div class="pos fs5" style="left: 222px; top: 257px; color: #231F20;" id="w1x">in</div>
    <div class="pos fs5" style="left: 265px; top: 257px; color: #231F20;" id="w2x">the</div>
    <div class="pos fs5" style="left: 328px; top: 257px; color: #231F20;" id="w3x">blanks</div>
    <div class="pos fs5" style="left: 450px; top: 257px; color: #231F20;" id="w4x">with</div>
    <div class="pos fs5" style="left: 552px; top: 257px; color: #231F20;" id="w5x">,</div>
    <div class="pos fs5" style="left: 532px; top: 257px; color: #00AEEF;" id="w6x">a</div>
    <div class="pos fs5" style="left: 573px; top: 257px; color: #00AEEF;" id="w7x">an</div>
    <div class="pos fs5" style="left: 670px; top: 257px; color: #00AEEF;" id="w8x">the</div>
    <div class="pos fs5" style="left: 626px; top: 258px; color: #231F20;">or</div>
    <div class="pos fs5" style="left: 126px; top: 336px; color: #231F20;">Once there was _____ beautiful garden.</div>
    <div class="pos fs5" style="left: 126px; top: 465px; color: #231F20;">_____ garden had _____ apple tree,</div>
    <div class="pos fs5" style="left: 126px; top: 594px; color: #231F20;">orange tree</div>
    <div class="pos fs5" style="left: 275px; top: 594px; color: #231F20;">and _____ guava tree.</div>
    <div class="pos fs5" style="left: 126px; top: 722px; color: #231F20;">In _____ garden lived _____ big giant.</div>
    <div class="pos fs5" style="left: 318px; top: 722px; color: #231F20;">_____ giant</div>
    <div class="pos fs5" style="left: 126px; top: 851px; color: #231F20;">did not like children to play in _____ garden.</div>
    <div class="pos fs5" style="left: 126px; top: 980px; color: #231F20;">____ giant used to eat _____ fruit every day from _____</div>
    <div class="pos fs5" style="left: 126px; top: 1108px; color: #231F20;">garden. But he did not share these with ____________</div>
    <div class="pos fs5" style="left: 126px; top: 1237px; color: #231F20;">children. So his garden dried up.</div>
    <div class="pos fs7" style="left: 949px; top: 1325px; color: #231F20;">151</div>
    <div class="pos fs5" style="left: 641px; top: 465px; color: #231F20;">_____</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000012.jpg');"> </div>
    <div class="pos fs5" style="left: 326px; top: 177px; color: #00A650;">Word building</div>
    <div class="pos fs6" style="left: 194px; top: 254px; color: #00A650;">R</div>
    <div class="pos fs5" style="left: 227px; top: 361px; color: #EC008C;">k</div>
    <div class="pos fs5" style="left: 227px; top: 419px; color: #EC008C;">p</div>
    <div class="pos fs5" style="left: 227px; top: 478px; color: #EC008C;">j</div>
    <div class="pos fs5" style="left: 227px; top: 536px; color: #EC008C;">t</div>
    <div class="pos fs5" style="left: 227px; top: 595px; color: #EC008C;">m</div>
    <div class="pos fs5" style="left: 227px; top: 255px; color: #231F20;">Look at the letters given below and make words</div>
    <div class="pos fs5" style="left: 227px; top: 302px; color: #231F20;">starting with them.</div>
    <div class="pos fs5" style="left: 315px; top: 361px; color: #231F20;" id="w1x">_____________</div>
    <div class="pos fs5" style="left: 550px; top: 361px; color: #231F20;" id="w2x">_____________</div>
    <div class="pos fs5" style="left: 800px; top: 361px; color: #231F20;" id="w3x">_____________</div>
    <div class="pos fs5" style="left: 315px; top: 419px; color: #231F20;" id="w4x">_____________</div>
    <div class="pos fs5" style="left: 550px; top: 419px; color: #231F20;" id="w5x">_____________</div>
    <div class="pos fs5" style="left: 800px; top: 419px; color: #231F20;" id="w6x">_____________</div>
    <div class="pos fs5" style="left: 315px; top: 478px; color: #231F20;" id="w7x">_____________</div>
    <div class="pos fs5" style="left: 550px; top: 478px; color: #231F20;" id="w8x">_____________</div>
    <div class="pos fs5" style="left: 800px; top: 478px; color: #231F20;" id="w9x">_____________</div>
    <div class="pos fs5" style="left: 315px; top: 536px; color: #231F20;" id="w10x">_____________</div>
    <div class="pos fs5" style="left: 550px; top: 536px; color: #231F20;" id="w11x">_____________</div>
    <div class="pos fs5" style="left: 800px; top: 536px; color: #231F20;" id="w12x">_____________</div>
    <div class="pos fs5" style="left: 315px; top: 595px; color: #231F20;" id="w13x">_____________</div>
    <div class="pos fs5" style="left: 550px; top: 595px; color: #231F20;" id="w14x">_____________</div>
    <div class="pos fs5" style="left: 800px; top: 595px; color: #231F20;" id="w15x">_____________</div>
    <div class="pos fs5" style="left: 300px; top: 712px; color: #00A650;">Let’s eat</div>
    <div class="pos fs6" style="left: 194px; top: 788px; color: #00A650;">R</div>
    <div class="pos fs6" style="left: 194px; top: 894px; color: #00A650;">R</div>
    <div class="pos fs15" style="left: 194px; top: 954px; color: #A54686;">You need –</div>
    <div class="pos fs5" style="left: 227px; top: 790px; color: #231F20;">Your mother makes delicious porridge. Would</div>
    <div class="pos fs5" style="left: 227px; top: 836px; color: #231F20;">you also like to learn how to make it?</div>
    <div class="pos fs5" style="left: 227px; top: 895px; color: #231F20;">Ask your mother or an older person to help you.</div>
    <div class="pos fs5" style="left: 393px; top: 955px; color: #231F20;">Milk, porridge, sugar</div>
    <div class="pos fs5" style="left: 221px; top: 1033px; color: #231F20;">1. Take hot milk.</div>
    <div class="pos fs5" style="left: 672px; top: 1111px; color: #231F20;">2. Add cooked porridge.</div>
    <div class="pos fs5" style="left: 221px; top: 1189px; color: #231F20;">3. Add sugar and eat it.</div>
    <div class="pos fs5" style="left: 194px; top: 1247px; color: #231F20;">Repeat this recipe in your language to the class.</div>
    <div class="pos fs7" style="left: 209px; top: 1325px; color: #231F20;">152</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000013.jpg');"> </div>
    <div class="pos fs6" style="left: 126px; top: 179px; color: #00A650;">R</div>
    <div class="pos fs5" style="left: 159px; top: 180px; color: #231F20;">It is your friend’s birthday. You are presenting a</div>
    <div class="pos fs5" style="left: 159px; top: 227px; color: #231F20;">bunch of balloons to her to decorate in her party.</div>
    <div class="pos fs5" style="left: 159px; top: 274px; color: #231F20;">These balloons are your friendship balloons.</div>
    <div class="pos fs5" style="left: 159px; top: 320px; color: #231F20;">Choose a name for each from the box and</div>
    <div class="pos fs5" style="left: 159px; top: 367px; color: #231F20;">colour them.</div>
    <div class="pos fs16" style="left: 606px; top: 396px; color: #231F20;">5</div>
    <div class="pos fs16" style="left: 495px; top: 454px; color: #231F20;">1</div>
    <div class="pos fs16" style="left: 234px; top: 553px; color: #231F20;">4</div>
    <div class="pos fs16" style="left: 592px; top: 622px; color: #231F20;">7</div>
    <div class="pos fs16" style="left: 144px; top: 694px; color: #231F20;">3</div>
    <div class="pos fs16" style="left: 557px; top: 819px; color: #231F20;">6</div>
    <div class="pos fs5" style="left: 159px; top: 952px; color: #2E3092;">caring</div>
    <div class="pos fs5" style="left: 248px; top: 1011px; color: #2E3092;">helpful</div>
    <div class="pos fs6" style="left: 126px; top: 1126px; color: #00A650;">R</div>
    <div class="pos fs5" style="left: 384px; top: 952px; color: #2E3092;">sharing</div>
    <div class="pos fs5" style="left: 586px; top: 952px; color: #2E3092;">kind</div>
    <div class="pos fs5" style="left: 476px; top: 1011px; color: #2E3092;">forgiving truthful</div>
    <div class="pos fs5" style="left: 159px; top: 1128px; color: #231F20;">Would you like to tell the class a story about any</div>
    <div class="pos fs5" style="left: 159px; top: 1175px; color: #231F20;">one of these balloons?</div>
    <div class="pos fs7" style="left: 949px; top: 1325px; color: #231F20;">153</div>
    <div class="pos fs5" style="left: 796px; top: 952px; color: #2E3092;">friendly</div>
    <div class="pos fs16" style="left: 709px; top: 639px; color: #231F20;">2</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000014.jpg');"> </div>
    <div class="pos fs17" style="left: 414px; top: 188px; color: #3349F8;">Loving Grandfather</div>
    <div class="pos fs18" style="left: 210px; top: 1333px; ">154</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000015.jpg');"> </div>
    <div class="pos fs19" style="left: 985px; top: 241px; color: #FFFFFF;">4</div>
    <div class="pos fs20" style="left: 350px; top: 323px; color: #231F20;">Grandpa loved to go</div>
    <div class="pos fs20" style="left: 350px; top: 348px; color: #231F20;">for walks. He loved to</div>
    <div class="pos fs20" style="left: 350px; top: 374px; color: #231F20;">do his exercises!</div>
    <div class="pos fs19" style="left: 140px; top: 561px; color: #FFFFFF;">5</div>
    <div class="pos fs20" style="left: 251px; top: 666px; color: #231F20;">I can see grandpa’s</div>
    <div class="pos fs20" style="left: 251px; top: 691px; color: #231F20;">friends. Let us talk</div>
    <div class="pos fs20" style="left: 251px; top: 717px; color: #231F20;">to them!</div>
    <div class="pos fs20" style="left: 802px; top: 619px; color: #231F20;">We must try to make</div>
    <div class="pos fs20" style="left: 802px; top: 645px; color: #231F20;">him go for a walk</div>
    <div class="pos fs20" style="left: 802px; top: 670px; color: #231F20;">with us and do some</div>
    <div class="pos fs20" style="left: 802px; top: 696px; color: #231F20;">exercise in the park!</div>
    <div class="pos fs20" style="left: 656px; top: 773px; color: #231F20;">Thank you, my children,</div>
    <div class="pos fs20" style="left: 656px; top: 799px; color: #231F20;">for taking me out in the</div>
    <div class="pos fs20" style="left: 656px; top: 824px; color: #231F20;">fresh air. It’s been a long</div>
    <div class="pos fs20" style="left: 656px; top: 849px; color: #231F20;">time I have been with my</div>
    <div class="pos fs20" style="left: 656px; top: 875px; color: #231F20;">friends or for a walk.</div>
    <div class="pos fs19" style="left: 985px; top: 920px; color: #FFFFFF;">6</div>
    <div class="pos fit fs20" style="left: 299px; top: 975px; width: 178px; color: #231F20;">
      <span class="just">Grandpa, your friends</span>
    </div>
    <div class="pos fit fs20" style="left: 299px; top: 1001px; width: 178px; color: #231F20;">
      <span class="just">are here. Talk to them</span>
    </div>
    <div class="pos fs20" style="left: 299px; top: 1026px; color: #231F20;">while we play. They</div>
    <div class="pos fs20" style="left: 299px; top: 1052px; color: #231F20;">have missed you!</div>
    <div class="pos fs7" style="left: 949px; top: 1325px; color: #231F20;">155</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000016.jpg');"> </div>
    <div class="pos fs5" style="left: 300px; top: 180px; color: #00A650;">Let’s read</div>
    <div class="pos fs6" style="left: 194px; top: 257px; color: #00A650;">R</div>
    <div class="pos fs5" style="left: 228px; top: 258px; color: #231F20;">Read the picture story aloud (3 students).</div>
    <div class="pos fs6" style="left: 194px; top: 452px; color: #00A650;">R</div>
    <div class="pos fs5" style="left: 273px; top: 570px; color: #00AEEF;">(a)</div>
    <div class="pos fs5" style="left: 228px; top: 453px; color: #231F20;" id="w1x">Tick</div>
    <div class="pos fs5" style="left: 309px; top: 453px; color: #231F20;" id="w2x">(</div>
    <div class="pos fs5" style="left: 338px; top: 453px; color: #231F20;" id="w3x">)</div>
    <div class="pos fs5" style="left: 359px; top: 453px; color: #231F20;" id="w4x">the</div>
    <div class="pos fs5" style="left: 422px; top: 453px; color: #231F20;" id="w5x">right</div>
    <div class="pos fs5" style="left: 511px; top: 453px; color: #231F20;" id="w6x">answer.</div>
    <div class="pos fs5" style="left: 228px; top: 512px; color: #231F20;">1. Grandfather sprained his</div>
    <div class="pos fs5" style="left: 321px; top: 570px; color: #231F20;">leg</div>
    <div class="pos fs5" style="left: 311px; top: 375px; color: #00A650;">Reading is fun</div>
    <div class="pos fs21" style="left: 319px; top: 461px; color: #231F20;">?</div>
    <div class="pos fs5 " style="left: 518px; top: 570px; color: #231F20;">
      <div class="just"><span style="color: #00AEEF;">(b)</span> back</div>
    </div>
    <div class="pos fs5" style="left: 228px; top: 648px; color: #231F20;">2. Grandfather loved</div>
    <div class="pos fs5" style="left: 323px; top: 707px; color: #231F20;">eating</div>
    <div class="pos fs5" style="left: 273px; top: 707px; color: #00AEEF;">(a)</div>
    <div class="pos fs5" style="left: 273px; top: 843px; color: #00AEEF;">(a)</div>
    <div class="pos fs5 " style="left: 516px; top: 707px; color: #231F20;">
      <div class="just"><span style="color: #00AEEF;">(b)</span> walking</div>
    </div>
    <div class="pos fs5" style="left: 228px; top: 785px; color: #231F20;">3. Grandfather missed his</div>
    <div class="pos fs5" style="left: 325px; top: 843px; color: #231F20;">food</div>
    <div class="pos fs5" style="left: 228px; top: 921px; color: #231F20;">Before Grandfather fell</div>
    <div class="pos fs5" style="left: 228px; top: 980px; color: #231F20;">Grandfather used to go</div>
    <div class="pos fs5" style="left: 228px; top: 1026px; color: #231F20;">for walks.</div>
    <div class="pos fs5" style="left: 228px; top: 1085px; color: #231F20;">Grandfather used to talk. Now he is ____________.</div>
    <div class="pos fs5" style="left: 228px; top: 1143px; color: #231F20;">Grandfather met many</div>
    <div class="pos fs5" style="left: 228px; top: 1190px; color: #231F20;">friends.</div>
    <div class="pos fs7" style="left: 209px; top: 1325px; color: #231F20;">156</div>
    <div class="pos fs5" style="left: 691px; top: 1143px; color: #231F20;">Now he does not _____</div>
    <div class="pos fs5" style="left: 691px; top: 1190px; color: #231F20;">_____________.</div>
    <div class="pos fs5 " style="left: 518px; top: 843px; color: #231F20;">
      <div class="just"><span style="color: #00AEEF;">(b)</span> books</div>
    </div>
    <div class="pos fs5 " style="left: 774px; top: 570px; color: #231F20;">
      <div class="just"><span style="color: #00AEEF;">(c)</span> arm</div>
    </div>
    <div class="pos fs5 fit " style="left: 774px; top: 707px; width: 187px; color: #231F20;">
      <div class="just"><span style="color: #00AEEF;">(c)</span> sleeping</div>
    </div>
    <div class="pos fs5 fit " style="left: 772px; top: 843px; width: 188px; color: #231F20;">
      <div class="just"><span style="color: #00AEEF;">(c)</span> exercise</div>
    </div>
    <div class="pos fs5" style="left: 691px; top: 921px; color: #231F20;">After he fell</div>
    <div class="pos fs5" style="left: 691px; top: 980px; color: #231F20;">Now he ______________.</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000017.jpg');"> </div>
    <div class="pos fs15" style="left: 131px; top: 178px; color: #231F20;">Let’s practise</div>
    <div class="pos fs22" style="left: 964px; top: 336px; color: #231F20;">.</div>
    <div class="pos fs22" style="left: 964px; top: 537px; color: #231F20;">.</div>
    <div class="pos fs22" style="left: 964px; top: 736px; color: #231F20;">.</div>
    <div class="pos fs22" style="left: 964px; top: 938px; color: #231F20;">.</div>
    <div class="pos fs22" style="left: 964px; top: 1139px; color: #231F20;">.</div>
    <div class="pos fs7" style="left: 949px; top: 1325px; color: #231F20;">157</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000018.jpg');"> </div>
    <div class="pos fs23" style="left: 200px; top: 169px; color: #231F20;">Teacher's Pages</div>
    <div class="pos fs19" style="left: 828px; top: 230px; color: #231F20;">Unit-9</div>
    <div class="pos fs24 fit " style="left: 197px; top: 338px; width: 763px; color: #231F20;">
      <div class="just">The main emphasis of<span style="font-family: 'pzzkna_bookman_demi';"> Unit 9</span> is to further sensitise children to their home</div>
    </div>
    <div class="pos fit fs24" style="left: 197px; top: 368px; width: 763px; color: #231F20;">
      <span class="just">environment. Unit -1 began with 'myself' and 'my own feelings for the world.' A</span>
    </div>
    <div class="pos fit fs24" style="left: 197px; top: 396px; width: 763px; color: #231F20;">
      <span class="just">gradual shift in the child's engagement with experiences, thoughts, feelings</span>
    </div>
    <div class="pos fit fs24" style="left: 197px; top: 425px; width: 763px; color: #231F20;">
      <span class="just">and relationships with her/his immediate family and friends has been subtly</span>
    </div>
    <div class="pos fs24" style="left: 197px; top: 453px; color: #231F20;">built in from Unit to Unit.</div>
    <div class="pos fs26" style="left: 197px; top: 497px; color: #EC008C;">l</div>
    <div class="pos fit fs24" style="left: 223px; top: 494px; width: 736px; color: #231F20;">
      <span class="just">Love for elders and sharing and caring for the old is a feeling which when</span>
    </div>
    <div class="pos fit fs24" style="left: 223px; top: 522px; width: 736px; color: #231F20;">
      <span class="just">generated at this young age will go a long way to give children a feeling of</span>
    </div>
    <div class="pos fit fs24" style="left: 223px; top: 551px; width: 736px; color: #231F20;">
      <span class="just">interdependence with their world. It is when this true feeling is expressed</span>
    </div>
    <div class="pos fit fs24" style="left: 223px; top: 580px; width: 736px; color: #231F20;">
      <span class="just">that language will flow (so the emphasis is on language building and not</span>
    </div>
    <div class="pos fs24" style="left: 223px; top: 608px; color: #231F20;">translation).</div>
    <div class="pos fs26" style="left: 197px; top: 651px; color: #EC008C;">l</div>
    <div class="pos fs26" style="left: 197px; top: 721px; color: #EC008C;">l</div>
    <div class="pos fs26" style="left: 197px; top: 761px; color: #EC008C;">l</div>
    <div class="pos fs26" style="left: 197px; top: 830px; color: #EC008C;">l</div>
    <div class="pos fs24" style="left: 223px; top: 649px; color: #231F20;">Motivate the children to read short stories and speak/narrate in their</div>
    <div class="pos fs24" style="left: 223px; top: 677px; color: #231F20;">own words.</div>
    <div class="pos fs24" style="left: 223px; top: 718px; color: #231F20;">Encourage them to look at pictures and talk about them.</div>
    <div class="pos fs24" style="left: 223px; top: 758px; color: #231F20;">Read the story/poem aloud with proper stress, punctuation, rhythm and</div>
    <div class="pos fs24" style="left: 223px; top: 787px; color: #231F20;">intonation, as the children repeat after you.</div>
    <div class="pos fit fs24" style="left: 223px; top: 828px; width: 736px; color: #231F20;">
      <span class="just">Most teachers will recognise that one child’s speech is different from that of</span>
    </div>
    <div class="pos fit fs24" style="left: 223px; top: 856px; width: 736px; color: #231F20;">
      <span class="just">other children. Perhaps a child hesitates, stutters, or blocks when</span>
    </div>
    <div class="pos fit fs24" style="left: 223px; top: 885px; width: 736px; color: #231F20;">
      <span class="just">attempting to communicate. The rhymes and stories in this book will give</span>
    </div>
    <div class="pos fs24" style="left: 223px; top: 913px; color: #231F20;">the child many successful speech experiences through choric speaking.</div>
    <div class="pos fs26" style="left: 197px; top: 957px; color: #EC008C;">l</div>
    <div class="pos fs26" style="left: 197px; top: 998px; color: #EC008C;">l</div>
    <div class="pos fs26" style="left: 197px; top: 1067px; color: #EC008C;">l</div>
    <div class="pos fs24 " style="left: 223px; top: 954px; color: #231F20;">
      <div class="just">In<span style="font-family: 'pzzkna_bookman_demi';"> Say aloud</span> put stress on the underlined syllables.</div>
    </div>
    <div class="pos fs24" style="left: 223px; top: 995px; color: #231F20;">By this Unit children should be able to do independent reading and</div>
    <div class="pos fs24" style="left: 223px; top: 1024px; color: #231F20;">comprehend the story.</div>
    <div class="pos fit fs24" style="left: 223px; top: 1064px; width: 736px; color: #231F20;">
      <span class="just">Use of repetitive sounds or words like ‘cook-pot-cook’ is a source of joy for</span>
    </div>
    <div class="pos fit fs24" style="left: 223px; top: 1093px; width: 736px; color: #231F20;">
      <span class="just">the child, who may overcome timidity and speech difficulties easily and</span>
    </div>
    <div class="pos fs24" style="left: 223px; top: 1122px; color: #231F20;">gradually.</div>
    <div class="pos fs26" style="left: 197px; top: 1165px; color: #EC008C;">l</div>
    <div class="pos fs26" style="left: 197px; top: 1205px; color: #EC008C;">l</div>
    <div class="pos fs24" style="left: 223px; top: 1162px; color: #231F20;">Put a word-chart of spellings from the lesson on the wall/board.</div>
    <div class="pos fs24" style="left: 223px; top: 1203px; color: #231F20;">Ask children to think of a time when they were sick and who looked</div>
    <div class="pos fs24" style="left: 223px; top: 1231px; color: #231F20;">after them.</div>
    <div class="pos fs7" style="left: 209px; top: 1325px; color: #231F20;">158</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1212, height=1568"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1212px; height:1568px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1212px; height: 1568px; background-image: url('page_000019.jpg');"> </div>
    <div class="pos fs26" style="left: 128px; top: 183px; color: #EC008C;">l</div>
    <div class="pos fs24 " style="left: 155px; top: 180px; color: #231F20;">
      <div class="just">The chart paper used for the Thank you card in<span style="font-family: 'pzzkna_bookman_demi';"> Fun time</span> should be 9 x 12</div>
    </div>
    <div class="pos fs24" style="left: 155px; top: 209px; color: #231F20;">inches in size.</div>
    <div class="pos fs25" style="left: 128px; top: 269px; color: #EC008C;">A few classroom suggestions</div>
    <div class="pos fs26" style="left: 128px; top: 313px; color: #EC008C;">l</div>
    <div class="pos fs26" style="left: 128px; top: 382px; color: #EC008C;">l</div>
    <div class="pos fs26" style="left: 128px; top: 451px; color: #EC008C;">l</div>
    <div class="pos fs24" style="left: 155px; top: 310px; color: #231F20;">Ask the children to share their feelings about any incident/story they know</div>
    <div class="pos fs24" style="left: 155px; top: 339px; color: #231F20;">where an animal has helped old and sick people.</div>
    <div class="pos fs24" style="left: 155px; top: 379px; color: #231F20;">Cards should be made and given to grandparents or older people whom</div>
    <div class="pos fs24" style="left: 155px; top: 408px; color: #231F20;">they like.</div>
    <div class="pos fit fs24" style="left: 155px; top: 448px; width: 736px; color: #231F20;">
      <span class="just">Vocabulary games must be encouraged and played, e.g., teachers could</span>
    </div>
    <div class="pos fit fs24" style="left: 155px; top: 477px; width: 736px; color: #231F20;">
      <span class="just">write four capital letters on the board. Each student chooses one and copies</span>
    </div>
    <div class="pos fit fs24" style="left: 154px; top: 506px; width: 736px; color: #231F20;">
      <span class="just">it down. Now write 3-4 lower case letters on the board. Again, let each child</span>
    </div>
    <div class="pos fit fs24" style="left: 154px; top: 534px; width: 736px; color: #231F20;">
      <span class="just">choose one. Continue writing 3-4 letters at a time. Let students complete</span>
    </div>
    <div class="pos fit fs24" style="left: 154px; top: 563px; width: 736px; color: #231F20;">
      <span class="just">the words. Each time they complete one word, they may start a new word</span>
    </div>
    <div class="pos fs24" style="left: 154px; top: 591px; color: #231F20;">(using a capital letter).</div>
    <div class="pos fs26" style="left: 128px; top: 635px; color: #EC008C;">l</div>
    <div class="pos fit fs24" style="left: 154px; top: 632px; width: 736px; color: #231F20;">
      <span class="just">The teacher can act out the picture story along with the students in a group.</span>
    </div>
    <div class="pos fit fs24" style="left: 154px; top: 661px; width: 736px; color: #231F20;">
      <span class="just">Then students can enact it independently. This can be done following the</span>
    </div>
    <div class="pos fit fs24" style="left: 154px; top: 689px; width: 736px; color: #231F20;">
      <span class="just">red, blue, green, yellow group method. Dramatisation can bring a story or</span>
    </div>
    <div class="pos fs24" style="left: 154px; top: 718px; color: #231F20;">poem to life.</div>
    <div class="pos fs25" style="left: 128px; top: 778px; color: #EC008C;">Raising awareness</div>
    <div class="pos fs24" style="left: 128px; top: 819px; color: #231F20;">Invite grandparents to the school. Ask them to talk about their school days</div>
    <div class="pos fs24" style="left: 128px; top: 848px; color: #231F20;">with the children.</div>
    <div class="pos fs7" style="left: 949px; top: 1325px; color: #231F20;">159</div>
  </body>
</html>
