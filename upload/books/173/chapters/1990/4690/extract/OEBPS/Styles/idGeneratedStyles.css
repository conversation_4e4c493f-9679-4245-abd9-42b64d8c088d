body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
font-family:"Walkman-Chanakya-905";

font-style:normal;


}
* {
margin:0;
padding:0;
}
@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:bold;

src : url("../Fonts/wcb.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:normal;

src : url("../Fonts/wcn.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}
body#hindi, div#hindi, body#eng-hin .hindi-chracter-style
{
font-family: "Walkman-Chanakya-905";
font-size:120%;
line-height:150%;
}
body#hindi .char-style-override-1
{
font-family:arial;
}
/* Hightlisght Boxes */
.box, .akd-1{
background-color:rgb(234,241,202);
padding: 15px;
font-size:0.9em;
line-height:150%;
}
img
{
margin-left: auto;
margin-right: auto;
max-width:100%;
margin:5px;
}
h4,.caption 
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
}
/* Chapter Name */

h1
{
color:#2B2E34 ;
font-size: 1.5em;
padding:10px;
background:#C5EFFC;
}

h2
{
color: #00B8F1;
font-size: 1.3em;
padding:10px;

}
/* Chapter number */
h3
{
color:#00B8F1;
font-size: 1.1em;
padding:10px;
}
/* Concept Heading */
.ConceptHeading, .Heading, .Heading-hindi, .Example-style
{
color:rgb(54,182,75);
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading, .Heading-2-hindi, .box-text-heading-hindi, .Heading-2, .Box-text-heading-style
{
color:rgb(54,182,75);
font-size:1em;
font-weight:bold;

}
/* Sub Heading 2*/
.SubHeading2
{
color:#CC0000;
font-size:1.1em;
font-weight:bold;
}
.white
{
border:2px solid #FB3099 ;
padding:25px;
width:100px;
margin-left:45%;
border-radius:0px;
background:#FFFFFF;

}
.pink
{
border:2px solid #FB3099 ;
padding:15px;
border-radius:0px;
background:#FDD8E8;
}
.white2
{
border:2px solid black ;
padding:15px;
border-radius:0px;
background: #EBECEC ;
}

p.resize img, .resize img

{


position:relative;

top:25px;

}

p.resize1 img, .resize1 img

{


position:relative;

top:10px;

}

p.resize2 img, .resize2 img

{


position:relative;

top:30px;

}

p.resize3 img, .resize3 img

{


position:relative;

top:20px;

}

p.resize4 img, .resize4 img

{

height:50px;

position:relative;

top:15px;

} 

.lining_box3
{
border:2px solid #A9E8FA;
padding:15px;
border-radius:15px;
background: #ffffff ;
}
.blue
{
border:4px solid #C5EFFC  ;
padding:15px;
border-radius:0px;
background: #FFFFFF ;
}
.CharOverride-16,.CharOverride-21{

color:#00B8F1;
}
.lining_box2
{
border:2px solid #c11667 ;
padding:15px;
border-radius:15px;
background: #D6DFD4 ;
}
.img_ryt
{
float:right;
}
.img_lft
{
float:left;
}

p
{
margin-top:10px;
}
.bold, .bold-hinid, .Example-head-2, .heading-3, .bold---italic-english
{
font-weight:bold;
}
.bold---italic-english
{
font-style:italic;
}
.Colour-Bullets
{
font-size:70%;
color:rgb(96,145,96);
}

.englishMeaning, .char-style-override-6, .char-style-override-27, .CharOverride-13
{
font-family:arial;
font-size:0.8em;
}
.char-style-override-25, .char-style-override-26
{
COLOR:rgb(251,53,155);
}
table
{
	width:100%;
	border:1px solid #000;
	border-collapse:collapse;
background:#C5EFFC;
}
td
{
	padding:10px;
	border:1px solid #000;
	border-collapse:collapse;
}
.Box-text-style-bullet-2
{
margin-left:60px;
}
.color-hindi
{
color:#41924B;
}
.Italic
{
font-style:italic;
}
.activity-style
{
color:#706627;
font-weight:bold;
font-size:1.3em;
}
table td.Cell-Style-Head-english
{
background:#72BF92;
color:#fff;
font-weight:bold;
}
.clear
{
clear:both;
}
.golden
{
color:#706627;
font-weight:bold;
}
.cover_img_small

{
width:50%;
}
div.layout


{


text-align: center;


}


div.chapter_pos



{



text-align: center;



width: 96%;



position:absolute;



top:70%;



font-weight:bold;



font-size:28px;



color:#fff;



}



div.chapter_pos div



{



background:#597741;



padding:10px;



width:30%;



margin:auto;


opacity:0.9;



}



div.chapter_pos div span



{



font-size:0.7em;



color:#eaeaea;



font-weight:normal;



}


@media only screen and (max-width: 767px) {



div.chapter_pos



{



font-size:0.8em;


line-height:120%;


top:50%;


}



div.chapter_pos div span



{



font-size:0.7em;



}


}