@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
	font-size:120%;
	line-height:150%;
	padding:2%;
	text-align:justify;
}

.chapterHeading {
	text-align:center;
	font-size:140%;
	font-weight:bold;
}

.chapterNumber {
	font-size:36px;
	font-weight:bold;
}
.paragraph_1
{
	margin-left:5%;
	font-weight:bold;
}
.paragraph_2
{
	margin-left:20%;
	font-weight:bold;
}

.blueb{
color:blue;
font-weight:bold;
}
.subHeading {
	font-size:120%;
	color:#fff;
	margin-bottom:1%;
	font-weight:bold;
}
.violet{
color:rgb(150,67,155);
margin:4% 0 1% 0;
font-weight:bold;
text-align:center;
}
.orange{
background:#FC6;
font-weight:bold;
}
.center{
	color:rgb(242,36,0);
	font-size:150%;
	font-weight:bold;
	text-align:center;
}
.pink{
	color:rgb(242,36,186);
	font-size:120%;
	font-weight:bold;
	text-align:center;
}
.right{
	text-align:right;
}

.green{
		color:green;
	font-size:120%;
	font-weight:bold;
	text-align:center;
}
.meaning {
	font-size:95%;
}
.underline{
	text-decoration:underline;
}

.image {
	text-align:center;
}

.activity {
	background: #e1def0;
	padding:2%;
}

.englishMeaning{
	font-family:Arial, Helvetica, sans-serif;
	font-size:70%;
}
/* Cover page band */
div.layout
{
  text-align: center;
}
div.chapter_pos
{
text-align: center;
width: 96%;
position:absolute;
top:50%;
line-height:110%;
font-weight:bold;
font-size:180%;
color:#fff;
}
div.chapter_pos div
{
background:#266A2E;
padding:10px;
width:40%;
margin:auto;
opacity:0.9;
}
div.chapter_pos div span
{
font-size:0.7em;
color:#eaeaea;
font-weight:normal;
}
#cover img
{
	margin:auto;
	width:65%;
}
#others img
{
	margin-left: auto;
	margin-right: auto;
	display: block;
	width:50%;
}
#others .img_wid
{
	margin-left: auto;
	margin-right: auto;
	display: block;
	width:90%;
}
/* if Mathematics or science book use */
#MathSc img
{
	position:relative;
	top:15px;
}
#MathSc .img1
{
	position:relative;
	top:5px;
}
#MathSc .img2
{
	position:relative;
	top:20px;
}
#MathSc .img3
{
	position:relative;
	top:0px;
}
#MathSc .img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.clear
{
	clear:both;
}
.lining_box
{
border:2px solid #00aeef;
padding:15px;
border-radius:15px;
}

ul
{
margin-left:45px;
}

.caption
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
text-align:center;
}
p
{
margin-top:10px;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:#00aeef;
padding:10px;
}
/* Chapter number */
h4
{
color:#00aeef;
font-size:1.3em;
}
/* Concept Heading */
.ConceptHeading
{
color:#d1640f;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading
{
color:#d1640f;
font-size:1.1em;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
.footer
{
display:none;
}
table td
{
padding:10px;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
margin: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
margin: 15px;
font-size:0.9em;
line-height:120%;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
margin: 15px;
font-size:0.9em;
line-height:120%;
}
/* Hightlight Boxes Heading : CSS given directly to <b> tag*/
.NewWordBox b, .activityBox b, .box b 
{
	font-weight:bold;
	font-size:1.2em;
}
/* Hightlight Boxes Sub Heading */
.NewWordBox .Subheading, .activityBox .Subheading, .box .Subheading 
{
	font-weight:bold;
	font-size:1em;
}
.underline_txt
{
font-decoration:underline;
}

.bold_txt
{
font-weight:bold;
}

.center_element
{
margin:auto;
}
.italics_txt
{
font-style:italic;
}
.block_element
{
display:block;
}
.img_rt
{
float:right;
clear:both;
}
.img_lft
{
float:left;
}
.img_jst
{
    float: justify;
    font-style: italic;
}
#prelims .char-style-override-29
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-20
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
#prelims img
{
	width:100%;
}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:30%;
font-size:1em;
}
div.chapter_pos div span
{
font-size:0.5em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}