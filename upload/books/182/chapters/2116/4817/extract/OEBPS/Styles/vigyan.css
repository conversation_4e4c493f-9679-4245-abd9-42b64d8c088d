@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
	font-size:120%;
	line-height:150%;
	padding:2%;
	text-align:justify;
}


.chapterHeading {
	text-align:center;
	font-weight:bold;
	font-size:200%;
	color:#360;
}

.chapterSubheading {
	text-align:left;
	font-weight:bold;
	font-size:140%;
	color:#03F;
}


.bold {
	font-weight:bold;
}

.chapterImage {
	height:100px;
	width:100px;
	float:right;
	margin-left:1%;
}

.chapter {
	text-align:left;
	font-weight:bold;
	font-size:130%;
	color:#C85C44;
}

.chapterNumber {
	text-align:left;
	font-weight:bold;
	font-size:150%;
	color:gray;
}

.chapterImage:after {
	clear:both;
}

.image {
	text-align: center;
}

.center {
	text-align: center;
	font-family: "Walkman-Chanakya-905";
}

.left {
	float:left;
}

.right {
	float:right;
}

.numbers {
	text-align:center;
	font-size:120%;
	font-weight:bold;
}

.englishMeaning
{
	font-family:Arial, Helvetica, sans-serif;
	font-size:80%;
	}
/* Cover page band */
div.layout
{
  text-align: center;
}
div.chapter_pos
{
text-align: center;
width: 96%;
position:absolute;
top:50%;
line-height:110%;
font-weight:bold;
font-size:180%;
color:#fff;
}
div.chapter_pos div
{
background:#8B8AB9;
padding:10px;
width:40%;
margin:auto;
opacity:0.9;
}
div.chapter_pos div span
{
font-size:0.7em;
color:#eaeaea;
font-weight:normal;
}
#cover img
{
	margin:auto;
	width:65%;
}
#others img
{
	margin-left: auto;
	margin-right: auto;
	display: block;
	width:50%;
}
#others .img_wid
{
	margin-left: auto;
	margin-right: auto;
	display: block;
	width:90%;
}
/* if Mathematics or science book use */
#MathSc img
{
	position:relative;
	top:15px;
}
#MathSc .img1
{
	position:relative;
	top:5px;
}
#MathSc .img2
{
	position:relative;
	top:20px;
}
#MathSc .img3
{
	position:relative;
	top:0px;
}
#MathSc .img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.clear
{
	clear:both;
}
.lining_box
{
border:2px solid #000;
padding:15px;
border-radius:15px;
background-color:#CCEEEC;
}

ul
{
margin-left:45px;
}

.caption
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
text-align:center;
}
p
{
margin-top:10px;
}
.footer
{
display:none;
}
table td
{
padding:10px;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
margin: 15px;
font-size:0.9em;
line-height:120%;
}
.words
{
	background-color:#C5D0C1;
padding: 15px;
font-size:0.9em;
line-height:120%;
width:100%;
font-weight:bold;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.box1{
background-color:#cce0f0;
padding: 15px;
line-height:120%;
}
.box, .subheadingBlue{
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
line-height:120%;
}
/* Hightlight Boxes Heading : CSS given directly to <b> tag*/
.NewWordBox b, .activityBox b, .box b 
{
	font-weight:bold;
	font-size:1.2em;
}
/* Hightlight Boxes Sub Heading */
.NewWordBox .Subheading, .activityBox .Subheading, .box .Subheading 
{
	font-weight:bold;
	font-size:1em;
}
.underline_txt
{
font-decoration:underline;
}

.bold_txt
{
font-weight:bold;
}

.center_element
{
margin:auto;
}
.italics_txt
{
font-style:italic;
}
.block_element
{
display:block;
}
.img_rt
{
float:right;
clear:both;
}
.img_lft
{
float:left;
}

@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:30%;
font-size:1em;
}
div.chapter_pos div span
{
font-size:0.5em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}
#prelims .char-style-override-13, #prelims .para-style-override-22
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-3
{
	font-style:italic;
font-weight:normal;
font-family:"Walkman-Chanakya-905";
font-size:100%;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
#prelims img
{
	width:100%;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:#B69521;
padding:15px;
}
/* Chapter number */
h4
{
color:#000000;
font-size:1.5em;
}
/* Concept Heading */
.ConceptHeading
{
color:white;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
background:#00ACEE;
}
/* Sub Heading */
.SubHeading
{
color:rgb(237, 30, 153);
font-size:1.3em;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.caption
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
text-align:center;

}
.box{
background-color:#C8E9EB;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
.lining_box2
{
border:2px solid #000;
padding:15px;
border-radius:15px;
}

.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:10%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}
.box3{
background-color:#AADFF9;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.box4{
background-color:#CCEEEC;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.fontcolor
{
color:#00ADEF;
}

.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:10%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}