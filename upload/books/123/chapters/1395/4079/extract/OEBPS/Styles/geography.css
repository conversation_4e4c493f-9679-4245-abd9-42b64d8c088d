@charset "utf-8";

body {
	font-family:Arial, Helvetica, sans-serif;
	font-size:100%;
	line-height:130%;
	padding:2%;
	text-align:justify;
}

* {
	margin:0;
	padding:0;
}

.images {
	text-align:center;
}

.unitHeading {
	text-transform:uppercase;
	font-size:130%;
	text-align:center;
	margin:1% 0;
}

.author {
	text-align:right;
}

.unitNotes {
	text-align:center;
	font-size:115%;
}

.chapterNo {
	text-transform:uppercase;
	text-align:center;
	font-weight:bold;
}

.chapterHeading {
	font-size:140%;
	text-align:center;
	margin:2% 0;
	font-weight:bold;
}

.subHeading {
	margin:0.7% 0;
	font-weight:bold;
	font-size:120%;
	color:rgb(0, 174, 239);
}

.activityBox{
background-color:rgba(255,255,255);
padding:5px 5px 5px 5px;
margin:5px 5px 5px 5px;
}

.example {
	color:rgb(0, 174, 239);	
}

.topicHeading {
	margin:0.5% 0;
}

.exercise {
	text-transform:uppercase;
	text-align:center;
	margin:1.5% 0;
}

table {
	border-collapse: collapse;
}

table tr:first-child {
	background: #00ADEE;
	padding: 5px;
	font-style: italic;
	font-weight: bold;
	color: #fff;
}

tr.secondary {
	background: #B7E3F8;
}

td {
	padding: 0.5%;
	border: solid 2px #00ADEE;
}

img {
	vertical-align: middle;
}

tr.nostyle {
	background: none !important;
color:#000 !important;
font-style: normal !important;
font-weight: normal !important;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width:96%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#222;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.lining_box
{
border:1px solid #000;
padding:5px;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:80%;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}



.footer

{

display:none;

}

table td

{

padding:10px;

}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
div.chapter_pos div span

{

font-size:0.5em;

color:#eaeaea;

font-weight:normal;

}
   
}
#prelims .char-style-override-15, #prelims .char-style-override-19
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#af7b0a;
}
.char-style-override-2
{
	font-style:italic;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:#24AFD9;
padding:10px;
}
/* Chapter number */
h4
{
color:#24AFD9;
font-size:1.3em;
}
/* Concept Heading */
.ConceptHeading
{
color:#24AFD9;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading
{
font-size:1.1em;
font-weight:bold;
color:#24AFD9;
}
/* Sub Heading 2*/
.SubHeading2
{
font-size:1em;
font-weight:bold;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.caption
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
text-align:center;
}
.box{
background-color:#9FDAF4;
padding: 15px;
font-size:0.9em;
line-height:120%;
border-radius:15px;
	margin: 1%;
}
table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
.lining_box2
{
background-color:#FFFACD;
border:2px solid #FFA500;
padding:15px;
border-radius:15px;
}

.cover_img_small
{
width:50%;
}


.lining_box3
{
border:2px solid #1E90FF;
padding:15px;
border-radius:15px;
}
.question
{
background-color:#F5F5F5;
border:2px solid #FFA500;
padding:15px;
border-radius:15px;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:10%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}