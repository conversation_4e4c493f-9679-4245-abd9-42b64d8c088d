@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
	font-size:120%;
	line-height:150%;
	padding:2%;
	text-align:justify;
}

.chapterHeading {
	text-align:center;
	font-size:140%;
	font-weight:bold;
	color:#000;
	
}

.chapterNumber {
	font-size:36px;
	font-weight:bold;
}
.lining_box
{
border:2px solid #AA0078;
padding:15px;
border-radius:15px;
}
.subHeading {
	font-size:130%;
	color:#AA0078;
	margin-bottom:1%;
	font-weight:bold;
    text-align:center;
}
.meaning {
	font-size:95%;
}

.image {
	text-align:center;
}

.activity {
	background: #e1def0;
	padding:2%;
}

.englishMeaning{
	font-family:Arial, Helvetica, sans-serif;
	font-size:70%;
}
.bold
{
	font-size:110%;
	font-family: Walkman-Chanakya-905;
		font-weight:bold;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 95%;
position:absolute;

top:60%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#3A5F0B;

padding:10px;

width:28%;
line-height:120%;
margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

h2
{
color:#fff;
font-size:1.5em;
background:#AA0078;
padding:10px;
}

h4

{

color:#d1640f;

}

.footer

{

display:none;

}

table td

{

padding:10px;

}

.conc

{

color:#006699;

}
#prelims .para-style-override-17
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-2
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#b55414;
}

.cover_img_small

{

width:50%;

}
@media only screen and (max-width: 767px) {


div.chapter_pos


{

top:30%;

font-size:1em;

}

div.chapter_pos div


{

width:80%;



}

.cover_img_small

{

width:90%;

}

}
.underline_txt

{

font-decoration:underline;

}

.bold_txt

{

font-weight:bold;

}

.center_element

{

margin:auto;

}

.italics_txt

{

font-style:italic;

}

.block_element

{

display:block;

}

.img_rt

{

float:right;

clear:both;

}

.img_lft

{

float:left;

}
table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}