body {
font-family:"Arial";
font-size:100%;
line-height:110%;
padding:12px;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}

.author {
text-align:right;
}
.chapterHeading {
font-size:160%;
margin-bottom:20px;
text-align:center;
text-transform:uppercase;

}
h3
{
color:#363435;
font-size:20px;
padding:15px;
}
.Heading {
font-size:160%;
margin-bottom:20px;
text-align:center;
}

.chapterNumber {
	font-size: 125%;
	
	text-align:center;
}

.subHeading {
font-size:125%;
margin-bottom:20px;
text-align:center;
text-transform:uppercase;
}

.topicHeading {
font-size:115%;
margin-bottom:20px;
text-transform:uppercase;
}

.center {
	text-align: center;
	
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
background-color:#fff9c2;
padding: 15px;
font-size: 0.9em;
}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size: 0.9em;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 15px;
font-size: 0.9em;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{
margin-left:-12.6%;
text-align: center;

width: 124%;

position:absolute;

top:20%;
line-height:110%;
font-weight:bold;

font-size:150%;

color:#fff;

}

div.chapter_pos div

{

background:#363435;
top:10%;
padding:15px;

width:24%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 15px;
font-size: 0.9em;
}
.lining_box
{
border:2px solid #000000;
padding:15px;
border-radius:15px;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:40%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:100%;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}
p.resize img, .resize img

{


position:relative;

top:20px;


}

p.resize2 img, .resize2 img

{


position:relative;

top:30px;

}

p.resize3 img, .resize3 img

{


position:relative;

top:20px;

}

p.resize4 img, .resize4 img

{

height:50px;

position:relative;

top:15px;

} 

h2
{
color:#363435;
font-size:1.5em;

padding:10px;
}


h4
{
	color:#363435;
	font-size: 1.2em;
}
h5
{
    color:#363435;
	font-size: 1.0em;
}

.footer

{

display:none;

}

table td

{

padding:10px;

}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size: 0.9em;
}
.box{
background-color:#9AE4FA;
padding: 15px;
font-size: 0.9em;
}


.box2{
background-color:#E2E2E3;
padding: 15px;
font-size: 0.9em;
}
.cover_img_small

{

width:50%;

}


.red
{
color:#FF0000;
font-size:1.1em;

}

@media only screen and (max-width: 767px) {


div.chapter_pos


{
background-color: #363435;
top:80%;

font-size:1em;

}

div.chapter_pos div


{

width:70%;

}

.cover_img_small

{

width:90%;

}

}


 .resize3 img

{

height:75px;

position:relative;

top:25px;



div.layout

{

text-align: center;

}

div.chapter_pos


{
background-color:#363435;

text-align: center;


width: 96%;


position:absolute;


top:90%;


font-weight:bold;


font-size:28px;


color:#fff;


}


div.chapter_pos div


{


background:#363435;


padding:10px;


width:40%;
top:70%;

margin:auto;

opacity:0.9;


}


div.chapter_pos div span


{


font-size:0.7em;


color:#eaeaea;


font-weight:normal;


}

@media only screen and (max-width: 767px) {


div.chapter_pos


{

background-color: #363435;
font-size:0.8em;

line-height:120%;

top:70%;

}


div.chapter_pos div span


{


font-size:0.7em;


}

}






