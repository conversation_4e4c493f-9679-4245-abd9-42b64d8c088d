<table border="1" cellpadding="1" cellspacing="1" style="height:147px; width:482px">
	<tbody>
		<tr>
			<td>
			<p style="text-align:justify"><span style="font-size:14px"><span style="color:#27ae60"><span style="font-family:Merriweather"><strong>Learning Objectives</strong></span></span></span></p>

			<ul>
				<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Learn how to open, edit and save drawings in Tux Paint</span></span></li>
				<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Know how to apply different effects to drawings</span></span></li>
				<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Understand how to use tools such as paint and shapes</span></span></li>
				<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Learn how to use different brushes</span></span></li>
			</ul>
			</td>
		</tr>
	</tbody>
</table>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">You already have an idea of how to start </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Tux Paint</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> and the tools available in it. Now let us find out how we can use these tools to create beautiful drawings.</span></span></p>

<p style="text-align:justify"><span style="font-size:16px"><span style="color:#27ae60"><span style="font-family:Merriweather"><strong>Opening a Drawing in Tux Paint</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">To open a drawing in Tux Paint, follow these steps:</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Start <strong>Tux Paint.</strong></span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on <strong>Open</strong> at the lower-left of the Open</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> screen.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on the drawing you want to open.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on <strong>Open </strong>to open the drawing.</span></span><br />
	<img alt="" height="248" src="Images/8rt1.png" width="313" /></li>
</ol>

<p style="text-align:justify"><span style="color:#2980b9"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>DO YOU KNOW?</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">You can play a slide show of the images you have created using Tux Paint using the </span></span><span style="color:#2980b9"><span style="font-family:Merriweather">Slides</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">&nbsp;</span></span><img alt="" height="18" src="Images/8rt1a.png" width="18" /><span style="color:#4e5f70"><span style="font-family:Merriweather"> button in the </span></span><span style="color:#2980b9"><span style="font-family:Merriweather">Open</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> menu.</span></span></p>

<p style="text-align:justify"><span style="color:#27ae60"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>Using the Paint Tool</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">Now that we know how to open a file in </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Tux Paint</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">, let us see how we can draw something cool. The </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Paint </span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">tool allows you to create free-hand drawings. Let us see an example of what we can draw using the </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Paint </span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">tool:</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Open <strong>Tux Paint</strong> and create a new</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> drawing using the <strong>New </strong>option.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on the <strong>Paint </strong>tool.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Select<strong> Leaf </strong>brush from the brushes</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> window and green colour to create</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> leaves of a tree.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Draw the leaves of the tree as shown.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Next, select the <strong>Big Square</strong> brush from</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> the brushes window to create the trunk</span></span> <span style="color:#4e5f70"><span style="font-family:Merriweather">of the tree. </span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Draw the trunk of the tree as shown.</span></span><br />
	<img alt="" height="329" src="Images/8rt1b.png" width="406" /><br />
	<span style="color:#4e5f70"><span style="font-family:Merriweather">Congratulations! You have just created your own tree in <strong>Tux Paint</strong>.</span></span></li>
</ol>

<p style="text-align:justify"><span style="color:#27ae60"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>Using the Shapes Tool</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">Suppose you want to draw a rectangle or a circle using Tux Paint. You can easily do so using the </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Shapes</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> tool. The </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Shapes</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> tool can be used to draw geometric shapes, such as a square, rectangle, circle and polygon. You can either create a hollow shape or a shape filled with colour. Let us first create a hollow circle and then create a circle filled with colour:</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Open <strong>Tux Paint</strong> and create a new drawing in it using the <strong>New </strong>option.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Next, click on the <strong>Shapes </strong>tool and select the<strong> Hollow Circle</strong> shape from the right-hand</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> side of the screen.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Drag the cursor to set a size for the</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> circle. Then click the mouse once and</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> set the angle of rotation for the circle.</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> Once you are done with setting the</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> rotation, click again to create the circle.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Now to draw a circle filled with colour,</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> choose the <strong>Filled Circle</strong> shape from the</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> right side of the screen.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Follow the same process as you did for</span></span> <span style="color:#4e5f70"><span style="font-family:Merriweather">creating the hollow circle.</span></span><br />
	<img alt="" height="352" src="Images/8rt1c.png" width="441" /></li>
</ol>

<p style="text-align:justify"><span style="font-size:14px"><span style="color:#8e44ad"><span style="font-family:Merriweather"><strong>Using the Stamp Tool</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">The </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Stamp</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> tool can be used to add predefined stamp pictures to your drawing such as birds, penguins, fishes and insects. Let us see how we can add stamp pictures to our drawing:</span></span></p>

<ol>
	<li style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">Open <strong>Tux Paint</strong> and create a new drawing using the <strong>New </strong>option.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on the <strong>Stamp </strong>tool and select a stamp from the right-hand side of the window.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click anywhere on the drawing where you want to add the stamp.</span></span><br />
	<img alt="" height="339" src="Images/8rt1d.png" width="423" /></li>
</ol>

<p style="text-align:justify"><span style="color:#8e44ad"><span style="font-size:14px"><span style="font-family:Merriweather"><strong>Using the Text Tool</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">Let us say you have created a beautiful drawing and want to add your name right below it. This can be done using the </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Text </span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">tool in </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Tux Paint</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">. It allows us to add text anywhere on the drawing. To understand this better, let us see how we can do this in </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Tux Paint</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">:</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Open <strong>Tux Paint</strong> and open the tree drawing we created while understanding how to use the Paint tool.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on the <strong>Text </strong>tool and choose a font from the right-hand side.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on an empty area on the drawing and type your text.</span></span><br />
	<img alt="" height="338" src="Images/8rt1e.png" width="428" /></li>
</ol>

<p style="text-align:justify"><span style="font-size:14px"><span style="color:#8e44ad"><span style="font-family:Merriweather"><strong>Using the Eraser Tool</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">While drawing a picture, you might draw some extra or stray marks. Don&rsquo;t worry! These stray marks can be erased easily using the </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Eraser </span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">tool. This tool works just like a real pencil eraser. The eraser tools have different sizes to help us erase any size of marks on a picture. Let us see how the Eraser tool works by erasing the circle into a semicircle:</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on the <strong>Eraser </strong>tool.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Select the size of the eraser that you think is suitable for your purpose.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Pressing the left mouse button, drag the rectangular eraser box over the circle so as to erase one half of the circle.</span></span><br />
	<img alt="" height="297" src="Images/8rt1f.png" width="395" /></li>
</ol>

<p style="text-align:justify"><span style="color:#27ae60"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>Concept of Drip Magic</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">When we paint a wall, you might have seen that some of the colour drips down the wall. Now, suppose we want to add the same effect to a colour in a drawing. We can do this using the </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Drip Magic</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> tool. To better understand this, let us create a &lsquo;dripping square&rsquo;.</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Create a filled square using the Shapes tool.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on the <strong>Magic</strong> tool and select the <strong>Drip </strong>option from the right side window.</span></span><br />
	<span style="color:#4e5f70"><span style="font-family:Merriweather">You will now see that your cursor has turned</span></span> <span style="color:#4e5f70"><span style="font-family:Merriweather">into a magic wand.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on the bottom edge of the square</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> from where you want the colour to drip.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click a few more times at different</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> positions on the bottom edge of the</span></span> <span style="color:#4e5f70"><span style="font-family:Merriweather">square.</span></span><br />
	<img alt="" height="293" src="Images/8rt1g.png" width="363" /></li>
</ol>

<p style="text-align:justify"><span style="color:#27ae60"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>Effects in Tux Paint</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">With </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Tux Paint</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">, you can add to your drawings wonderful effects such as rainbow colours, snowflakes, railway tracks, to name a few. Let us look at some of these effects.</span></span></p>

<p style="text-align:justify"><span style="font-size:14px"><span style="color:#8e44ad"><span style="font-family:Merriweather"><strong>Grass Effect</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">Suppose you want to create a drawing of a forest. For this, you would paint some of the area on the drawing in green colour. </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Tux Paint</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> provides an interesting effect for creating reallooking grass. This effect is called </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Grass</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> effect. Let&rsquo;s try drawing some grass in the drawing of the tree we created before:</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Open<strong> Tux Paint</strong> and open the drawing in which we created the tree.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on the <strong>Magic </strong>button and then select the <strong>Grass effect </strong>from the righthand side window.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Add some grass to the drawing by clicking and dragging the mouse across the bottom edge of the drawing.</span></span><br />
	<img alt="" height="335" src="Images/8rt2.png" width="419" /></li>
</ol>

<p style="text-align:justify"><span style="color:#2980b9"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>DO YOU KNOW?</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">For </span></span><span style="color:#2980b9"><span style="font-family:Merriweather">Grass </span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">effect, the size of grass increases as you move lower towards the screen and increases as you move higher up in the screen. Suggested change: You can increase or decrease the size of grass in the Grass effect. To increase the size, drag/move the cursor down the screen. To decrease the size, drag/ move the cursor up the screen.</span></span></p>

<p style="text-align:justify"><span style="color:#8e44ad"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Foam Effect</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">The</span></span><span style="color:#27ae60"><span style="font-family:Merriweather"> Foam</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> effect allows you to add foam bubbles to your image. If you plan to paint a frothy beach or if you love bubbles, then you will enjoy using this tool.<br />
So, let us try creating something bubbly!</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Open <strong>Tux Paint</strong> and create a new drawing using the <strong>New </strong>option.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on the <strong>Magic </strong>tool and select the Foam effect from the right-hand side window.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click and drag the mouse on the screen where you want to draw the foam bubbles.</span></span><br />
	<img alt="" height="310" src="Images/8rt2a.png" width="389" /></li>
</ol>

<p style="text-align:justify"><span style="color:#8e44ad"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Edges Effect</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">The </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Edges</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> effect can be used to draw the outline of any shape that you have drawn. Let us find out how we can do this:</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Open <strong>Tux Paint</strong> and create a new drawing using the <strong>New </strong>option.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on <strong>Shapes</strong> tool and then on a <strong>Filled Square</strong> shape on the right-hand side window.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on <strong>Magic</strong> and select the <strong>Edges</strong> effect.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click and drag the mouse all over the square you have drawn.</span></span><br />
	<img alt="" height="286" src="Images/8rt2b.png" width="360" /></li>
</ol>

<p style="text-align:justify"><span style="color:#8e44ad"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Rainbow Effect</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">The </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Rainbow </span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">effect is used to draw free-hand shapes with a brush that uses multiple or rainbow colours. Let us see how to use the Rainbow effect:</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Open <strong>Tux Paint</strong> and create a new drawing using the <strong>New </strong>option.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on<strong> Magic </strong>and select the <strong>Rainbow</strong> effect.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click and drag the mouse to create a shape of your choice.</span></span><br />
	<img alt="" height="296" src="Images/8rt2c.png" width="373" /></li>
</ol>

<p style="text-align:justify"><span style="font-size:14px"><span style="color:#8e44ad"><span style="font-family:Merriweather"><strong>Noise Effect</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">The </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Noise</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> effect is used to add random noise to your drawing. You can view noise as tiny coloured dots on the drawing. Let&rsquo;s find out how to add noise to your drawing:</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Open<strong> Tux Paint</strong> and create a new drawing using the <strong>New</strong> option.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on the<strong> Stamp </strong>tool and select the <strong>Penguin </strong>stamp.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Next, click on the <strong>Magic</strong> tool and select the <strong>Noise </strong>effect.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click and drag the mouse to add some noise to the penguin in the drawing.</span></span><br />
	<img alt="" height="259" src="Images/8rt2d.png" width="322" /></li>
</ol>

<p style="text-align:justify"><span style="color:#8e44ad"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Using Brushes</strong></span></span></span><br />
<span style="color:#27ae60"><span style="font-family:Merriweather">Tux Paint </span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">allows us to use different brushes to add creative new designs to a drawing. You can use different brushes to draw different shapes in the drawing. The drawing below shows the use of the star brush:</span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">Tux Paint provides the following brush options:</span></span></p>

<ul>
	<li style="text-align:justify"><strong><span style="color:#8e44ad"><span style="font-family:Merriweather">Point:</span></span></strong><span style="color:#4e5f70"><span style="font-family:Merriweather"><strong> </strong>Different sizes of points are available.</span></span></li>
	<li style="text-align:justify"><strong><span style="color:#8e44ad"><span style="font-family:Merriweather">Arrow:</span></span></strong><span style="color:#4e5f70"><span style="font-family:Merriweather"> Different arrows types are available.</span></span></li>
	<li style="text-align:justify"><span style="color:#8e44ad"><span style="font-family:Merriweather"><strong>Textures: </strong></span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">Textures such heart, star, leaves, cat and starfish are available.</span></span></li>
	<li style="text-align:justify"><span style="color:#8e44ad"><span style="font-family:Merriweather"><strong>Basic Shapes: </strong></span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">Shapes such as spiral, square, pentagon and hexagon are included.</span></span><br />
	<img alt="" height="322" src="Images/7.png" width="374" /></li>
</ul>

<p style="text-align:justify"><span style="color:#2980b9"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>DO YOU KNOW?</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">Some brushes change their shape depending on the direction they are drawn; for example, the </span></span><span style="color:#2980b9"><span style="font-family:Merriweather">Squirrel</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">&nbsp;</span></span><img alt="" height="20" src="Images/8rt2f.png" width="19" /><span style="color:#4e5f70"><span style="font-family:Merriweather"> brush under the</span></span><span style="color:#2980b9"><span style="font-family:Merriweather"> Brush </span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">window.</span></span></p>

<p style="text-align:justify"><span style="color:#27ae60"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>Saving a Drawing</strong></span></span></span><br />
<span style="color:#4e5f70"><span style="font-family:Merriweather">After you draw and paint a drawing, you will want to save it. To save a new drawing, click on the </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Save </span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">tool. To save a drawing that you opened using the </span></span><span style="color:#27ae60"><span style="font-family:Merriweather">Open </span></span><span style="color:#4e5f70"><span style="font-family:Merriweather">tool, you can do the following:</span></span></p>

<ol>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Click on <strong>Save</strong>.</span></span><br />
	<span style="color:#4e5f70"><span style="font-family:Merriweather">A window will appear, inquiring if you want</span></span> <span style="color:#4e5f70"><span style="font-family:Merriweather">to replace the old drawing.</span></span><br />
	<img alt="" height="310" src="Images/8rt2g.png" width="388" /></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">If you want to replace the old</span></span><span style="color:#4e5f70"><span style="font-family:Merriweather"> drawing, click on the <strong>Tick </strong>button or</span></span> <span style="color:#4e5f70"><span style="font-family:Merriweather">else click on the <strong>Cross </strong>button.</span></span></li>
</ol>

<p style="text-align:justify"><span style="color:#8e44ad"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>SUMMARY</strong></span></span></span></p>

<ul>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">You can open files in Tux Paint using the Open option.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">The Paint tool can be used to create free-hand drawings.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">The Shapes tool is used to draw geometric shapes like square, circle and rectangle.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">The Text tool is used to add text on the drawing.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">The Eraser tool is used to erase any stray marks on the drawing.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">The Drip Magic tool can be used to add dripping effect to any colour in our</span></span> <span style="color:#4e5f70"><span style="font-family:Merriweather">drawing.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Effects such as Grass, Foam, Rainbow, Edges and Noise can be used to give a</span></span> <span style="color:#4e5f70"><span style="font-family:Merriweather">creative look to a drawing.</span></span></li>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Brushes can be used to draw different shapes such as star and dots.</span></span></li>
</ul>

<p style="text-align:justify"><span style="color:#999999"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>Key Terms</strong></span></span></span></p>

<table border="1" cellpadding="1" cellspacing="1" style="width:500px">
	<tbody>
		<tr>
			<td>
			<table border="0" cellpadding="1" cellspacing="1" style="width:500px">
				<tbody>
					<tr>
						<td>
						<p><span style="color:#4e5f70"><span style="font-family:Merriweather">Paint </span></span></p>
						</td>
						<td><span style="color:#4e5f70"><span style="font-family:Merriweather">Eraser </span></span></td>
						<td><span style="color:#4e5f70"><span style="font-family:Merriweather">Drip Magic </span></span></td>
						<td><span style="color:#4e5f70"><span style="font-family:Merriweather">Grass </span></span></td>
						<td>
						<p><span style="color:#4e5f70"><span style="font-family:Merriweather">Foam</span></span></p>
						</td>
					</tr>
					<tr>
						<td>
						<p><span style="color:#4e5f70"><span style="font-family:Merriweather">Edges</span></span></p>
						</td>
						<td><span style="color:#4e5f70"><span style="font-family:Merriweather">Rainbow </span></span></td>
						<td><span style="color:#4e5f70"><span style="font-family:Merriweather">Effect</span></span></td>
						<td>&nbsp;</td>
						<td>&nbsp;</td>
					</tr>
				</tbody>
			</table>
			</td>
		</tr>
	</tbody>
</table>
