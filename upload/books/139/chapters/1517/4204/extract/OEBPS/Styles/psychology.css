@charset "utf-8";
@charset "windows-1253";

body {
	font-family:Arial, Helvetica, sans-serif;
	font-size:100%;
	line-height:125%;
	padding:2%;
	text-align:justify;
}

* {
	margin:0;
	padding:0;
}

.images {
	text-align:center;
border:2px solid #0099ff;
}

.subheading {
	
	font-size: 115%;
	margin-top:40px;
}

.chapterHeading {
	font-size: 120%;
	text-align: left;
	font-weight: bold;
	margin: 4% 0;
}
.mainHeading {
	font-size:120%;
text-transform: uppercase;
	background-color:#9FDAF4;
font-weight: bold;
	margin:2% 0 2% 0;
padding: 1%;
display: inline-block;

}

.chapterNumber {
	font-size:160%;
	text-align:right;
	font-weight : bold;
	text-transform:uppercase;
}

.activity {
	background:rgb(255, 252, 199);
	padding:1%;
	border:rgb(222, 18, 122);
}

.box {
	border: solid 2px #9FA1A2;
	padding: 1%;
	margin: 1%;
}

.bluebox {
	padding: 1%;
	margin: 1%;
	background: #9FDAF4;;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#444;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.lining_box
{
border:1px solid #000;
padding:5px;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:80%;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}
.footer

{

display:none;

}

table td

{

padding:10px;

}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

body {
font-family:"Arial";
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

#prelims .char-style-override-15, #prelims .char-style-override-19
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#af7b0a;
}
.char-style-override-2
{
	font-style:italic;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:#0099ff;
padding:10px;
}
/* Chapter number */
h4
{
color:#0099ff;
font-size:1.5em;
}
/* Concept Heading */
.ConceptHeading
{
color:#000000;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading
{
font-size:1.3em;
font-weight:bold;
color:#00AEEF;
margin-top:40px;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.caption
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
text-align:center;
}
.box{
background-color:#9FDAF4;
padding: 15px;
font-size:0.9em;
line-height:120%;
border-radius:15px;
	margin: 1%;
}
table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
.lining_box2
{
border:2px solid #000;
padding:15px;
border-radius:15px;
}



.fontcolor
{
color:#0099ff;
}
.lining_box3
{
border:2px solid #909090;
padding:15px;
border-radius:15px;
background-color:#c0c0c0;
}
.lining_box4
{
border:2px solid #00AEEF;
padding:15px;
border-radius:15px;
}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:10%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}