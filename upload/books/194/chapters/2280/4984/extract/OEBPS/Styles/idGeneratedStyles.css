@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

body {
	font-family:"Walkman-Chanakya-905";
}

body {
	font-size:120%;
	line-height:150%;
	padding:2%;
	text-align:justify;
	font-family: "Walkman-<PERSON>akya-905";
}

.chapterNumber {
	font-size:36px;
	text-align:center;
}

.CharOverride-6
{
	font-size:110%;
	font-family: "Walkman-Chanakya-905";
		font-weight:bold;
}
.CharOverride-19
{
	font-style:italic;
}

div.layout
{
  text-align: center;
}


.lining_box
{
border:2px solid #555;
padding:15px;
border-left:30px solid #00B2B0;
}
.lining_box2
{
border:2px solid #555;
padding:15px;
border-radius:15px;
}
img
{
	max-width:100%;
position:relative;
top:15px;
}
ul

{

margin-left:25px;

}

.Caption_Hindi, .ParaOverride-9

{

font-style: italic;

font-size: 0.90em;

color: #000000;

text-align:center;

}

p

{

margin-top:10px;

}
h1
{
color:#0094D9;
font-size:1.5em;
padding:10px 0px;
}

h2
{
color:#fff;
font-size:1.5em;
background:#0094D9;
padding:10px;
}
h3
{
color:#0094D9;
font-size:1.5em;
padding:5px 5px;
}
h4

{
color:#0094D9;
font-size:1.3em;
font-weight:bold;
margin-bottom:20px;

}
h5

{
margin-bottom:0px;
padding-bottom:0px;
color:#d1640f;
font-size:1.1em;
font-weight:bold;

}
table, table td

{
border-collapse:collapse;
padding:10px;

}
.box{
background-color:#ABE5E4;
padding: 15px;
}
.pbox{
background-color:#C6D4EB;
padding: 15px;
}
.ybox{
background-color:#FFE6C5;
padding: 15px;
border-top:1px solid #000;
border-bottom:1px solid #000;
}
.gbox{
background-color:#EEE7C8;
padding: 10px;

}
.gbox1{
background-color:#FFC78B;
padding: 5px;
width:20%;
}
.sbox{
background-color:#AAD6EF;
padding: 15px;
border-top:2px solid #FC64B2;
border-bottom:2px solid #FC64B2;
}

}
.sky
{
color:#26C8F4;
font-weight:bold;
}


table
{
width:100%;
border:2px solid #fff;
border-collapse:collapse;
}
td
{
padding:10px;
border:2px solid #fff;
border-collapse:collapse;
}
.englishMeaning, .CharOverride-2,.CharOverride-8,.CharOverride-8,.CharOverride-17
{
	font-family:Arial, Helvetica, sans-serif;
	font-size:80%;
}
.CharOverride-23
{
padding:5px;
background:#6F2895;
color:#fff;
font-weight:bold;
font-size:1.3em !important;
position:relative;
top:-30px;
}
img
{
margin-left: auto;
margin-right: auto;
max-width:90%;
margin:5px;
}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
.cover_img_small
{
width:50%;
}
/*img{
width:60%;*/
}
}

div.layout
{
text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:70%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#008888;

padding:5px;

width:20%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
@media only screen and (max-width: 767px) {

div.chapter_pos

{

font-size:0.8em;
line-height:120%;
top:50%;
}

div.chapter_pos div span

{

font-size:0.7em;

}
}