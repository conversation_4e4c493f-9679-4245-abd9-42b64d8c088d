
html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
.box{
background-color:#FCA58D;
padding: 15px;
font-size:1.5em;
line-height:150%;
}
.box1{
background-color:#FFCCB1;
padding: 15px;
font-size:0.9em;
line-height:150%;
}
.box2{
background-color:#E2E4F2;
padding: 15px;
font-size:0.9em;
line-height:150%;
}
img
{
	max-width:100%;
}
h4
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
/* Chapter Name */
h1
{
color:#fffff;
font-size:1.5em;
background:#86D7F0;
padding:10px;
}
/* Chapter number */
h2
{
color:#85868A;
font-size:1.3em;
text-decoration: none;
 border-bottom: 3px solid #F9E05E;
width:auto;
}
.underline{
color:#85868A;
font-size:1.3em;
text-decoration: none;
 border-bottom: 3px solid #F9E05E;
width:auto;
}
/* Concept Heading */
h3
{
color:#7C868B;
font-size:1.1em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */

/* Sub Heading 2*/
h5
{
color:#CC0000;
font-size:1.1em;
font-weight:bold;
}
p
{
	margin-top:10px;
}
table
{
	width:100%;
	
	border-collapse:collapse;
}
td
{
	padding:10px;
	
	border-collapse:collapse;
}
div.layout
{
text-align: center;
}
div.chapter_pos
{
text-align: center;
width: 96%;
position:absolute;
top:70%;
font-weight:bold;
font-size:28px;
color:#fff;
}
div.chapter_pos div
{
background:#508C50;
padding:10px;
width:40%;
margin:auto;
opacity:0.9;
}
div.chapter_pos div span
{
font-size:0.7em;
color:#eaeaea;
font-weight:normal;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
font-size:0.8em;
line-height:120%;
top:50%;
}
div.chapter_pos div span
{
font-size:0.7em;
}
}
