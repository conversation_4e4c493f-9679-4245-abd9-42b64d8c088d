<p style="text-align:justify"><span style="color:#27ae60"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>More on PowerPoint 2010</strong></span></span></span></p>

<table border="1" cellpadding="1" cellspacing="1" style="height:231px; width:449px">
	<tbody>
		<tr>
			<td>
			<p style="text-align:justify"><span style="font-size:14px"><span style="font-family:Merriweather"><span style="color:#27ae60"><strong>Learning Objectives</strong></span></span></span></p>

			<ul>
				<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Understand templates and their use</span></span></li>
				<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Understand themes and learn how to apply them</span></span></li>
				<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Know how to add decorative text</span></span></li>
				<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Learn how to move, copy, rotate and resize objects</span></span></li>
				<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Learn how to apply various effects</span></span></li>
				<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Know how to create a photo album</span></span></li>
				<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Understand various visual cues</span></span></li>
				<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">List various presentation views</span></span></li>
				<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Understand slide master and learn how to create one</span></span></li>
			</ul>
			</td>
		</tr>
	</tbody>
</table>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">PowerPoint presentations help us to communicate our ideas in a more effective way. PowerPoint allows us to combine multimedia and text to create engaging presentations. Templates and themes make it easier for designing consistent-looking presentations quickly. Templates are pre-designed presentations that can be used as starter files, whereas themes help give your presentation instant style. Each theme uses a unique combination of colours, fonts and effects. In this chapter, we will learn to use templates and themes, create decorative text, transform objects, apply effects, use various presentation views and so on.</span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="font-size:14px"><strong><span style="color:#27ae60">Opening an Existing Presentation</span></strong>&nbsp;&nbsp;&nbsp;</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">PowerPoint lets you create new presentations as well as make changes to existing ones. The simplest way to open a saved presentation is to press Ctrl + O. This opens the Open dialog box where you can navigate to and select the presentation you want to open.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To open an existing presentation in PowerPoint 2010, follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1 </strong></span><span style="color:#4e5f70">Open PowerPoint 2010.</span><br />
<span style="color:#e74c3c"><strong>2</strong></span><span style="color:#4e5f70"> Select the <strong>File </strong>tab. This opens the Backstage view.</span><br />
<span style="color:#e74c3c"><strong>3</strong></span><span style="color:#4e5f70"> In the left pane, select the <strong>Open </strong>option. This opens the <strong>Open </strong>dialog box.</span></span></p>

<p style="text-align:justify"><img alt="" height="207" src="Images/cc1.png" width="284" /><br />
<span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>4</strong></span><span style="color:#4e5f70"> In the <strong>Open </strong>dialog box, navigate to the presentation file that you want to open and click on it to select.</span><br />
<span style="color:#e74c3c"><strong>5</strong></span><span style="color:#4e5f70"> Click on the <strong>Open </strong>button.</span></span></p>

<p style="text-align:justify"><img alt="" height="240" src="Images/cc2.png" width="304" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">This opens the saved presentation in a separate PowerPoint window.</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="font-size:14px"><span style="color:#27ae60"><strong>Using a Template for Presentation</strong></span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">A template provides a blueprint or outline for designing a presentation quickly in PowerPoint. It is actually a presentation with predefined layouts, professional formatting that includes theme colours, theme fonts, theme effects, animation effects and transition effects. Templates may include a single slide or group of slides&mdash;with opening and closing slides. You can create a new presentation from a built-in template and edit it accordingly. Moreover, you can also save a presentation as your own custom template and reuse and share them with others. The file extension of a PowerPoint template is .potx.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To use a template to create a presentation, follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1 </strong></span><span style="color:#4e5f70">Open PowerPoint 2010.</span><br />
<span style="color:#e74c3c"><strong>2</strong></span><span style="color:#4e5f70"> Select the <strong>File </strong>tab. This opens the Backstage view.</span><br />
<span style="color:#e74c3c"><strong>3</strong></span><span style="color:#4e5f70"> Select the <strong>New </strong>option.</span><br />
<span style="color:#e74c3c"><strong>4</strong></span><span style="color:#4e5f70"> Then, under <strong>Available Templates and Themes</strong>, click on <strong>Sample templates</strong>.</span></span></p>

<p style="text-align:justify"><img alt="" height="226" src="Images/cc3.png" width="321" /><br />
<span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>5</strong></span><span style="color:#4e5f70"> Under <strong>Sample templates</strong>, select a template (scroll down if required) and click on <strong>Create</strong>.</span></span></p>

<p style="text-align:justify"><img alt="" height="192" src="Images/cc4.png" width="281" /><br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">The selected template opens in PowerPoint that can be used as the blueprint for a presentation.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">This template can be used as a starter file for your presentation. You can see that the template has multiple slides with different slide layouts and even transitions.</span></span><br />
<img alt="" height="207" src="Images/cc5.png" width="312" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="font-size:14px"><strong><span style="color:#27ae60">Using PowerPoint Themes</span></strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">A theme is a set of predefined design parameters such as fonts, colours, slide layouts and effects, which can be applied to a presentation in PowerPoint. By applying a theme, you can quickly change the overall appearance of your presentation, giving it a consistent, professional look. You can create a new presentation based on a theme or apply a theme any time after creating your presentation.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To create a presentation using a predefined theme, follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1</strong></span><span style="color:#4e5f70"> Open PowerPoint 2010.</span><br />
<span style="color:#e74c3c"><strong>2 </strong></span><span style="color:#4e5f70">Select the <strong>File </strong>tab and then select the <strong>New </strong>option.</span><br />
<span style="color:#e74c3c"><strong>3</strong></span><span style="color:#4e5f70"> Then, under <strong>Available Templates</strong> <strong>and Themes</strong>, click on <strong>Themes</strong>.</span></span></p>

<p style="text-align:justify"><img alt="" height="197" src="Images/6.png" width="297" /><br />
<span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>4</strong></span><span style="color:#4e5f70"> Under <strong>Themes</strong>, select a theme such as <strong>Grid </strong>and click on <strong>Create</strong>.</span></span></p>

<p style="text-align:justify"><img alt="" height="188" src="Images/cc6.png" width="282" /><br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">A new PowerPoint window opens with a new presentation based on the selected theme. You will notice that the presentation has one slide with the theme applied to it.</span></span><br />
<img alt="" height="210" src="Images/cc7.png" width="306" /><br />
<br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">To apply a theme on a presentation, follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1</strong></span><span style="color:#4e5f70"> Open the presentation on which you want to apply a theme.</span><br />
<strong><span style="color:#e74c3c">2</span></strong><span style="color:#4e5f70"> Select the <strong>Design </strong>tab on the Ribbon.</span><br />
<span style="color:#e74c3c"><strong>3</strong></span><span style="color:#4e5f70"> In the <strong>Themes </strong>group, click on <strong>More </strong>drop-down arrow.</span></span><br />
<img alt="" height="84" src="Images/cc8.png" width="357" /><br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">This will open the gallery of all available themes. Each image in the gallery represents a theme.</span><br />
<span style="color:#e74c3c"><strong>4</strong></span><span style="color:#4e5f70"> Click on a theme to apply it to the presentation.</span></span></p>

<p style="text-align:justify"><img alt="" height="249" src="Images/cc9.png" width="269" /><br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">The selected theme will change the overall design of your presentation by changing existing fonts, effects and colour schemes all at the same time.</span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">If you don&rsquo;t like the theme, you can apply a different theme by simply selecting it from the Design tab. After applying a theme, you can use the Colors, Fonts and Effects options in the Themes group to edit it. You can also choose a different background style or hide background graphics using the options in the Background group.</span></span></p>

<p style="text-align:justify"><img alt="" height="223" src="Images/cc10.png" width="328" /><br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">You can also save your presentation as a theme and reuse it latter by applying to other presentations. This way you can save a lot of time which you would have spent on formatting your presentation. To save your current presentation theme as a new theme, select the Save Current Theme option in the More drop-down menu.</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="font-size:14px"><span style="color:#27ae60"><strong>Adding Decorative Text with WordArt</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">WordArt helps you quickly create decorative text that stand out. You can choose a WordArt style from the WordArt gallery, and if required, customise it. In the WordArt gallery, the letter A represents the different designs or WordArt styles. The WordArt command is found in the Text group of the Insert tab.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To add special effects to text with WordArt, follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1</strong></span><span style="color:#4e5f70"> Select the slide in which you want to add decorative text.</span><br />
<span style="color:#e74c3c"><strong>2 </strong></span><span style="color:#4e5f70">On the <strong>Insert </strong>tab, in the <strong>Text </strong>group, and click on <strong>WordArt</strong>. A drop-down appears.</span></span></p>

<p style="text-align:justify"><img alt="" height="221" src="Images/cc11.png" width="317" /><br />
<span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>3</strong></span><span style="color:#4e5f70"> Click on the WordArt style from the drop-down. A text placeholder appears at the centre of the slide with the highlighted text &ldquo;Your text here&rdquo;.</span><br />
<span style="color:#e74c3c"><strong>4</strong></span><span style="color:#4e5f70"> Move the placeholder to an appropriate position on the slide.</span><br />
<span style="color:#e74c3c"><strong>5 </strong></span><span style="color:#4e5f70">Type in your own text to replace the placeholder text.</span></span></p>

<p style="text-align:justify"><img alt="" height="232" src="Images/cc12.png" width="342" /><br />
<br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">After adding the WordArt, you can also change its size and colour by selecting the text and using the various formatting options.</span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="font-size:14px"><span style="color:#27ae60"><strong>Moving and Copying Objects</strong></span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">In PowerPoint, you can move or copy objects (such as text boxes, pictures, clip arts, shapes, SmartArt, etc.) within a slide or between slides. You can use the Copy/Paste or Cut/Paste method to copy or move any object respectively between slides or even presentations.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To copy an object (text box), follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1 </strong></span><span style="color:#4e5f70">Select the object you want to copy and press <strong>Ctrl + C</strong> or right-click on the object choose <strong>Copy</strong>.</span><br />
<span style="color:#e74c3c"><strong>2</strong></span><span style="color:#4e5f70"> Select the slide into which you want to paste the copied object.</span><br />
<span style="color:#e74c3c"><strong>3</strong></span><span style="color:#4e5f70"> Press <strong>Ctrl + V</strong> or right-click and choose one of the <strong>Paste Options</strong>.</span></span></p>

<p style="text-align:justify"><img alt="" height="221" src="Images/c14.png" width="314" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To move an object (text box), follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1</strong></span><span style="color:#4e5f70"> Select the object you want to move and press <strong>Ctrl + X</strong> or right- click on the object choose <strong>Cut</strong>.</span><br />
<span style="color:#e74c3c"><strong>2 </strong></span><span style="color:#4e5f70">Select the slide into which you want to paste the object you want to move.</span><br />
<span style="color:#e74c3c"><strong>3</strong></span><span style="color:#4e5f70"> Press <strong>Ctrl + V</strong> or right-click on the slide and choose one of the <strong>Paste Options</strong>.</span></span><br />
<img alt="" height="244" src="Images/cc15.png" width="336" /><br />
<span style="font-family:Merriweather"><span style="font-size:14px"><span style="color:#27ae60"><strong>Rotating and Resizing Objects</strong></span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">You can rotate an object (such as a text box, shape, picture, etc.) in a presentation using the Rotate command, or by manually using the rotation handle.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To rotate an object using the rotation handle, follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1 </strong></span><span style="color:#4e5f70">Select the object you want to rotate. A bounding box appears on the object which can be used to rotate or resize the object. Rotation handle</span></span><br />
<img alt="" height="105" src="Images/cc16.png" width="142" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>2 </strong></span><span style="color:#4e5f70">Click and drag the rotation handle (the green circular icon) present at the top in the direction in which you want the graphic to rotate.</span></span><br />
<img alt="" height="189" src="Images/c16.png" width="250" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>3</strong></span><span style="color:#4e5f70"> Release the mouse button.</span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To rotate an object using the Rotate command, follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1</strong></span><span style="color:#4e5f70"> Double-click the object you want to rotate.</span><br />
<span style="color:#e74c3c"><strong>2</strong></span><span style="color:#4e5f70"> On the <strong>Format </strong>tab, in the <strong>Arrange </strong>group, click the <strong>Rotate&nbsp;</strong></span></span><img alt="" height="17" src="Images/7.png" width="21" /><span style="font-family:Merriweather"><span style="color:#4e5f70"><strong> </strong>command.</span><br />
<span style="color:#e74c3c"><strong>3 </strong></span><span style="color:#4e5f70">From the available options, choose a rotation option or choose <strong>More Rotation Options</strong>.</span></span><br />
<img alt="" height="216" src="Images/cc17.png" width="330" /><br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">Sometimes, you need to change the default size of an object to make it fit appropriately on the slide. You can change the height and width of a shape, picture, text box or WordArt using the options in the Size group of the Format tab.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To change the size of a picture, shape, text box or WordArt, follow these steps:</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>1</strong></span><span style="color:#4e5f70"> Double-click on the inserted object. The <strong>Format </strong>tab under the <strong>Picture Tools</strong> (for a picture) or <strong>Drawing Tools</strong> (for a shape) contextual tab will appear on the Ribbon.</span><br />
<span style="color:#e74c3c"><strong>2</strong></span><span style="color:#4e5f70"> In the <strong>Size </strong>group, enter new values in the <strong>Shape Height</strong> and <strong>Shape Width</strong> text boxes and press the <strong>Enter </strong>key. Or, you can use the Up&nbsp;</span></span><img alt="" height="18" src="Images/8.png" width="16" /><span style="font-family:Merriweather"><span style="color:#4e5f70"> and Down&nbsp;</span></span><img alt="" height="17" src="Images/9.png" width="15" /><span style="font-family:Merriweather"><span style="color:#4e5f70"> arrows to resize the selected graphic object.</span></span></p>

<p style="text-align:justify"><img alt="" height="122" src="Images/cc18.png" width="323" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To change the size of an object manually, follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1</strong></span><span style="color:#4e5f70"> Click on the object you want to resize. A bounding box will appear around the object with control handles that can be used for resizing.</span><br />
<span style="color:#e74c3c"><strong>2</strong></span><span style="color:#4e5f70"> Moving the mouse pointer over these handles will change it to a double-sided arrow&nbsp;</span></span><img alt="" height="16" src="Images/10.png" width="22" /><span style="font-family:Merriweather"><span style="color:#4e5f70"> . By using the corner handles, you can resize an object uniformly without stretching or shrinking it.</span><br />
<span style="color:#e74c3c"><strong>3</strong></span><span style="color:#4e5f70"> Place the pointer over any of the handles, left-click and drag the double-sided arrow inward or outward to decrease or increase the size, respectively.</span></span><br />
<img alt="" height="161" src="Images/cc 20.png" width="373" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>4</strong></span><span style="color:#4e5f70"> Release the mouse button at the desired size to stop resizing.</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="font-size:14px"><span style="color:#27ae60"><strong>Applying Effects</strong></span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">PowerPoint offers different types of visual effects that can be applied on shapes and pictures to enhance their look. Various visual effects include shadow, glow, bevel, soft edges, reflection and 3D rotation.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To apply an effect on a graphic object, follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1 </strong></span><span style="color:#4e5f70">Double-click on the graphic object.</span><br />
<span style="color:#e74c3c"><strong>2</strong></span><span style="color:#4e5f70"> On the <strong>Format </strong>tab, in the <strong>Picture Styles</strong> group (for a picture or clip art), click <strong>Picture Effects</strong>. Or, in the <strong>Shape Styles</strong> group (for a shape), click <strong>Shape Effects</strong>.</span><br />
<span style="color:#e74c3c"><strong>3 </strong></span><span style="color:#4e5f70">From the drop-down menu, choose an effect. </span></span><br />
<img alt="" height="167" src="Images/cc21.png" width="259" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">You can also use the </span><span style="color:#27ae60">Format Picture</span><span style="color:#4e5f70"> dialog box to apply visual effects on picture or clip art. To open the dialog box, right-click on the object and select Format Picture.</span></span></p>

<p style="text-align:justify"><img alt="" height="193" src="Images/cc22.png" width="289" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="font-size:14px"><span style="color:#27ae60"><strong>Creating a Photo Album</strong></span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">PowerPoint lets you create photo albums for your favourite photo collections. You can also add your own text to the photos in the album using text boxes on slides.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To create a photo album in PowerPoint, follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1</strong></span><span style="color:#4e5f70"> Create a PowerPoint presentation. (Press <strong>Ctrl + N</strong> or select <strong>File &rarr; New</strong>)</span><br />
<span style="color:#e74c3c"><strong>2</strong></span><span style="color:#4e5f70"> On the <strong>Insert </strong>tab, in the <strong>Images </strong>group, click the lower part of the <strong>Photo Album</strong> command.</span><br />
<span style="color:#e74c3c"><strong>3 </strong></span><span style="color:#4e5f70">From the drop-down menu, select the <strong>New Photo Album</strong> option.</span></span><br />
<img alt="" height="139" src="Images/cc23.png" width="297" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>4 </strong></span><span style="color:#4e5f70">In the <strong>Photo Album</strong> dialog box, click on the <strong>File/Disk</strong> button under <strong>Insert picture from</strong> to add photos to your photo album.</span></span><br />
<img alt="" height="211" src="Images/c25.png" width="290" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>5</strong></span><span style="color:#4e5f70"> In the <strong>Insert New Pictures</strong> dialog box, choose the photos you want to add to the photo album and then click the <strong>Insert </strong>button. Use the Ctrl or Shift keys to select m</span></span><span style="font-family:Merriweather"><span style="color:#4e5f70">ultiple photos.</span></span></p>

<p style="text-align:justify"><img alt="" height="239" src="Images/cc24.png" width="313" /><br />
<span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>6</strong></span><span style="color:#4e5f70"> To add text description to your photos, click on the <strong>New Text Box</strong> button under <strong>Insert text</strong>.</span><br />
<span style="color:#e74c3c"><strong>7</strong></span><span style="color:#4e5f70"> Click the <strong>Browse </strong>button and select a theme for your photo album in the <strong>Choose Theme</strong> dialog box and then click <strong>Open</strong>.</span><br />
<span style="color:#e74c3c"><strong>8</strong></span><span style="color:#4e5f70"> Now, click on the <strong>Create </strong>button to create the photo album.</span></span></p>

<p style="text-align:justify"><img alt="" height="229" src="Images/cx.png" width="295" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">The photo album appears in a new PowerPoint window. You will notice that for each text box added, there is a new slide.</span></span><br />
<img alt="" height="224" src="Images/ca.png" width="297" /></p>

<p style="text-align:justify"><span style="font-size:14px"><span style="font-family:Merriweather"><span style="color:#27ae60"><strong>Adding Bullets and Numbering&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </strong></span></span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">Similar to Word, you can create bulleted and numbering lists in PowerPoint. These lists help you present information in an clear and easy-to-understand manner. A bulleted or numbering list can be converted into a SmartArt effectively.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To add bullets or numbering to lines of text in your presentation, follow these steps:</span><br />
<span style="color:#e74c3c"><strong>1</strong></span><span style="color:#4e5f70"> Select the text on the slide to which you want to add bullets or numbering.</span><br />
<span style="color:#e74c3c"><strong>2</strong></span><span style="color:#4e5f70"> On the <strong>Home </strong>tab, in the <strong>Paragraph </strong>group, click on the arrow next to <strong>Bullets </strong>command and from the drop-down gallery, choose the bullet style<br />
you want to use.</span></span></p>

<p style="text-align:justify"><img alt="" height="167" src="Images/cs.png" width="235" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <img alt="" height="139" src="Images/cd.png" width="253" /><br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">The selected bullet style gets applied to the selected text. </span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br />
<img alt="" height="177" src="Images/cf.png" width="327" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">Similarly, you can add a numbering style to the selected text.</span><br />
<span style="color:#e74c3c"><strong>3</strong></span><span style="color:#4e5f70"> Click on the arrow next to the <strong>Numbering </strong>command, if you want to add numbering to your text.</span><br />
<span style="color:#e74c3c"><strong>4</strong></span><span style="color:#4e5f70"> From the drop-down gallery, choose the numbering style you want to use. </span></span></p>

<p style="text-align:justify"><img alt="" height="175" src="Images/cg.png" width="316" /><br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">The selected numbering style gets applied to the selected text.</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="font-size:14px"><span style="color:#27ae60"><strong>Showing Visual Cues</strong></span></span>&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">In PowerPoint, ruler, guides and gridlines are visual references that can help in positioning objects on the slides. You can align shapes and other objects in slides using these visual cues.</span><br />
<span style="color:#27ae60">Ruler: </span><span style="color:#4e5f70">It is used to measure and line up objects on the slide.</span><br />
<span style="color:#27ae60">Gridlines: </span><span style="color:#4e5f70">These are dotted lines to which objects on the slide can be aligned.</span><br />
<span style="color:#27ae60">Guides:</span><span style="color:#4e5f70"> These are adjustable drawing guides to which you can align objects in the document.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To show visual cues on a slide, follow these steps:</span><br />
<strong><span style="color:#e74c3c">1</span></strong><span style="color:#4e5f70"> Select the slide.</span><br />
<span style="color:#e74c3c"><strong>2</strong></span><span style="color:#4e5f70"> Select the <strong>View </strong>tab.</span><br />
<span style="color:#e74c3c"><strong>3 </strong></span><span style="color:#4e5f70">In the <strong>Show </strong>group, check the <strong>Ruler</strong>, <strong>Gridlines </strong>and <strong>Guides </strong>options.</span></span></p>

<p style="text-align:justify"><img alt="" height="259" src="Images/ch.png" width="364" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="font-size:14px"><span style="color:#27ae60"><strong>Understanding Presentation Views</strong></span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">PowerPoint offers different ways of viewing PowerPoint slides depending on the task. For example, you can choose a view when you&rsquo;re creating your presentation, and another for delivering your presentation. The </span><span style="color:#27ae60">View </span><span style="color:#4e5f70">tab includes available presentation views such as Normal, Slide Sorter, Notes Page, Reading View, etc. in PowerPoint.</span></span><br />
<img alt="" height="111" src="Images/cv.png" width="521" /><br />
<span style="font-family:Merriweather"><span style="color:#27ae60">Normal view:</span><span style="color:#4e5f70"> This is the default view used mainly for designing your presentations. One slide is shown on the screen at a time in the Slide pane. The view also includes the Notes pane and Outline and Slides tabs.</span></span></p>

<p style="text-align:justify"><img alt="" height="206" src="Images/cb.png" width="279" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#27ae60">Slide Sorter view: </span><span style="color:#4e5f70">This view is mainly used for sorting and organising the sequence of your slides. All slides in the presentation are displayed in thumbnail form. You can sort slides into different categories and sections.</span></span></p>

<p style="text-align:justify"><img alt="" height="203" src="Images/cz.png" width="276" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#27ae60">Notes Page view:</span><span style="color:#4e5f70"> This view is used to type presenter/speaker notes for the current slide in full page format. One slide is shown at a time with its note. This presentation view is located below the Slide pane.</span></span></p>

<p style="text-align:justify"><img alt="" height="197" src="Images/cm.png" width="278" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#27ae60">Reading View: </span><span style="color:#4e5f70">This view is used to view the presentation in full screen as a slide show that fits within the PowerPoint window.</span></span><br />
<img alt="" height="221" src="Images/i.png" width="301" /><br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">To switch to a different view, click on it in the Presentation Views group. You can also click on the view buttons for the most frequently used views at the bottom right of the Status bar to change your existing view.</span></span></p>

<p style="text-align:justify"><img alt="" height="45" src="Images/cc.png" width="424" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="font-size:14px"><span style="color:#27ae60"><strong>Exploring Master Views</strong></span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">Master views are the main slides that store information about the presentation that includes colour scheme, fonts, placeholders, effects and background styles. They allow you to make universal changes to every slide, notes and handouts in a presentation. The master views include Slide, Handout and Notes view. To get to a master view, on the </span><span style="color:#27ae60">View </span><span style="color:#4e5f70">tab, in the</span><span style="color:#27ae60"> Master Views</span><span style="color:#4e5f70"> group, choose the master view that you want. Let us learn about slide master.<br />
Slide masters are useful for designing consistent looking presentation. When you want all your slides to contain the same formatting, it is helpful to create a slide master. If you make changes to a slide master, they&rsquo;ll be applied to all the slides that follow that master. This way you can quickly make changes to multiple slides and slide layouts from one place. You should create and edit your slide master and its layout before you create other slides.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">You can use the slide master to make the following universal changes:</span></span></p>

<ul>
	<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Change the font colour, size and style</span></span></li>
	<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Add a footer, date and slide number</span></span></li>
	<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Add a title and/or picture</span></span></li>
</ul>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#4e5f70">To create and modify a master view, follow these steps:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>1</strong></span><span style="color:#4e5f70"> Open a blank PowerPoint presentation.</span><br />
<span style="color:#e74c3c"><strong>2</strong></span><span style="color:#4e5f70"> On the <strong>View </strong>tab, in the <strong>Master </strong>Views group, click <strong>Slide Master</strong>. </span></span><br />
<img alt="" height="81" src="Images/cw.png" width="352" /><br />
<span style="font-family:Merriweather"><span style="color:#4e5f70">A blank slide master with the associated layouts appears.</span></span></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>3 </strong></span><span style="color:#4e5f70">In the left pane, select the first slide, which is the <strong>slide master</strong>.</span></span></p>

<p style="text-align:justify"><img alt="" height="223" src="Images/ce.png" width="326" /><br />
<span style="font-family:Merriweather"><span style="color:#e74c3c"><strong>4</strong></span><span style="color:#4e5f70"> Use the desired tabs on the Ribbon to make changes to the slide master. For example, for inserting a picture that appears in the lower-right corner of every slide, click the <strong>Picture </strong>command in the <strong>Images </strong>group of the<br />
&nbsp;&nbsp;&nbsp; <strong>Insert </strong>tab.</span><br />
<span style="color:#e74c3c"><strong>5</strong></span><span style="color:#4e5f70"> In the<strong> Insert Picture </strong>dialog box, select the picture and click <strong>Insert</strong>.</span><br />
<span style="color:#e74c3c"><strong>6</strong></span><span style="color:#4e5f70"> Resize and place the logo image accordingly.</span><br />
<span style="color:#e74c3c"><strong>7 </strong></span><span style="color:#4e5f70">To close the Master View, click on the <strong>Close Master view</strong> option given at the end of the <strong>Slide Master</strong> tab.</span></span></p>

<p style="text-align:justify"><img alt="" height="233" src="Images/cr.png" width="340" /></p>

<p style="text-align:justify"><span style="font-family:Merriweather"><span style="font-size:16px"><span style="color:#8e44ad"><strong>&nbsp; SUMMARY</strong></span></span></span></p>

<ul>
	<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">PowerPoint can be used to create presentations using multimedia and text.</span></span></li>
	<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">You can change the look of your slide using templates or themes.</span></span></li>
	<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">You can include decorated text styles in your presentation using WordArt.</span></span></li>
	<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Objects in the slide can be resized, rotated, moved and deleted.</span></span></li>
	<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">PowerPoint also offers the feature of creating a photo album.</span></span></li>
	<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Gridlines allow objects to be positioned precisely in a slide.</span></span></li>
	<li style="text-align: justify;"><span style="font-family:Merriweather"><span style="color:#4e5f70">Bullets and numbering can be used to present a list or lines of text in a presentation.</span></span></li>
</ul>

<p style="text-align:justify"><span style="color:#999999"><span style="font-size:16px"><strong><span style="font-family:Merriweather">Key Terms</span></strong></span></span></p>

<table border="1" cellpadding="1" cellspacing="1" style="width:500px">
	<tbody>
		<tr>
			<td>
			<table border="0" cellpadding="1" cellspacing="1" style="width:500px">
				<tbody>
					<tr>
						<td>
						<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Template </span></span></p>
						</td>
						<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Theme </span></span></td>
						<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Picture Effects </span></span></td>
						<td>
						<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Shape Effects</span></span></p>
						</td>
					</tr>
					<tr>
						<td>
						<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Gridlines</span></span></p>
						</td>
						<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Guides </span></span></td>
						<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Ruler </span></span></td>
						<td>
						<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Presentation Views</span></span></p>
						</td>
					</tr>
				</tbody>
			</table>
			</td>
		</tr>
	</tbody>
</table>
