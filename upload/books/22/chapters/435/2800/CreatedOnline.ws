<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>Working with Tables in Word 2010</strong></span></span></span></p>

<table border="1" cellpadding="1" cellspacing="1" style="width:500px">
	<tbody>
		<tr>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:16px"><strong><span style="font-family:Merriweather">Learning Objectives</span></strong></span></span></p>

			<ul>
				<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Understand tables and their use in Word</span></span></li>
				<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Learn how to insert a table</span></span></li>
				<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Understand how to add data into tables</span></span></li>
				<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Know how to add and delete rows or columns</span></span></li>
				<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Learn how to merge and split cells</span></span></li>
				<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Know how to change column width and resize a table</span></span></li>
				<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Learn how to apply borders and shading</span></span></li>
				<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Know how to perform calculations in a table</span></span></li>
			</ul>
			</td>
		</tr>
	</tbody>
</table>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Tables help present data in an organised manner. Word 2010 offers different features to work with tables including the ability to perform calculations in a cell. An example of how data can be organised using a table is shown below:</span></span></p>

<table border="1" cellpadding="1" cellspacing="1" style="width:500px">
	<tbody>
		<tr>
			<td colspan="1">
			<p style="text-align:justify"><span style="color:#4e5f70"><strong><span style="font-family:Merriweather">Example</span></strong></span></p>
			</td>
			<td style="text-align:justify"><span style="color:#4e5f70"><strong><span style="font-family:Merriweather">Word</span></strong></span></td>
			<td style="text-align:justify"><span style="color:#4e5f70"><strong><span style="font-family:Merriweather">Latin Root 1</span></strong></span></td>
			<td style="text-align:justify"><span style="color:#4e5f70"><strong><span style="font-family:Merriweather">Latin Root 2</span></strong></span></td>
			<td style="text-align:justify"><span style="color:#4e5f70"><strong><span style="font-family:Merriweather">Meaning</span></strong></span></td>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><strong><span style="font-family:Merriweather">Translation</span></strong></span></p>
			</td>
		</tr>
		<tr>
			<td colspan="1" rowspan="3">
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">abductor<br />
			digiti<br />
			minimi</span></span></p>
			</td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">abductor</span></span></td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">ab = away from</span></span></td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">duct = to move</span></span></td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">a muscle that<br />
			moves away<br />
			from</span></span></td>
			<td colspan="1" rowspan="3">
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">A muscle<br />
			that moves<br />
			the little<br />
			finger or toe<br />
			away</span></span></p>
			</td>
		</tr>
		<tr>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">digiti</span></span></p>
			</td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">digitus = digit</span></span></td>
			<td style="text-align:justify">&nbsp;</td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">refers to a<br />
			finger or toe</span></span></td>
		</tr>
		<tr>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">minimi</span></span></p>
			</td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">minimus = mini,<br />
			tiny</span></span></td>
			<td style="text-align:justify">&nbsp;</td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">little</span></span></td>
		</tr>
		<tr>
			<td colspan="1" rowspan="3">
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">adductor<br />
			digiti<br />
			minimi</span></span></p>
			</td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">adductor</span></span></td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">ad = to, toward</span></span></td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">duct = to move</span></span></td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">a muscle that<br />
			moves towards</span></span></td>
			<td colspan="1" rowspan="3">
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">A muscle<br />
			that moves<br />
			the little<br />
			finger or toe<br />
			toward</span></span></p>
			</td>
		</tr>
		<tr>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">digiti</span></span></p>
			</td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">digitus = digit</span></span></td>
			<td style="text-align:justify">&nbsp;</td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">refers to a<br />
			finger or toe</span></span></td>
		</tr>
		<tr>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">minimi</span></span></p>
			</td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">minimus = mini,<br />
			tiny</span></span></td>
			<td style="text-align:justify">&nbsp;</td>
			<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">little</span></span></td>
		</tr>
	</tbody>
</table>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>What is a Table?</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">A table is a grid of cells arranged in the form of rows and columns. An individual box or the cross-section of a row and column is called a cell. Horizontally aligned cells are called rows and vertically aligned cells are called columns.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="147" src="Images/c1.png" width="254" /><br />
<span style="font-size:14px"><span style="font-family:Merriweather"><strong>Inserting a Table</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">We can insert a table in a Word document to organise data in tabular form. There are different methods to create a table such as using the grid, the Insert Table command or designing your own table by drawing cells. In addition, selected text can also be converted to a table.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To quickly insert a table using the grid in Word 2010, follow these steps:<br />
<strong>1</strong> Open the document and click where you want to create a table.<br />
<strong>2 </strong>On the <strong>Insert </strong>tab, in the <strong>Tables </strong>group, click on <strong>Table</strong>. A drop-down menu appears with a grid at the top.<br />
<strong>3 </strong>Move the cursor over the grid until you highlight the number of columns and rows you want.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="202" src="Images/c2.png" width="305" /><br />
<span style="font-family:Merriweather"><strong>4</strong> Click on the grid to insert the table. </span><br />
<img alt="" height="220" src="Images/c3.png" width="328" /><br />
<span style="font-family:Merriweather">When a table is inserted, the Design tab appears under the Table Tools contextual tab which also appears when you click anywhere in a table. The Design tab can be used to apply predefined styles to your table or change the background colour of the cells.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To insert a table using the Insert Table option, follow these steps:<br />
<strong>1</strong> Click where you want to insert the table.<br />
<strong>2</strong> On the <strong>Insert </strong>tab, in the <strong>Tables </strong>group, click on <strong>Table</strong>.<br />
<strong>3</strong> From the drop-down menu, select the <strong>Insert Table</strong> option.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="284" src="Images/c4.png" width="259" /><br />
<span style="font-family:Merriweather"><strong>4</strong> In the Insert Table dialog box, define the table size by entering the number of columns and rows you want in the <strong>Number of columns</strong> and <strong>Number of rows</strong> text fields, respectively.<br />
<strong>5</strong> Type the column width or use the arrows to set the value for the<strong> Fixed column width</strong>.<br />
<strong>6</strong> Click the <strong>OK </strong>button.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="244" src="Images/c5.png" width="221" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <img alt="" height="133" src="Images/c6.png" width="293" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">A table with specified number of columns and rows will appear in the document. By default, the background colour of the cells is white and border is black.&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Adding Data in a Table</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">After inserting a table in a document, you can enter data in its cells. You can add different types of data into a cell such as text, picture, SmartArt and even another table. Simply, click the cell in which you want to enter data and then type the text. You can also paste the text or picture into a cell. You can also use the Insert tab to insert other graphical objects in a table cell.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To add text in a table cell, follow these steps:<br />
<strong>1</strong> Click on a cell in the table where you want to enter text.<br />
<strong>2</strong> Type in your text in the selected cell.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="106" src="Images/c7.png" width="299" /><br />
<span style="font-family:Merriweather"><strong>3</strong> Press the <strong>Tab </strong>key to move to the next cell and type the text for that cell.<br />
<strong>4</strong> Repeat the steps to enter text for other cells.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="115" src="Images/c8.png" width="297" /><br />
<span style="font-family:Merriweather"><strong>5</strong> To insert a picture in a cell, click on the cell and then select the <strong>Insert </strong>tab and then <strong>Picture</strong>.<br />
<strong>6</strong> In the <strong>Insert Picture</strong> dialog box, select the picture and click <strong>Insert</strong>. This will insert the picture into the table cell.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Adding Rows and Columns in a Table</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">You can also add more table rows and columns in an inserted table if you want to add more content in the table. You can insert a row directly above or below the selected cell or row, and insert a column directly to the left or right of the selected cell or column. You can do this using the Layout tab under the Table Tools contextual tab, which only appears when you click in a table or select it.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To add a new row in a table, follow these steps:<br />
<strong>1</strong> Place the Insertion point in the cell, above or below which you want to insert a row.<br />
<strong>2</strong> Select the <strong>Layout </strong>tab under the<strong> Table Tools</strong> contextual tab.<br />
<strong>3 </strong>In the <strong>Rows &amp; Columns</strong> group, click on <strong>Insert Above</strong> to insert a new row above or click on <strong>Insert Below</strong> to insert a new row below the selection.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="161" src="Images/c9.png" width="302" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Following figure shows a new row is inserted above the selection.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="89" src="Images/c10.png" width="330" /><br />
<span style="font-family:Merriweather">To add a column in a table, follow these steps:<br />
<strong>1</strong> Place the <strong>Insertion point </strong>in the cell.<br />
<strong>2</strong> On the <strong>Layout </strong>tab, in the <strong>Rows &amp; Columns</strong> group, select<strong> Insert Left</strong> to insert a column to the left or select <strong>Insert Right</strong> to insert a column to the right of the selection.&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Follow</span><span style="font-family:Merriweather">ing figure shows a new row is inserted to the right of the selection.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="96" src="Images/c11.png" width="309" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <img alt="" height="126" src="Images/c12.png" width="243" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">You can also right-click in a cell and select the Insert option from the context menu. From the submenu, select an option as per your requirement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Deleting Rows and Columns in a Table</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">You can always delete any table row or column if you no longer need it.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To delete a row or column in a table, follow these steps:<br />
<strong>1</strong> In the table, click on the row or column you want to delete.<br />
<strong>2</strong> On the <strong>Layout </strong>tab, in the <strong>Rows &amp; Columns</strong> group, click on <strong>Delete</strong>.<br />
<strong>3 </strong>From the drop-down menu, click on <strong>Delete Columns</strong> or <strong>Delete Rows</strong> to delete a column or row respectively from the table.</span><br />
<img alt="" height="171" src="Images/c13.png" width="273" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">You can also delete the entire table by selecting the Delete Table option in the drop-down.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">You can also delete an individual cell by right-clicking on the cell and then selecting the Delete Cells option from the context menu.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="148" src="Images/c14.png" width="340" /><br />
<span style="font-family:Merriweather">The Delete Cells dialog box will open. Select an option and then click on OK.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="159" src="Images/c15.png" width="197" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Merging and Splitting Cells in a Table</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">In order to format your table according to the content/data, you can work with cells by combining or dividing them. Let&rsquo;s discuss merging and splitting of columns and rows.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Merging Cells</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Merging means combining two or more table cells located in the same row or column into one cell. For example, you can merge multiple cells together for a table heading.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To merge two or more cells in a table, follow these steps:<br />
<strong>1</strong> In the table, select the cells that you want to merge.<br />
<strong>2 </strong>On the <strong>Layout </strong>tab, click on <strong>Merge Cells</strong> in the <strong>Merge </strong>group.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="312" src="Images/c16.png" width="325" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">All the selected cells merge to create one cell. The width of the merged cell spans the combined width of the selected cells.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:16px"><span style="font-family:Merriweather"><strong>Smart tip</strong></span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Use the up and down arrow keys to jump from one row to another. Similarly, use the left and right arrow keys to move from one column to another.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Splitting Cells</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Splitting means dividing a cell into two or more cells. This process is the opposite of merging.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To split a cell, follow these steps:<br />
<strong>1 </strong>Select the cell or cells that you want to split.<br />
<strong>2</strong> On the Layout tab, in the Merge group, click on <strong>Split Cells</strong>.</span><br />
<img alt="" height="217" src="Images/c17.png" width="324" /><br />
<span style="font-family:Merriweather">The Split Cells dialog box will appear.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="176" src="Images/c18.png" width="226" /><br />
<span style="font-family:Merriweather"><strong>3</strong> Type the number of columns and rows that you want to split the selected cells into.<br />
<strong>4 </strong>Then, click on <strong>OK</strong>.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">The selected cell is divided into two cells.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="104" src="Images/c19.png" width="354" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Changing the Column Width</strong>&nbsp;&nbsp;&nbsp;&nbsp; </span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">The default column width in a table can be adjusted according to our needs. We can adjust the column width either manually or using the Table Properties dialog box.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><strong>Method 1:</strong><br />
To adjust the column width manually, follow these steps:<br />
<strong>1</strong> Move the cursor above the column border.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="149" src="Images/c20.png" width="350" /><br />
<span style="font-family:Merriweather"><strong>2 </strong>Click and drag to adjust the width of the column. Drag left to decrease the column width and drag right to increase the column width.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="114" src="Images/c21.png" width="323" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><strong>Method 2:</strong><br />
To either increase or decrease the column width using the Table Properties dialog box, follow these steps:<br />
<strong>1</strong> Click in the cell of the column for which you want to adjust the width.<br />
<strong>2 </strong>Right-click and then select <strong>Table Properties</strong>. The <strong>Table Properties</strong> dialog box appears.<br />
<strong>3 </strong>Under <strong>Size</strong>, check the <strong>Preferred width</strong> option, which will enable the option.<br />
<strong>4</strong> Type the column width or use the arrows to adjust the value.<br />
<strong>5</strong> Click <strong>OK </strong>to save the changes made.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="270" src="Images/c22.png" width="261" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:16px"><span style="font-family:Merriweather"><strong>Smart tip</strong></span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To display row height measurement on the vertical ruler, click in a cell and then hold down option as you drag the boundary</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Resizing a Table</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">The table size can be increased or decreased as per your wish.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To resize a table, follow these steps:<br />
<strong>1</strong> Move the cursor over the small square at the bottom-right corner of the table you want to resize.</span><br />
<img alt="" height="134" src="Images/c23.png" width="310" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><strong>2 </strong>The cursor will change to a resize icon which is a diagonal double-headed arrow&nbsp;</span><img alt="" height="16" src="Images/3.png" width="16" /><span style="font-family:Merriweather"> . Click and drag to resize the table.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="143" src="Images/c24.png" width="259" /><br />
<span style="font-family:Merriweather"><strong>3</strong> Release the mouse button when you get the required size of the table.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:16px"><strong><span style="font-family:Merriweather">Know More</span></strong></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">The dotted lines that you see while dragging the table indicate the size of the table.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Formatting a Table</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">There are a variety of ways to format an inserted table in Word. For example, you can change the appearance of a table with table styles, apply border and shading, and perform several other tasks easily using the various functions and features available in Word.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Apply Table Styles</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">A style is a predefined set of formatting options that can be applied to a table to format it. By using table styles, we can save a lot of time. The Design tab under the Table Tools contextual tab includes the Table Styles gallery.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To apply a style to a table, follow these steps:<br />
<strong>1</strong> Move the cursor over the table. You will see a small icon near the top-left corner of the table. Click the icon to select the entire table.</span><br />
<img alt="" height="220" src="Images/c25.png" width="314" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><strong>2</strong> Select the <strong>Design </strong>tab under the <strong>Table Tools</strong> contextual tab.<br />
<strong>3</strong> In the <strong>Table Styles</strong> group, click the <strong>More&nbsp;</strong></span><img alt="" height="16" src="Images/4.png" width="15" /><span style="font-family:Merriweather"><strong> </strong>button to open a gallery.<br />
<strong>4</strong> Click on a visual style from the gallery to apply it to the selected table.</span><br />
<img alt="" height="263" src="Images/c26.png" width="305" /><br />
<span style="font-family:Merriweather">The visual style applies to the table.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="156" src="Images/c27.png" width="291" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:16px"><span style="font-family:Merriweather"><strong>Smart tip</strong></span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To select an entire table, click anywhere in the table and press <strong>Alt + 5</strong>. (Press 5 from the numeric keypad with Num Lock off).</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Applying Borders and Shading</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">We can also format a table by applying borders and shading (background colour) to a cell, multiple cell or the entire table. Shading allows you to change the default colour (white) behind the selected text or paragraph. </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><strong>Method 1:</strong><br />
To apply border and shading to table cells, follow these steps:<br />
<strong>1 </strong>Select the cells in a table.<br />
<strong>2 </strong>Select the <strong>Design </strong>tab, and in the <strong>Table Styles </strong>group, click the arrow next to <strong>Shading</strong>.<br />
<strong>3 </strong>From the drop-down colour palette, click on a colour of your choice to change the shading of the selected cells.</span><br />
<span style="font-family:Merriweather"><strong>4</strong> Click the arrow next to <strong>Border </strong>and choose a border type such as <strong>All Borders</strong>.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="433" src="Images/c28.png" width="317" /><br />
<span style="font-family:Merriweather">This will add borders to all sides of the selected cells.</span><br />
<img alt="" height="120" src="Images/5.png" width="321" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">You can also use the Borders and Shadings dialog box to change the border and shadings in a table. To open the dialog box, right-click anywhere in the table and choose Borders and Shading from the context menu. Now, change the desired options to create a custom border style. In the Apply to drop-down menu choose an option to which you want to apply the formatting and click OK.<br />
Similarly, you can change the shading using the Shading tab in the Borders and Shading dialog box.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="218" src="Images/c29.png" width="292" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">You can also use the various options in the Draw Borders group to draw a table with custom borders. The options include the following:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Line Style: Change the style of the line used to draw the borders.<br />
Line Weight: Change the width of the line used to draw the borders&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:14px"><strong>Performing Calculations in a Table</strong></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Word allows you to insert a formula in an empty table cell in order to perform a simple calculation. You can also use the Formula command to create your own formula or paste a function such as AND, AVERAGE, COUNT, IF, etc. Like Excel, formulas in Word starts with an equal to (=) sign. The Formula command is found in the Data group on the Layout tab which is under the Table Tools contextual tab.&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">To perform a calculation in a table, follow these steps:<br />
<strong>1 </strong>Open a document with a table or create one and add data in it.<br />
<strong>2</strong> Select an empty cell where you want to show the result of the calculation.<br />
<strong>3</strong> On the <strong>Layout </strong>tab, in the <strong>Data </strong>group, click the <strong>Formula </strong>command.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">This opens the Formula dialog box which you can use to create your formula. You will see a formula automatically added in the Formula box.</span><br />
<img alt="" height="72" src="Images/c31.png" width="155" /><br />
<span style="font-family:Merriweather"><strong>4</strong> Click the <strong>OK </strong>button. </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="164" src="Images/c32.png" width="280" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">The total value appears in the selected cell.</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><img alt="" height="150" src="Images/c33.png" width="306" /></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">In the formula =SUM(ABOVE), SUM is the name of the function and (ABOVE) is called positional argument. Other positional arguments include (LEFT, RIGHT, BELOW) which you can use with various functions that include AVERAGE, COUNT, MAX, MIN, PRODUCT and SUM.<br />
<br />
Following table explains the use of various positional arguments with the SUM function.</span></span></p>

<table border="1" cellpadding="1" cellspacing="1" style="height:311px; width:733px">
	<tbody>
		<tr>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><strong><span style="font-family:Merriweather">Formula</span></strong></span></p>
			</td>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><strong><span style="font-family:Merriweather">Use</span></strong></span></p>
			</td>
		</tr>
		<tr>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">=SUM(ABOVE)</span></span></p>
			</td>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Adds up all the numbers above the cell you have selected.</span></span></p>
			</td>
		</tr>
		<tr>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">=SUM(LEFT)</span></span></p>
			</td>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Adds up all the numbers in the row located to the left of the cell you have selected.</span></span></p>
			</td>
		</tr>
		<tr>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">=SUM(RIGHT)</span></span></p>
			</td>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Adds up all the numbers in the row located to the right of the cell you have selected.</span></span></p>
			</td>
		</tr>
		<tr>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">=SUM(BELOW)</span></span></p>
			</td>
			<td>
			<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Adds up all the numbers below the cell you have selected.</span></span></p>
			</td>
		</tr>
	</tbody>
</table>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather"><span style="font-size:16px"><strong>SUMMARY</strong></span></span></span></p>

<ul>
	<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Tables help to keep data organised.</span></span></li>
	<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">A table is a grid of cells arranged in rows and columns.</span></span></li>
	<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">A table can be resized either manually or using the Table Properties dialog box.</span></span></li>
	<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">We can add and delete any number of columns or rows in an existing table.</span></span></li>
	<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Merging cells is the process of combining two or more cells into one cell.</span></span></li>
	<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Splitting cells is the process of dividing a cell into multiple cells.</span></span></li>
	<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">The width of a column can be changed using the Layout tab.</span></span></li>
	<li style="text-align: justify;"><span style="color:#4e5f70"><span style="font-family:Merriweather">Calculations are performed in a table using built-in formulas in Word 2010.</span></span></li>
</ul>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:16px"><strong><span style="font-family:Merriweather">Key Terms</span></strong></span></span></p>

<table border="1" cellpadding="1" cellspacing="1" style="width:500px">
	<tbody>
		<tr>
			<td>
			<table border="0" cellpadding="1" cellspacing="1" style="width:500px">
				<tbody>
					<tr>
						<td>
						<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Row </span></span></p>
						</td>
						<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Column </span></span></td>
						<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Cell </span></span></td>
						<td>
						<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Merge</span></span></p>
						</td>
					</tr>
					<tr>
						<td>
						<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Split</span></span></p>
						</td>
						<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Border </span></span></td>
						<td style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Formula </span></span></td>
						<td>
						<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Merriweather">Table Style</span></span></p>
						</td>
					</tr>
				</tbody>
			</table>
			</td>
		</tr>
	</tbody>
</table>
