@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
	font-size:120%;
	line-height:150%;
	padding:2%;
	text-align:justify;
}

.chapterHeading {
	text-align:center;
	font-size:175%;
	font-weight:bold;
	color:#0CF;
}

.chapterNumber
{
font-style: italic; 
	font-size:1em; 
	color: #00aeef;
}
.black1 {
	font-size:30px;
	font-weight:bold;
	color:#000;
	text-align:center;
}
.black2 {
	font-size:135%;
	color:#000;
	font-weight:bold;
	margin-bottom:1%;
}
/* Concept Heading */
.ConceptHeading
{
color:#00aeef;
font-size:1.3em;
font-weight:bold;
margin-top:40px;
}
.clear
{
 clear:both;
}
/* Sub Heading */
.subheading
{
color:#00aeef;
font-size:1.1em;
font-weight:bold;
}
.SubHeading2
{
color:#00aeef;
font-size:1em;
font-weight:bold;
}
.questions {
font-size:125%;
margin:2% 0;
color:rgb(222,118,28);
}
.subHeading1 {
	font-size:120%;
	color:#0CF;
	font-weight:bold;
	margin-bottom:1%;
}
.meaning {
	font-size:95%;
}

.image {
	text-align:center;
}

.activity {
	background: #6CF;
	padding:1%;
}
.note
{
	background: #69C;
	padding:1%;
}
.englishMeaning
{
	font-family:Arial, Helvetica, sans-serif;
	font-size:80%;
}
.bold
{
	font-size:110%;
	font-weight:bold;
	
}
.superscript{
position:relative;
top:-15%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
bottom:-18%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}
.Center
{
	text-align:center;
}
.color {
	background: #6CF;
	
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 95%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#266A2E;

padding:10px;

width:30%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.lining_box
{
border:1px solid #000;
padding:5px;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	max-width:100%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
.img_wid2
{
	margin-left: 0;
    margin-right: 0;
	display: inline;
	width:auto;
}
ul

{

margin-left:45px;

}

.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}

p

{

margin-top:10px;

}

h2
{
color:#fff;
font-size:1.3em;
background:#136AA7;
padding:10px;
}
/* Chapter number */
h4
{
color:#136AA7;
font-size:1.3em;
}
.footer

{

display:none;

}

table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
background-color: #ffe3b8;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
.activityBox{
background-color:#EFF5D9;
padding: 15px;
line-height:120%;
}
.lining_box2
{
border:2px solid #87915F;
padding:15px;
border-radius:15px;
}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:50%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}

#prelims .char-style-override-8, .char-style-override-23
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-3, .para-style-override-9
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}