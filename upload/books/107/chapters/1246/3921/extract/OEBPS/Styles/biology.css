html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
span.char-style-override-1 {
	color:#007e47;
	font-family:<PERSON>man, serif;
	font-size:0.833em;
	font-style:normal;
	font-weight:600;
}
span.char-style-override-2 {
	color:#007e47;
	font-family:"Bookman-Light";
	font-size:0.833em;
	font-style:normal;
	font-weight:500;
}
p.title-head {
	color:#0074be;
	font-size:1.833em;
}
span.char-style-override-14 {
	color:#00aeef;
}
.image {
text-align:center;
}

.subjectHead {
text-align:right;
text-transform:uppercase;
font-size:150%;
margin-bottom:3%;
color:rgb(222,118,28);
}

.chapterText {
font-size:130%;
}

.mainHead {
font-size:120%;
font-weight:bold;
margin:2% 0;
}

.activity {
font-size:120%;
color:rgb(0, 174, 239);
margin:2% 0;
}

.endnote {
font-size:95%;
padding:2%;
}

.questions {
font-size:125%;
margin:2% 0;
color:rgb(222,118,28);
}

.exercises {
color:rgb(46, 49, 146);
font-size:115%;
margin:2% 0;
}
.center {
	text-align: center;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.activityBox{
background-color:#EFF5D9;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.lining_box2
{
border:2px solid #87915F;
padding:15px;
border-radius:15px;
}


.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
p
{
	margin-top:10px;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.3em;
background:#136AA7;
padding:10px;
}
/* Chapter number */
h4
{
color:#136AA7;
font-size:1.3em;
}
.chapterNumber
{
font-style: italic; 
	font-size: 0.9em; 
	color: #00aeef;
}
.char-style-override-16
{
font-weight:bold;
}
.char-style-override-17
{
font-style:italic;
}
/* Concept Heading */
.ConceptHeading
{
color:#00aeef;
font-size:1.3em;
font-weight:bold;
margin-top:40px;
}
/* Sub Heading */
.subheading
{
color:#00aeef;
font-size:1.1em;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#00aeef;
font-size:1em;
font-weight:bold;
}
.footer
{
	display:none;
}
table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
background-color: #ffe3b8;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
.clear
{
clear:both;
}
.conc
{
	color:#006699;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;
line-height:150%;
top:60%;

font-weight:bold;

font-size:1.5em;

color:#fff;

}

div.chapter_pos div

{

background:#421C52;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.cover_img_small
{
width:50%;
}

@media only screen and (max-width: 767px) 
{
div.chapter_pos
{
top:50%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:80%;
}
.cover
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.bold
{
	font-weight:bold;
}
body {
font-family:"Arial";
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}
#prelims
{
	line-height:200%;
}
#prelims .char-style-override-15, .char-style-override-315
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#00aeef;
}
#prelims .char-style-override-31
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#00aeef;
}



