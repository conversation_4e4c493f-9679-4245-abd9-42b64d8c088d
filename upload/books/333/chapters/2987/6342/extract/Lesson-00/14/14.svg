<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 935 1265" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<style type="text/css"><![CDATA[
.g0_14{
fill: #231F20;
}
.g1_14{
fill: none;
stroke: #231F20;
stroke-width: 1.5583333;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g2_14{
fill: none;
stroke: #939598;
stroke-width: 6.141667;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g3_14{
fill: #2E3092;
stroke: #2E3092;
stroke-width: 1.5583333;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
.g4_14{
fill: #F58220;
stroke: #F58220;
stroke-width: 1.5583333;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
.g5_14{
fill: none;
stroke: #231F20;
stroke-width: 1.491111;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g6_14{
fill: #F58220;
}
.g7_14{
fill: #FFFFFF;
stroke: #FCF9F8;
stroke-width: 0.16041668;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g8_14{
fill: #FFFFFF;
stroke: #F8F8F7;
stroke-width: 0.16041668;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g9_14{
fill: #F58220;
stroke: #FFFFFF;
stroke-width: 0.7455556;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
]]></style>
</defs>
<image x="247" y="105" width="580" height="31" xlink:href="shade/1.png" />
<path d="M55,1178.4h50.3v-24.9H55v24.9Z" class="g0_14" />
<path d="M55,1179.2H825" class="g1_14" />
<path d="M233.8,105.9V1127.5" class="g2_14" />
<image preserveAspectRatio="none" x="55" y="222" width="86" height="100" xlink:href="img/1.png" />
<path d="M132.4,309.3h98M55,309.3H97.1" class="g1_14" />
<path fill-rule="evenodd" d="M308.9,989.6H595.7v27.8H308.9V989.6Z" class="g3_14" />
<image preserveAspectRatio="none" x="248" y="958" width="59" height="61" xlink:href="img/2.png" />
<path fill-rule="evenodd" d="M305.9,795.2H473.6v26.1H305.9V795.2Z" class="g4_14" />
<path d="M251.2,768.2h48.7c1.3,0,2.4,1.2,2.4,2.6v48.4c0,1.4,-1.1,2.5,-2.4,2.5H251.2c-1.3,0,-2.4,-1.1,-2.4,-2.5V770.8c0,-1.4,1.1,-2.6,2.4,-2.6Z" class="g5_14" />
<path fill-rule="evenodd" d="M269.6,778.9c1,-4.3,9.8,-5,12,-.1c.3,1,.3,3.4,0,4.9l-12.1,-.1c-.3,-1.4,-.2,-3.1,.1,-4.7Z" class="g6_14" />
<path fill-rule="evenodd" d="M272.5,779h.1c0,1.4,0,3.3,.2,4.7l-.4,-.1c.2,-1.4,.2,-3.1,.1,-4.6Z" class="g7_14" />
<path fill-rule="evenodd" d="M278.4,778.9h.2c-.1,1.5,-.1,3.4,.1,4.8l-.3,-.1c.1,-1.4,.1,-3.3,0,-4.7Z" class="g8_14" />
<path fill-rule="evenodd" d="M275.7,771.9c1.1,0,2.3,.9,2.3,2.7c0,1.9,-.8,3.7,-2.2,3.8c-1.4,0,-2.4,-2,-2.4,-3.8c0,-1.8,1.2,-2.7,2.3,-2.7Z" class="g9_14" />
<path fill-rule="evenodd" d="M257.1,795.7c-3.7,-2.4,-1.3,-10.9,4,-11.3c1.1,.1,3.3,.9,4.6,1.7l-4.1,11.3c-1.5,-.1,-3.1,-.8,-4.5,-1.7Z" class="g6_14" />
<path fill-rule="evenodd" d="M258.3,793l-.1,-.1c1.4,.6,3.2,1.2,4.5,1.5l-.2,.3c-1.2,-.7,-2.9,-1.2,-4.2,-1.7Z" class="g7_14" />
<path fill-rule="evenodd" d="M260.2,787.4v-.1c1.4,.6,3.2,1.2,4.5,1.5l-.1,.3c-1.3,-.6,-3.1,-1.2,-4.4,-1.7Z" class="g8_14" />
<path fill-rule="evenodd" d="M252.6,787.6c.4,-1,1.7,-1.8,3.4,-1.2c1.7,.6,3.2,2,2.8,3.4c-.5,1.3,-2.7,1.6,-4.4,.9c-1.8,-.6,-2.1,-2.1,-1.8,-3.1Z" class="g9_14" />
<path fill-rule="evenodd" d="M268.9,812.4c-3.5,2.6,-10.6,-2.5,-9.2,-7.6c.4,-1,2,-2.9,3.2,-3.8l9.2,7.8c-.6,1.3,-1.8,2.6,-3.2,3.6Z" class="g6_14" />
<path fill-rule="evenodd" d="M266.8,810.4c.9,-1.1,2.1,-2.6,2.8,-3.8l.3,.4c-1.1,.9,-2.2,2.3,-3.1,3.4Z" class="g7_14" />
<path fill-rule="evenodd" d="M262.2,806.7v-.1c.9,-1.1,2.2,-2.6,2.9,-3.8l.2,.3c-1,1,-2.2,2.4,-3.1,3.6Z" class="g8_14" />
<path fill-rule="evenodd" d="M259.8,813.8c-.8,-.7,-1.1,-2.1,0,-3.5c1.2,-1.5,3,-2.4,4.2,-1.5c1,.8,.5,3,-.6,4.4c-1.2,1.5,-2.7,1.3,-3.6,.6Z" class="g9_14" />
<path fill-rule="evenodd" d="M288.7,807c1.2,4.1,-6.1,9.1,-10.3,6c-.8,-.7,-2,-2.8,-2.5,-4.3l10.5,-6c1,1.1,1.8,2.7,2.3,4.3Z" class="g6_14" />
<path fill-rule="evenodd" d="M286.1,808.2l-.1,.1c-.7,-1.3,-1.7,-2.9,-2.5,-3.9l.4,-.2c.5,1.3,1.4,2.8,2.2,4Z" class="g7_14" />
<path fill-rule="evenodd" d="M281,811.3h-.1c-.7,-1.2,-1.7,-2.9,-2.5,-4l.3,-.1c.6,1.3,1.6,2.9,2.3,4.1Z" class="g8_14" />
<path fill-rule="evenodd" d="M286.9,816c-.9,.5,-2.4,.4,-3.3,-1.2c-1,-1.6,-1.2,-3.6,0,-4.4c1.2,-.7,3.1,.5,4,2.1c.9,1.6,.2,3,-.7,3.5Z" class="g9_14" />
<path fill-rule="evenodd" d="M290.3,786.6c4.4,.2,6.6,8.8,2.2,11.7c-1,.5,-3.4,.9,-4.9,.9l-2,-11.9c1.3,-.6,3.1,-.8,4.7,-.7Z" class="g6_14" />
<path fill-rule="evenodd" d="M290.6,789.4l.1,.1c-1.4,.2,-3.3,.6,-4.6,1v-.4c1.4,0,3.1,-.4,4.5,-.7Z" class="g7_14" />
<path fill-rule="evenodd" d="M291.8,795.2v.2c-1.4,.2,-3.4,.5,-4.6,.9l-.1,-.2c1.4,-.2,3.3,-.6,4.7,-.9Z" class="g8_14" />
<path fill-rule="evenodd" d="M298.2,791.3c.2,1.1,-.5,2.4,-2.3,2.7c-1.8,.3,-3.7,-.1,-4.1,-1.5c-.2,-1.3,1.5,-2.7,3.4,-3c1.8,-.3,2.8,.8,3,1.8Z" class="g9_14" />
<path fill-rule="evenodd" d="M275.3,808.5c7.5,0,13.6,-5.8,13.6,-13c0,-7.3,-6.1,-13.1,-13.6,-13.1c-7.5,0,-13.6,5.8,-13.6,13.1c0,7.2,6.1,13,13.6,13Z" class="g9_14" />
</svg>