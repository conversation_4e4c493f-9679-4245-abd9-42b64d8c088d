package com.wonderslate.content

class ContentExamDtl {

    Long contentExamMstId
    Integer year
    String month
    String shift
    String questionPaperPath
    String extractedContent
    String createdBy
    Date dateCreated

    static constraints = {
        contentExamMstId nullable: false
        year nullable: false
        month nullable: true
        shift nullable: true
        questionPaperPath blank: true, nullable: true
        extractedContent blank: true, nullable: true
    }


    String toString() {
        "${exam?.examName} - ${year} ${month ?: ''} ${shift ?: ''}"
    }
}
