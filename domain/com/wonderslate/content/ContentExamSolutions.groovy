package com.wonderslate.content

class ContentExamSolutions {
    Long contentExamDtlId
    String question
    String questionType
    String answer

    String option1
    String option2
    String option3
    String option4
    String option5
    String solution
    String difficultyLevel

    BigDecimal marks
    BigDecimal negativeMark

    String topic
    String subtopic
    Long directionId

    static constraints = {
        contentExamDtlId nullable: false
        question blank: false
        answer blank: false

        option1 nullable: true
        option2 nullable: true
        option3 nullable: true
        option4 nullable: true
        option5 nullable: true

        marks nullable: false, scale: 2
        negativeMark nullable: false, scale: 2

        topic nullable: true
        subtopic nullable: true
        directionId nullable: true
        solution nullable: true
        difficultyLevel nullable: true
    }



    String toString() {
        "Q: ${question?.take(50)}..."
    }
}
