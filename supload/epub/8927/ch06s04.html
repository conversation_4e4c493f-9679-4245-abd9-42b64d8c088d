<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Final test</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Final test"><div class="titlepage"><div><div><h1 class="title"><a id="ch06lvl1sec38"/>Final test</h1></div></div></div><p>To test the <a id="id290" class="indexterm"/>prototype, I used some tricks to simulate the baby: I got the crying sound on the Internet and simply reproduced it with an audio player. Regarding the breath, I used doll, manually pressurizing its chest in time with my breathing. I admit it's not the best test, but my children are too big to help me in these experiments!</p><p>To set up all peripherals and drivers, we can use <code class="literal">SYSINIT.sh</code>, as in the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# ./SYSINIT.sh</strong></span>
<span class="strong"><strong>done!</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note66"/>Note</h3><p>This command can be found in the <code class="literal">chapter_06/SYSINIT.sh</code> file in the book's example code repository.</p></div></div><p>Then, I executed both the <code class="literal">adc</code> and <code class="literal">lcd.sh</code> programs by using the following command line in order to send all outputs to the terminal that runs on the tiny LCD:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# ./adc | ./lcd.sh &gt; /dev/tty0</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip84"/>Tip</h3><p>Note that on the <a id="id291" class="indexterm"/>first framebuffer device, we have at least one terminal defined by default, which is referred to by the <code class="literal">/dev/tty0</code> device.</p></div></div></div></body></html>
