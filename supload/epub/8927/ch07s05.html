<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Summary</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Summary"><div class="titlepage"><div><div><h1 class="title"><a id="ch07lvl1sec44"/>Summary</h1></div></div></div><p>This time, we used several sensors to get important data about our lovely plant. Then, we also discovered a simple way to exchange this data between processes by using a normal file. We learned how to use the Facebook PHP API to do a post on a user's timeline by using a simple script.</p><p>In the next chapter, we'll try to implement an intrusion alarm system with motion detection sensors that, in case of alarm, will start taking pictures of the intruders and then send them to the user's e-mail address.</p></div></body></html>
