<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Setting up the hardware</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Setting up the hardware"><div class="titlepage"><div><div><h1 class="title"><a id="ch11lvl1sec61"/>Setting up the hardware</h1></div></div></div><p>The <a id="id502" class="indexterm"/>Z-Wave technology, which is oriented to the residential control and automation market, is designed to be suitable for battery-operated devices. In fact, one of its main goals is to minimize the power consumption. Despite this fact, it provides reliable and low-latency transmission of small data packets at data rates of up to 100 kbps, and a simple yet reliable method to wirelessly manage sensors and control lights and appliances in a house.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note110"/>Note</h3><p>For more information on <a id="id503" class="indexterm"/>Z-Wave, a good starting point is at <a class="ulink" href="https://en.wikipedia.org/wiki/Z-Wave">https://en.wikipedia.org/wiki/Z-Wave</a>.</p></div></div><p>In our project, we're going to use a Z-Wave controller on a USB dongle, one slave device powered by the same plug where it's connected to, and one multisensor device that can be powered by batteries or via an external USB connection.</p><div class="section" title="Setting up the Z-Wave controller"><div class="titlepage"><div><div><h2 class="title"><a id="ch11lvl2sec87"/>Setting up the Z-Wave controller</h2></div></div></div><p>The <a id="id504" class="indexterm"/>Z-Wave controller I <a id="id505" class="indexterm"/>used in this prototype is shown in the following image:</p><div class="mediaobject"><img src="graphics/B00255_11_01.jpg" alt="Setting up the Z-Wave controller"/></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note111"/>Note</h3><p>The device can be<a id="id506" class="indexterm"/> purchased at the following link (or by surfing the Internet): <a class="ulink" href="http://www.cosino.io/product/usb-z-wave-controller">http://www.cosino.io/product/usb-z-wave-controller</a>.</p><p>A reference design is available here:</p><p>
<a class="ulink" href="http://z-wave.sigmadesigns.com/docs/brochures/UZB_br.pdf">http://z-wave.sigmadesigns.com/docs/brochures/UZB_br.pdf</a>.</p></div></div><p>Once <a id="id507" class="indexterm"/>connected with the BeagleBone Black's USB host port by using the<a id="id508" class="indexterm"/> <code class="literal">lsusb</code> command, we should get the following output:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# lsusb</strong></span>
<span class="strong"><strong>Bus 001 Device 002: ID 0658:0200 Sigma Designs, Inc.</strong></span>
<span class="strong"><strong>Bus 001 Device 001: ID 1d6b:0002 Linux Foundation 2.0 root hub</strong></span>
<span class="strong"><strong>Bus 002 Device 001: ID 1d6b:0002 Linux Foundation 2.0 root hub</strong></span>
</pre></div><p>Also we should see the following kernel activity:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>hub 1-0:1.0: hub_resume</strong></span>
<span class="strong"><strong>hub 1-0:1.0: port 1: status 0101 change 0001</strong></span>
<span class="strong"><strong>hub 1-0:1.0: state 7 ports 1 chg 0002 evt 0000</strong></span>
<span class="strong"><strong>hub 1-0:1.0: port 1, status 0101, change 0000, 12 Mb/s</strong></span>
<span class="strong"><strong>usb 1-1: new full-speed USB device number 2 using musb-hdrc</strong></span>
<span class="strong"><strong>usb 1-1: ep0 maxpacket = 8</strong></span>
<span class="strong"><strong>usb 1-1: skipped 4 descriptors after interface</strong></span>
<span class="strong"><strong>usb 1-1: udev 2, busnum 1, minor = 1</strong></span>
<span class="strong"><strong>usb 1-1: New USB device found, idVendor=0658, idProduct=0200</strong></span>
<span class="strong"><strong>usb 1-1: New USB device strings: Mfr=0, Product=0, SerialNumber=0</strong></span>
<span class="strong"><strong>usb 1-1: usb_probe_device</strong></span>
<span class="strong"><strong>usb 1-1: configuration #1 chosen from 1 choice</strong></span>
<span class="strong"><strong>usb 1-1: adding 1-1:1.0 (config #1, interface 0)</strong></span>
<span class="strong"><strong>cdc_acm 1-1:1.0: usb_probe_interface</strong></span>
<span class="strong"><strong>cdc_acm 1-1:1.0: usb_probe_interface - got id</strong></span>
<span class="strong"><strong>cdc_acm 1-1:1.0: This device cannot do calls on its own. It is not a modem.</strong></span>
<span class="strong"><strong>cdc_acm 1-1:1.0: ttyACM0: USB ACM device</strong></span>
<span class="strong"><strong>usb 1-1: adding 1-1:1.1 (config #1, interface 1)</strong></span>
<span class="strong"><strong>hub 1-0:1.0: state 7 ports 1 chg 0000 evt 0002</strong></span>
<span class="strong"><strong>hub 1-0:1.0: port 1 enable change, status 00000103</strong></span>
</pre></div><p>Looking at the last but fourth line, we can discover that the Z-Wave controller has been connected to the <code class="literal">/dev/ttyACM0</code> device file. So, the device is correctly connected. But to really test it, we need to install a proper management software. To do so, we can use an open source implementation of the Z-Wave protocol named <a id="id509" class="indexterm"/>
<span class="strong"><strong>Open Z-Wave</strong></span>, where we can find a lot of suitable software to test a Z-Wave network.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note112"/>Note</h3><p>The home page of the <a id="id510" class="indexterm"/>Open Z-Wave project is at <a class="ulink" href="http://www.openzwave.com">http://www.openzwave.com</a>.</p></div></div><p>With the<a id="id511" class="indexterm"/> following command, we can download the code we<a id="id512" class="indexterm"/> need into our prototype:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# git clone https://github.com/OpenZWave/open-zwave openzwave</strong></span>
</pre></div><p>Then, we need some extra packages to compile the needed tools. So, let's install them with the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# aptitude install build-essential make git libudev-dev libjson0 libjson0-dev libcurl4-gnutls-dev</strong></span>
</pre></div><p>Now, just enter into the <code class="literal">openzwave</code> directory, and simply use the <code class="literal">make</code> command as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# cd openzwave</strong></span>
<span class="strong"><strong>root@beaglebone:~/openzwave# make</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip136"/>Tip</h3><p>The compilation is quite slow, so be patient.</p></div></div><p>When finished, we need to download another repository into the current directory with the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~/openzwave# git clone https://github.com/OpenZWave/open-zwave-control-panel openzwave-control-panel</strong></span>
</pre></div><p>Then, after the download, we have to install an extra package to proceed with the compilation. So, let's use the <code class="literal">aptitude</code> command again, as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~/openzwave# aptitude install libmicrohttpd-dev</strong></span>
</pre></div><p>Now, as the last step, enter into the <code class="literal">openzwave-control-panel</code> directory and rerun the <code class="literal">make</code> command with the following command lines:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~/openzwave# cd openzwave-control-panel/</strong></span>
<span class="strong"><strong>root@beaglebone:~/openzwave/openzwave-control-panel# make</strong></span>
</pre></div><p>When the compilation is finished, the <code class="literal">ozwcp</code> program should be available. So, let's execute it by using the following command lines:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~/openzwave/openzwave-control-panel# ln -s ../config</strong></span>
<span class="strong"><strong>root@beaglebone:~/openzwave/openzwave-control-panel# ./ozwcp -d -p 8080</strong></span>
<span class="strong"><strong>2014-04-23 21:12:52.943 Always, OpenZwave Version 1.3.526 Starting Up</strong></span>
<span class="strong"><strong>webserver starting port 8080</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip137"/>Tip</h3><p>Note that the <code class="literal">ln</code> command is just used once to create a proper link with the Open Z-Wave configuration directory <code class="literal">config</code>, which is located in the upper directory.</p><p>If you get the following error while executing the program, it means that most probably your web server is holding port <code class="literal">8080</code>, so you have to disable it:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>Failed to bind to port 8080: Address already in use</strong></span>
</pre></div></div></div><p>Now, we <a id="id513" class="indexterm"/>should point the web browser on our host PC to the <a id="id514" class="indexterm"/>address <code class="literal">http://***********:8080/</code> to get what is shown in the following screenshot:</p><div class="mediaobject"><img src="graphics/B00255_11_02.jpg" alt="Setting up the Z-Wave controller"/></div><p>Okay, now we have to enter the <code class="literal">/dev/ttyACM0</code> path name into the <span class="strong"><strong>Device name</strong></span> field, and then press the <span class="strong"><strong>Initialize</strong></span> button to start the communication. If everything works well, you should see that a new device is listed in the <span class="strong"><strong>Devices</strong></span> tab, as shown in the following screenshot:</p><div class="mediaobject"><img src="graphics/B00255_11_03.jpg" alt="Setting up the Z-Wave controller"/></div><p>Now, the <a id="id515" class="indexterm"/>controller is up and running, so, we can continue <a id="id516" class="indexterm"/>installing the Z-Wave slaves.</p></div><div class="section" title="Setting up the Z-Wave wall plug"><div class="titlepage"><div><div><h2 class="title"><a id="ch11lvl2sec88"/>Setting up the Z-Wave wall plug</h2></div></div></div><p>The first <a id="id517" class="indexterm"/>Z-Wave <a id="id518" class="indexterm"/>slave is the wall plug shown in the following image:</p><div class="mediaobject"><img src="graphics/B00255_11_04.jpg" alt="Setting up the Z-Wave wall plug"/></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note113"/>Note</h3><p>The device can be <a id="id519" class="indexterm"/>purchased at the following link (or by surfing the Internet): <a class="ulink" href="http://www.cosino.io/product/z-wave-wall-plug">http://www.cosino.io/product/z-wave-wall-plug</a>.</p><p>A reference manual is available here:</p><p>
<a class="ulink" href="http://www.fibaro.com/manuals/en/FGWPx-101/FGWPx-101-EN-A-v1.00.pdf">http://www.fibaro.com/manuals/en/FGWPx-101/FGWPx-101-EN-A-v1.00.pdf</a>.</p></div></div><p>The device is <a id="id520" class="indexterm"/>wireless<a id="id521" class="indexterm"/> and, once connected with a powered plug, it's self-powered; so, we don't need special connections to set it up. However, we need some home appliance connected to it, as shown in the following image, for the power consumption measurements:</p><div class="mediaobject"><img src="graphics/B00255_11_05.jpg" alt="Setting up the Z-Wave wall plug"/></div><p>Now, to test this<a id="id522" class="indexterm"/> device and its communication with the controller, we can use<a id="id523" class="indexterm"/> the <code class="literal">ozwcp</code> program again. Just click on the <span class="strong"><strong>Select an operation</strong></span> menu entry in the <span class="strong"><strong>Controller</strong></span> tab and select the <span class="strong"><strong>Add Device</strong></span> entry and then press the <span class="strong"><strong>Go</strong></span> button. On the left, you should see the <span class="strong"><strong>Add Device: waiting for a user action</strong></span> message. So, let's power up the device by putting it into a wall plug and then strike the button on the device in order to start the pairing procedure (just as a Bluetooth device does).</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip138"/>Tip</h3><p>Note that newer version of this device doesn't require you to press the button to start the pairing procedure&#x2014;it just starts automatically after the first plug.</p></div></div><p>If everything works well, a new device should appear in the <span class="strong"><strong>Devices</strong></span> tab as follows:</p><div class="mediaobject"><img src="graphics/B00255_11_06.jpg" alt="Setting up the Z-Wave wall plug"/></div><p>Now, we can<a id="id524" class="indexterm"/> change some of the device's settings by selecting the new device and <a id="id525" class="indexterm"/>then clicking on the <span class="strong"><strong>Configuration</strong></span> option under the <span class="strong"><strong>Devices</strong></span> listing tab. A panel setting similar to the following screenshot should appear:</p><div class="mediaobject"><img src="graphics/B00255_11_07.jpg" alt="Setting up the Z-Wave wall plug"/></div><p>Now, we can <a id="id526" class="indexterm"/>change the <span class="strong"><strong>Standard power load reporting</strong></span> entry by <a id="id527" class="indexterm"/>writing the new value in the related field and then pressing the <span class="strong"><strong>Submit</strong></span> button. In this manner, we can define a lower value by how much power load must change (in percentage) to be reported to the main controller (I used the value <code class="literal">5</code>).</p></div><div class="section" title="Setting up the Z-Wave multisensor"><div class="titlepage"><div><div><h2 class="title"><a id="ch11lvl2sec89"/>Setting up the Z-Wave multisensor</h2></div></div></div><p>The <a id="id528" class="indexterm"/>second<a id="id529" class="indexterm"/> Z-Wave slave is the multisensor shown in the following image:</p><div class="mediaobject"><img src="graphics/B00255_11_08.jpg" alt="Setting up the Z-Wave multisensor"/></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note114"/>Note</h3><p>The device can be<a id="id530" class="indexterm"/> purchased at the following link (or by surfing the Internet): <a class="ulink" href="http://www.cosino.io/product/z-wave-multi-sensor">http://www.cosino.io/product/z-wave-multi-sensor</a>.</p><p>A reference manual is available here:</p><p>
<a class="ulink" href="http://aeotec.com/z-wave-sensor/47-multisensor-manual.html">http://aeotec.com/z-wave-sensor/47-multisensor-manual.html</a>.</p></div></div><p>To power the <a id="id531" class="indexterm"/>device, we can use 4 batteries or a USB cable connected <a id="id532" class="indexterm"/>as in the following image. Then, to test the device and its communication with the controller, we can use the <code class="literal">ozwcp</code> program again. So, just click on the <span class="strong"><strong>Select an operation</strong></span> menu entry in the <span class="strong"><strong>Controller</strong></span> tab and select the <span class="strong"><strong>Add Device</strong></span> entry. Then, press the <span class="strong"><strong>Go</strong></span> button in order to repeat a pairing procedure again (the pairing button is the black button near the sensitivity regulator under the battery pack cover pack cover. In the image below it is located in the top-right corner).</p><div class="mediaobject"><img src="graphics/B00255_11_09.jpg" alt="Setting up the Z-Wave multisensor"/></div><p>Again, if<a id="id533" class="indexterm"/> everything<a id="id534" class="indexterm"/> works well, a new device should appear in the <span class="strong"><strong>Devices</strong></span> tab as follows:</p><div class="mediaobject"><img src="graphics/B00255_11_10.jpg" alt="Setting up the Z-Wave multisensor"/></div><p>Now, as before, we<a id="id535" class="indexterm"/> can change the default settings. In particular, we <a id="id536" class="indexterm"/>can set up the environment report's frequencies and the report's content by changing the <span class="strong"><strong>Group 1 Reports</strong></span> entry to <code class="literal">224</code> and the <span class="strong"><strong>Group 2 Reports</strong></span> entry to <code class="literal">1</code>, then <span class="strong"><strong>Group 1 Interval</strong></span> to <code class="literal">10</code> and <span class="strong"><strong>Group 2 Interval</strong></span> to <code class="literal">60</code>. </p><p>These special settings will instruct the multisensor to enable bits 7 (luminosity), 6 (humidity), and 5 (temperature) into group 1, and bit 0 (battery level) into group 2, and to repeat them every 10 seconds for group 1 and every 60 seconds for group 2 (see the following screenshot):</p><div class="mediaobject"><img src="graphics/B00255_11_11.jpg" alt="Setting up the Z-Wave multisensor"/></div><p>Ok, now all <a id="id537" class="indexterm"/>the devices are ready to operate! So, we can stop the <code class="literal">ozwcp</code> program <a id="id538" class="indexterm"/>by pressing the <span class="emphasis"><em>CTRL</em></span> + <span class="emphasis"><em>C</em></span> key sequence and go forward to the next sections.</p></div><div class="section" title="The final picture"><div class="titlepage"><div><div><h2 class="title"><a id="ch11lvl2sec90"/>The final picture</h2></div></div></div><p>The following is <a id="id539" class="indexterm"/>the image that shows the prototype I realized to implement this project and to test the software.</p><p>Nothing special to say here; just the BeagleBone Black with the Z-Wave controller USB dongle and the two Z-Wave devices described before.</p><div class="mediaobject"><img src="graphics/B00255_11_12.jpg" alt="The final picture"/></div></div></div></body></html>
