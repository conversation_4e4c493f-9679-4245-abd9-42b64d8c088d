<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Final test</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Final test"><div class="titlepage"><div><div><h1 class="title"><a id="ch07lvl1sec43"/>Final test</h1></div></div></div><p>To test the <a id="id358" class="indexterm"/>prototype, I first executed the <code class="literal">chapter_07/SYSINIT.sh</code> file in the book's example code repository to set up all peripherals:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# ./SYSINIT.sh</strong></span>
<span class="strong"><strong>done!</strong></span>
</pre></div><p>Now, after checking that the web server is running, I started the <code class="literal">plant_mon.sh</code> plant monitor script, enabling all debugging messages:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# root@beaglebone:~/chapter_07# ./plant_mon.sh -d -l -f</strong></span>
<span class="strong"><strong>plant_mon.sh: min=10</strong></span>
<span class="strong"><strong>plant_mon.sh: signals traps installed</strong></span>
<span class="strong"><strong>plant_mon.sh: lig_levl=442 int_temp=29.50 ext_temp=29.91 msr_mois=0</strong></span>
<span class="strong"><strong>plant_mon.sh: curr_date=0010 next_date=</strong></span>
<span class="strong"><strong>plant_mon.sh: start main loop</strong></span>
<span class="strong"><strong>plant_mon.sh: lig_levl=428 int_temp=29.50 ext_temp=29.25 msr_mois=0</strong></span>
<span class="strong"><strong>plant_mon.sh: curr_date=0010 next_date=0011</strong></span>
<span class="strong"><strong>plant_mon.sh: cff_mois=50</strong></span>
<span class="strong"><strong>plant_mon.sh: est_mois=221</strong></span>
<span class="strong"><strong>plant_mon.sh: lig_levl=423 int_temp=29.50 ext_temp=27.99 msr_mois=0</strong></span>
<span class="strong"><strong>plant_mon.sh: curr_date=0010 next_date=0011</strong></span>
<span class="strong"><strong>plant_mon.sh: cff_mois=50</strong></span>
<span class="strong"><strong>plant_mon.sh: est_mois=220</strong></span>
<span class="strong"><strong>...</strong></span>
</pre></div><p>Then, I set up the <a id="id359" class="indexterm"/>web server's root directory in order to implement the web interface. On my BeagleBone Black, the web server's root directory is <code class="literal">/var/www/</code>, but it may vary according to your system settings.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note81"/>Note</h3><p>The reader can take a look at the book <span class="emphasis"><em>BeagleBone Essentials</em></span>, <span class="emphasis"><em>Packt Publishing</em></span>, written by the author of this book, in order to get more information regarding how to set up a web server on the BeagleBone Black.</p></div></div><p>If you have the same configuration as mine, and the <code class="literal">plant_mon.sh</code> script is running, then your <code class="literal">/var/www/</code> directory should look like the following:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# ls /var/www/</strong></span>
<span class="strong"><strong>plant.lock  plant.status  webcam-shot.jpg</strong></span>
</pre></div><p>These files are created by the monitoring script, and they're, respectively, the lock file, the system's status file, and the last picture taken. Along with these files you also need the Facebook API (so we have to unzip its source code here, as shown previously) and the configuration files <code class="literal">config.php</code> and <code class="literal">setup.php</code>.</p><p>Then, we have to add the <code class="literal">plant.html</code>, <code class="literal">plant.css</code>, and <code class="literal">handler.php</code> files for the the web interface with the <code class="literal">jquery-1.9.1.js</code> file that can be downloaded from <a class="ulink" href="https://code.jquery.com/jquery/">https://code.jquery.com/jquery/</a> by using the browser on the host PC or directly on your BeagleBone Black with the following command line:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:# wget --no-check-certificate https://code.jquery.com/jquery-1.9.1.js</strong></span>
</pre></div><p>Then, we must <a id="id360" class="indexterm"/>make sure that all files are owned by the system user <code class="literal">www-user</code> in order to allow the web server to read/write them without problems. To do so, we can use the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:# cd /var/www &amp;&amp; chown -R www-data:www-data *</strong></span>
</pre></div><p>If everything works well, your web server's root directory should look like the following:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:/var/www# ls -l</strong></span>
<span class="strong"><strong>total 308</strong></span>
<span class="strong"><strong>-rw-r--r-- 1 <USER> <GROUP>    344 Aug 19  2015 config.php</strong></span>
<span class="strong"><strong>drwxr-xr-x 5 <USER> <GROUP>   4096 Aug 18  2015 facebook-php-sdk-v4-5.0-dev</strong></span>
<span class="strong"><strong>-rw-r--r-- 1 <USER> <GROUP>   1846 Aug 26  2015 handler.php</strong></span>
<span class="strong"><strong>-rw-r--r-- 1 <USER> <GROUP> 268381 Oct 24  2014 jquery-1.9.1.js</strong></span>
<span class="strong"><strong>-rw-r--r-- 1 <USER> <GROUP>   2968 Aug 26  2015 plant.html</strong></span>
<span class="strong"><strong>-rw-rw-rw- 1 <USER> <GROUP>      0 Apr 26 01:17 plant.lock</strong></span>
<span class="strong"><strong>-rw-rw-rw- 1 <USER> <GROUP>     95 Apr 26 01:17 plant.status</strong></span>
<span class="strong"><strong>-rw-r--r-- 1 <USER> <GROUP>    183 Aug 24  2015 setup.php</strong></span>
<span class="strong"><strong>-rw-r--r-- 1 <USER> <GROUP>  17583 Apr 26 01:17 webcam-shot.jpg</strong></span>
</pre></div><p>Now, everything should be in place, so, on my host PC, I pointed my browser to the BeagleBone Black's IP address on the emulated Ethernet line via a USB cable to display the web interface. A screenshot of my test is as follows:</p><div class="mediaobject"><img src="graphics/B00255_07_16.jpg" alt="Final test"/></div><p>Notice that a similar <a id="id361" class="indexterm"/>result can be obtained by using a smartphone or tablet. In fact, if I connect my BeagleBone Black to my LAN and then point my smartphone's browser to the BeagleBone Black IP address, I get what is shown in the following screenshot:</p><div class="mediaobject"><img src="graphics/B00255_07_17.jpg" alt="Final test"/></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip99"/>Tip</h3><p>Remember that the IP address of the USB emulated Ethernet is usually <code class="literal">***********</code>, while the IP address that the BeagleBone Black gets when it is connected to your LAN may vary according to your LAN settings. It can be retrieved by using the <code class="literal">ifconfig eth0</code> command on a BeagleBone Black's terminal.</p></div></div></div></body></html>
