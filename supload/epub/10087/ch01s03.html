<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Creating your Pass (Medium)</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Creating your Pass (Medium)"><div class="titlepage"><div><div><h1 class="title"><a id="ch01lvl1sec10"/>Creating your Pass (Medium)</h1></div></div></div><p>Passes are built and customized by specifying the relevant information in JSON format. Certain graphical assets can also be provided to further customize the look and feel of the Pass.</p><p>As an example, we will build a Pass of a Generic type, to be used as an employee identification card. Once we understand the JSON structure of this type, we will see how other Pass types differ.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch01lvl2sec14"/>Getting ready</h2></div></div></div><p>You can get the full JSON code, which we will use, from the following link:</p><p>
<a class="ulink" href="http://passkit.pro/example-generic-json">http://passkit.pro/example-generic-json</a>
</p><p>This will create a Pass that looks like the following two images.</p><p>The following screenshot shows the front of the Pass:</p><div class="mediaobject"><img src="graphics/7064OT_01_14.jpg" alt="Getting ready"/></div><p>The back of the Pass is shown in the following screenshot:</p><div class="mediaobject"><img src="graphics/7064OT_01_15.jpg" alt="Getting ready"/></div></div><div class="section" title="How to do it&#x2026;"><div class="titlepage"><div><div><h2 class="title"><a id="ch01lvl2sec15"/>How to do it&#x2026;</h2></div></div></div><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">Save the following JSON code to a file called <code class="literal">pass.json</code>:<div class="informalexample"><pre class="programlisting">{
"passTypeIdentifier" : "pass.pro.passkit.example.generic",
  "formatVersion" : 1,
  "teamIdentifier" : "YAXJZJ267E",
  "organizationName" : "Passbook Example Company",
  "serialNumber" : "0000001",
  "description" : "Staff Pass for Employee Number 001",
  "associatedStoreIdentifiers" : [
    *********
  ],
  "locations" : [
    {
  "latitude" : 51.50506, 
  "longitude" : -0.01960, 
  "relevantText" : "Company Offices"
}
  ],
  "foregroundColor" : "rgb(255, 255, 255)",
  "backgroundColor" : "rgb(90, 90, 90)",
  "labelColor" : "rgb(255, 255, 255)",
  "logoText" : "Company Staff ID",
  "barcode" : {
    "format" : "PKBarcodeFormatQR",
    "message" : "0000001",
    "messageEncoding" : "iso-8859-1",
    "altText" : "Staff ID 0000001"
   },
  "generic" : {
    "headerFields" : [
      {
        "key" : "staffNumber",
  "label" : "Staff Number",
  "value" : "001"
      }
    ],
  "primaryFields" : [
    {
  "key" : "staffName",
  "label" : "Name",
  "value" : "Peter Brooke"
  }
  ],
  "secondaryFields" : [
  {
  "key" : "telephoneExt",
  "label" : "Extension",
  "value" : "9779"
  },
  {
  "key" : "jobTitle",
  "label" : "Job Title",
  "value" : "Chief Pass Creator"
  }
  ],
  "backFields" : [
  {
  "key" : "managersName",
  "label" : "Manager's Name",
  "value" : "Paul Bailey"
  },
  {
  "key" : "managersExt",
  "label" : "Manager's Extension",
  "value" : "9673"
  },
  {
  "key" : "expiryDate",
  "dateStyle" : "PKDateStyleShort",
  "label" : "Expiry Date",
  "value" : "2013-12-31T00:00-23:59"
  }
  ]
  }
}</pre></div></li><li class="listitem">Replace the value for <code class="literal">passTypeIdentifier</code> (currently <code class="literal">pass.pro.passkit.example.generic</code>) with your own Pass Type Identifier created previously.</li><li class="listitem">Replace the value for <code class="literal">teamIdentifier</code> (currently <code class="literal">YAXJZJ267E</code>) with your own Team Identifier, noted previously as the App ID Prefix.</li><li class="listitem">A number of graphical assets can be added to further customise the Pass. The only required graphical asset is an icon, which is used when displaying the Pass on the lock screen. </li><li class="listitem">Create an icon for retina screens with dimensions of 58 px by 58 px, this should be named <code class="literal"><EMAIL></code>. Create a non-retina version with dimensions 29 px by 29 px, this should be named <code class="literal">icon.png</code>. Example assets can downloaded from <a class="ulink" href="http://passkit.pro/example-generic-package">http://passkit.pro/example-generic-package</a>.</li></ol></div></div><div class="section" title="How it works&#x2026;"><div class="titlepage"><div><div><h2 class="title"><a id="ch01lvl2sec16"/>How it works&#x2026;</h2></div></div></div><p>The <code class="literal">pass.json</code> file we've just created contains the following top-level key/value pairs:</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc"><code class="literal">passTypeIdentifier</code>: This ties the Pass to your developer account.</li><li class="listitem" style="list-style-type: disc"><code class="literal">formatVersion</code>: Currently this is always 1, but it will allow Apple to vary the format in the future, while maintaining backwards compatibility.</li><li class="listitem" style="list-style-type: disc"><code class="literal">teamIdentifier</code>: This identifier allows separate apps, by the same developer, to share data through the iOS keychain. For our purposes, this just needs to match the App ID Prefix specified when the Pass Type Identifier was created.</li><li class="listitem" style="list-style-type: disc"><code class="literal">organizationName</code>: This is the name of your company or app, this should be how your users know and refer to you. It will be displayed as the title of the notification when a Pass is presented on the lock screen.</li><li class="listitem" style="list-style-type: disc"><code class="literal">serialNumber</code>: This should contain a unique reference to the Pass, when updating a Pass, this serial number will be used to request the updated information from your server, and therefore this should uniquely identify only one Pass of this type. In the example of a staff identification Pass, this could be the employee reference number. While it's represented as a number in the example, it can be any text value.</li><li class="listitem" style="list-style-type: disc"><code class="literal">description</code>: Used for accessibility features and so should briefly describe the Pass's use and should include enough information to distinguish it from other Passes of the same type.</li><li class="listitem" style="list-style-type: disc"><code class="literal">associatedStoreIdentifiers</code>: An array of iTunes Store App Identifiers for Apps that are associated with this Pass. If you already have an app in the App Store, through which Passes will be provided, this should be included. An app's identifier can be found in iTunes Connect. If multiple identifiers are provided, the first one that is available for the device is used. This will present an App information banner on the back of the Pass. This banner will prompt the user to install your app or open the app if it is already installed.</li></ul></div><p>The following two top-level key/value pairs determine under what circumstances the Pass is presented to the user on their lock screen.</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc"><code class="literal">locations</code>: This is a list of locations that are relevant to the use of the Pass. This is represented as an array of dictionaries containing the location information. Only one location is provided in the example, however up to 10 may be specified. If the user is within a certain distance of this location, they may be presented with the Pass on their lock screen. The distance from the location that triggers this behavior varies depending on the Pass type, for a generic Pass type the distance is approximately 100 meters. Each <code class="literal">locations</code> dictionary contains latitude, longitude, and relevant text that will be displayed on the lock screen when triggered. In the <code class="literal">locations</code> dictionary you can optionally specify altitude as well.</li><li class="listitem" style="list-style-type: disc"><code class="literal">relevantDate</code>: Not included in the example above, as it didn't fit with the employer ID use case. This determines a time period within which the Pass is relevant. The value for this key should be text in the form of a W3C timestamp, for example, "2013-12-31T00:00-23:59", and can also include seconds if necessary. No timezone information should be included, as the timezone set in the device's settings is used.</li></ul></div><p>The behavior of the two relevancy keys described above varies depending on the Pass type. The Pass type determines that for <code class="literal">locations</code> and <code class="literal">relevantDate</code>, which are required and which are options, it also determines the triggering criteria used in presenting a Pass on the user's lock screen.</p><p>An explanation of the differing behavior can be found in Apple's Passbook Programming Guide (Apple Developer account signin required) at:</p><p>
<a class="ulink" href="https://developer.apple.com/library/ios/#documentation/UserExperience/Conceptual/PassKit_PG/Chapters/Creating.html">https://developer.apple.com/library/ios/#documentation/UserExperience/Conceptual/PassKit_PG/Chapters/Creating.html#//apple_ref/doc/uid/**********-CH4-SW53</a>
</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc"><code class="literal">foregroundColor</code>: The foreground color of the Pass, value provided as an RGB triple.</li><li class="listitem" style="list-style-type: disc"><code class="literal">backgroundColor</code>: The background color of the Pass, value provided as an RGB triple.</li><li class="listitem" style="list-style-type: disc"><code class="literal">labelColor</code>: The color of the text displayed in the Pass, value provided as an RGB triple.</li><li class="listitem" style="list-style-type: disc"><code class="literal">logoText</code>: The text shown in the top-left hand corner of the Pass. This will mostly likely be the name of your company, or an indication of the Pass's use.</li><li class="listitem" style="list-style-type: disc"><code class="literal">barcode</code>: This dictionary contains the information to display in a barcode and how to display it. The Pass format supports automatic creation of 2D barcodes in one of the following formats:<div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc"><code class="literal">PKBarcodeFormatQR</code></li><li class="listitem" style="list-style-type: disc"><code class="literal">PKBarcodeFormatPDF417</code></li><li class="listitem" style="list-style-type: disc"><code class="literal">PKBarcodeFormatAztec</code></li></ul></div><p>Message encoding will typically be <code class="literal">iso-8859-1</code>, unless you know that another encoding format is supported by your barcode scanner.</p><p>The value to be encoded into the barcode should be defined with the message key and the <code class="literal">altText</code> key used to optionally display a more human readable description of the barcode information.</p></li><li class="listitem" style="list-style-type: disc"><span class="strong"><strong>generic</strong></span>: This top-level key determines the type of Pass that you will be creating. The types currently available are:<div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc"><code class="literal">generic</code></li><li class="listitem" style="list-style-type: disc"><code class="literal">boardingPass</code></li><li class="listitem" style="list-style-type: disc"><code class="literal">coupon</code></li><li class="listitem" style="list-style-type: disc"><code class="literal">eventTicket</code></li><li class="listitem" style="list-style-type: disc"><code class="literal">storeCard</code></li></ul></div></li></ul></div><p>Your choice of Pass type effects many things about the Pass, including overall style, text layout, available graphical asset options, and lock screen behavior. Apple has optimized the Pass types for their individual use cases, so try to pick a Pass type that most closely represents your Pass's use case. If none of the specific Pass types are appropriate, then the generic Pass type can be used.</p><p>The Passbook Programming Guide describes the different layout configurations for each Pass type:</p><p>
<a class="ulink" href="https://developer.apple.com/library/ios/#documentation/UserExperience/Conceptual/PassKit_PG/Chapters/Creating.html">https://developer.apple.com/library/ios/#documentation/UserExperience/Conceptual/PassKit_PG/Chapters/Creating.html#//apple_ref/doc/uid/**********-CH4-SW1</a>
</p><p>Within the Pass type top-level key is a dictionary, containing arrays of various display groups, including <code class="literal">headerFields</code>, <code class="literal">primaryFields</code>, <code class="literal">secondaryFields</code>, and <code class="literal">backFields</code>, which are displayed according to Pass type. Each group contains an array of dictionaries, specifying key, label, and value. The label and value fields are displayed on the Pass, while the key field should be unique within your Pass format, and will be used when updating a Pass.</p></div><div class="section" title="There's more&#x2026;"><div class="titlepage"><div><div><h2 class="title"><a id="ch01lvl2sec17"/>There's more&#x2026;</h2></div></div></div><p>Graphical assets can be provided to further visually customize your Pass. The <code class="literal">icon.png</code> asset is required, but the following assets can optionally be included in the Pass package:</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc"><code class="literal">logo.png</code></li><li class="listitem" style="list-style-type: disc"><code class="literal">background.png</code></li><li class="listitem" style="list-style-type: disc"><code class="literal">thumbnail.png</code></li><li class="listitem" style="list-style-type: disc"><code class="literal">footer.png</code></li><li class="listitem" style="list-style-type: disc"><code class="literal">strip.png</code></li></ul></div><p>All assets should include a retina version that is twice as wide and twice as high, with <code class="literal">@2x</code> at the end of the filename. Therefore, for the icon, you provide the following files:</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc"><code class="literal">icon.png</code>: 29 px width x 29 px height</li><li class="listitem" style="list-style-type: disc"><code class="literal"><EMAIL></code>: 58 px width x 58 px height</li></ul></div><p>The following table provides the available size of each asset, and the space available for each Pass type. The sizes are provided as pixels width x pixels height:</p><div class="informaltable"><table border="1"><colgroup><col style="text-align: left"/><col style="text-align: left"/><col style="text-align: left"/><col style="text-align: left"/><col style="text-align: left"/><col style="text-align: left"/><col style="text-align: left"/></colgroup><thead><tr><th style="text-align: left" valign="bottom">
<p>Pass Type</p>
</th><th style="text-align: left" valign="bottom">
<p>icon</p>
<p>29 x 29</p>
</th><th style="text-align: left" valign="bottom">
<p>logo</p>
<p>160 x 50</p>
</th><th style="text-align: left" valign="bottom">
<p>background</p>
<p>180 x 220</p>
</th><th style="text-align: left" valign="bottom">
<p>thumbnail</p>
<p>90 x 90*</p>
</th><th style="text-align: left" valign="bottom">
<p>strip</p>
<p>**</p>
</th><th style="text-align: left" valign="bottom">
<p>footer</p>
<p>286x15</p>
</th></tr></thead><tbody><tr><td style="text-align: left" valign="top">
<p>Generic</p>
</td><td style="text-align: left" valign="top">
<p>Required</p>
</td><td style="text-align: left" valign="top">
<p>Optional</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td style="text-align: left" valign="top">
<p>Optional</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>Boarding pass</p>
</td><td style="text-align: left" valign="top">
<p>Required</p>
</td><td style="text-align: left" valign="top">
<p>Optional</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td style="text-align: left" valign="top">
<p>Optional</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>Coupon</p>
</td><td style="text-align: left" valign="top">
<p>Required</p>
</td><td style="text-align: left" valign="top">
<p>Optional</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td style="text-align: left" valign="top">
<p>Optional</p>
</td><td style="text-align: left" valign="top">
<p>No</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>Event ticket</p>
</td><td rowspan="2" style="text-align: left" valign="middle">
<p>Required</p>
</td><td rowspan="2" style="text-align: left" valign="middle">
<p>Optional</p>
</td><td style="text-align: left" valign="top">
<p>Optional</p>
</td><td style="text-align: left" valign="top">
<p>Optional</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td rowspan="2" style="text-align: left" valign="middle">
<p>Not used</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>(with strip)</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td style="text-align: left" valign="top">
<p>Optional</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>Store card</p>
</td><td style="text-align: left" valign="middle">
<p>Required</p>
</td><td style="text-align: left" valign="top">
<p>Optional</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td><td style="text-align: left" valign="top">
<p>Optional</p>
</td><td style="text-align: left" valign="top">
<p>Not used</p>
</td></tr></tbody></table></div><p>* 90px x 90px is the space available, but the graphic must be either 60px x 90px, or 90px x 60px</p><p>** Allowed size for the strip asset is 312 x 84 for Event tickets, 312 x 110 when a square barcode is used and 312 x 123 otherwise.</p><div class="section" title="Further documentation"><div class="titlepage"><div><div><h3 class="title"><a id="ch01lvl3sec03"/>Further documentation</h3></div></div></div><p>Further details of Pass type layout structures can be found in the Apple Passbook Package Format Reference (Apple Developer account signin required) at:</p><p>
<a class="ulink" href="https://developer.apple.com/library/ios/#documentation/UserExperience/Reference/PassKit_Bundle/Chapters/Introduction.html">https://developer.apple.com/library/ios/#documentation/UserExperience/Reference/PassKit_Bundle/Chapters/Introduction.html#//apple_ref/doc/uid/**********</a>
</p></div></div></div></body></html>
