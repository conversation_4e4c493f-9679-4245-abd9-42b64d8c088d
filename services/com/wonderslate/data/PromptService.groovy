package com.wonderslate.data

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.shop.BookPriceService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.transaction.Transactional
import com.wonderslate.log.GptLog
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import org.apache.commons.io.FileUtils

@Transactional
class PromptService {
    DataProviderService dataProviderService
    ResourceCreatorService resourceCreatorService
    UserManagementService userManagementService
    def redisService
    def springSecurityService
    def utilService
    PromptService promptService
    GptLogService gptLogService
    BookPriceService bookPriceService

    def createMCQResource(request,session, questions,requestBody) {
        String returnResponse = "Added"
        GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(new Integer("" + requestBody.resId), requestBody.promptType)
        def resourceDtlInstance
       ResourceDtl readingMaterialResourceDtl = dataProviderService.getResourceDtl(new Integer("" + requestBody.resId))
        /**  if(readingMaterialResourceDtl.mcqTypes==null||"".equals(readingMaterialResourceDtl.mcqTypes)) readingMaterialResourceDtl.mcqTypes=request.getParameter("promptType")
        else readingMaterialResourceDtl.mcqTypes=readingMaterialResourceDtl.mcqTypes+","+request.getParameter("promptType")
        readingMaterialResourceDtl.save(failOnError: true, flush: true)*/
        if (gptDefaultCreateLog == null){
            QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
            quizIdGenerator.save()
            resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.resLink = quizIdGenerator.id
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = "Multiple Choice Questions"
            resourceDtlInstance.chapterId = readingMaterialResourceDtl.chapterId
            resourceDtlInstance.resourceName = "GPT MCQ"
            resourceDtlInstance.siteId = utilService.getSiteId(request, session)
            resourceDtlInstance.gptResourceType = requestBody.promptType
            resourceDtlInstance.save(failOnError: true, flush: true)
        }else{
            resourceDtlInstance = dataProviderService.getResourceDtl(gptDefaultCreateLog.resId)
        }

       String answer=null
        if(questions.size()==0){
            returnResponse = "Did not add. \n\n\n"+questions
        }
       questions.each { question ->
            answer=null
           // the parameter answer can be in question.answer or question.answer(op), first we have to identify that and then set the answer.First step is if question.answer is present or question.answer(op) is present
             if(question.answer!=null){
                    answer = question.answer
                }else if(question.answer(op)!=null){
                    answer = question.answer(op)
                }
           boolean answerFound = false
           if(answer!=null){
                if(answer.equals("op1")){
                    answer = "op1"
                    answerFound = true
                }else if(answer.equals("op2")){
                    answer = "op2"
                    answerFound = true
                }else if(answer.equals("op3")){
                    answer = "op3"
                    answerFound = true
                }else if(answer.equals("op4")){
                    answer = "op4"
                    answerFound = true
                }
               if(!answerFound){
                   if(question.op1.equals(answer)){
                       answer = "op1"
                       answerFound = true
                     }else if(question.op2.equals(answer)){
                       answer = "op2"
                       answerFound = true
                        }else if(question.op3.equals(answer)){
                       answer = "op3"
                       answerFound = true
                            }else if(question.op4.equals(answer)) {
                       answer = "op4"
                       answerFound = true
                   }
               }
               }
             if(answerFound) {
                 ObjectiveMst om = new ObjectiveMst(quizId: new Integer(resourceDtlInstance.resLink), quizType: "Multiple Choice Questions", question: question.question,
                        option1: question.op1,
                        option2: question.op2,
                        option3: question.op3,
                        option4: question.op4,
                        answer1: answer.equals("op1") ? "Yes" : null,
                        answer2: answer.equals("op2") ? "Yes" : null,
                        answer3: answer.equals("op3") ? "Yes" : null,
                        answer4: answer.equals("op4") ? "Yes" : null,
                        answerDescription: question.explanation,
                        difficultylevel: question.difficulty,
                        questionType: requestBody.promptLabel,
                        marks: new Double("1"),
                        qType: question.questionType)
                om.save(failOnError: true, flush: true)
            }
        }

        if(gptDefaultCreateLog==null){
             gptDefaultCreateLog = new GptDefaultCreateLog(resId: resourceDtlInstance.id,promptType: requestBody.promptType,prompt: requestBody.prompt,response: "MCQ",
                    readingMaterialResId:new Integer(""+requestBody.resId),username: springSecurityService.currentUser.username,promptLabel:requestBody.promptLabel )
            gptDefaultCreateLog.save(failOnError: true, flush: true)
        }
         redisService.("quiz_"+resourceDtlInstance.id)= null

        return returnResponse
    }

    def createReadingResource(request,session,params,requestBody){
        String returnResponse = "Added"
        def resId
        ResourceDtl readingMaterialResourceDtl = dataProviderService.getResourceDtl(new Integer(""+requestBody.resId))
        params.put("chapterId",""+readingMaterialResourceDtl.chapterId)
        params.put("resourceType","Notes")
        params.put("notes",requestBody.answer)
        params.put("resourceName",requestBody.promptType)
        params.put("page","notes")
        params.put("gptResourceType",requestBody.promptType)
        params.put("subType","lesson")
        params.put("promptLabel",requestBody.promptLabel)
        String response, response2

        String inputResponse = requestBody.answer
        if(inputResponse.length()>60000){
            response = inputResponse.substring(0,60000)
            if(inputResponse.length()>120000)
                response2 = inputResponse.substring(60001,120000)
            else response2 = inputResponse.substring(60001)
        }else response = inputResponse
        GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(new Integer(""+requestBody.resId),requestBody.promptType)
        if(gptDefaultCreateLog==null){
            params.put("mode","create")
             resId =resourceCreatorService.addHTML(params,session,utilService.getSiteId(request,session))
            gptDefaultCreateLog = new GptDefaultCreateLog(resId: resId,promptType: requestBody.promptType,prompt: requestBody.prompt,response: response,response2: response2,
                    readingMaterialResId:new Integer(""+requestBody.resId),username: springSecurityService.currentUser.username,promptLabel:requestBody.promptLabel)

        }else{
            params.put("mode","edit")
            params.put("resourceDtlId",gptDefaultCreateLog.resId)
             resId =resourceCreatorService.addHTML(params,session,utilService.getSiteId(request,session))
            if(inputResponse.length()>60000){
                gptDefaultCreateLog.response = inputResponse.substring(0,60000)
                if(inputResponse.length()>120000)
                    gptDefaultCreateLog.response2 = inputResponse.substring(60001,120000)
                else gptDefaultCreateLog.response2 = inputResponse.substring(60001)
            }else gptDefaultCreateLog.response = inputResponse
            gptDefaultCreateLog.prompt = requestBody.prompt
        }
        gptDefaultCreateLog.save(failOnError: true, flush: true)

        return returnResponse
    }

    def createFlashCard(request,requestBody,flashcards){
        String returnResponse = "Added"
        def resId
        ResourceDtl readingMaterialResourceDtl = dataProviderService.getResourceDtl(new Integer(""+requestBody.resId))



        def keyValueId=-1
        ResourceDtl resourceDtlInstance
           //create the resource dtl first
            resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = "KeyValues"
            resourceDtlInstance.chapterId = new Integer(""+readingMaterialResourceDtl.chapterId)
            resourceDtlInstance.resourceName = "GPT Flash Card"
            resourceDtlInstance.resLink="blank"
            resourceDtlInstance.gptResourceType = requestBody.promptType
            resourceDtlInstance.save(flush: true, failOnError: true)
            resId = resourceDtlInstance.id

        flashcards.each { flashcard ->
            KeyValues keyValues = new KeyValues(term: flashcard.front, definition:flashcard.back ,resId: resId,status: "active")
            keyValues.save(flush: true, failOnError: true)
            resourceDtlInstance.resLink=""+keyValues.id
        }
        resourceDtlInstance.save(flush: true, failOnError: true)
        GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(new Integer(""+requestBody.resId),requestBody.promptType)
        if(gptDefaultCreateLog==null){
            gptDefaultCreateLog = new GptDefaultCreateLog(resId: resourceDtlInstance.id,promptType: requestBody.promptType,prompt: requestBody.prompt,response: "FlashCard",
                    readingMaterialResId:new Integer(""+requestBody.resId),username: springSecurityService.currentUser.username,promptLabel:requestBody.promptLabel )
        }else{
            gptDefaultCreateLog.resId= resourceDtlInstance.id
            gptDefaultCreateLog.prompt = requestBody.prompt
        }
        gptDefaultCreateLog.save(failOnError: true, flush: true)
        return returnResponse
    }

    def createQAResource(request,session,questions,requestBody){
        String returnResponse = "Added"
        GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(new Integer(""+requestBody.resId),requestBody.promptType)
        ResourceDtl readingMaterialResourceDtl = dataProviderService.getResourceDtl(new Integer(""+requestBody.resId))
        /**if(readingMaterialResourceDtl.qaTypes==null||"".equals(readingMaterialResourceDtl.qaTypes)) readingMaterialResourceDtl.qaTypes=request.getParameter("promptType")
        else readingMaterialResourceDtl.qaTypes=readingMaterialResourceDtl.qaTypes+","+request.getParameter("promptType")
        readingMaterialResourceDtl.save(failOnError: true, flush: true)*/
        def resourceDtlInstance
        if(gptDefaultCreateLog==null) {
            QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
            quizIdGenerator.save()

            resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.resLink = quizIdGenerator.id
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = "QA"
            resourceDtlInstance.chapterId = readingMaterialResourceDtl.chapterId
            if ("qna".equals(requestBody.promptType))
                resourceDtlInstance.resourceName = "GPT Question and Answers"
            else
                resourceDtlInstance.resourceName = "GPT Problems and Solutions"

            resourceDtlInstance.siteId = utilService.getSiteId(request, session)
            resourceDtlInstance.gptResourceType = requestBody.promptType
            resourceDtlInstance.save(failOnError: true, flush: true)
        }else{
            resourceDtlInstance = dataProviderService.getResourceDtl(gptDefaultCreateLog.resId)
        }

        if(questions.size()==0){
            returnResponse = "Did not add. \n\n\n"+questions
        }
        questions.each { question ->
            ObjectiveMst om = new ObjectiveMst(quizId: new Integer(""+resourceDtlInstance.resLink), quizType: "QA", question: question.question,
                    answer: question.answer,
                    difficultylevel: question.difficulty,
            questionType: requestBody.promptLabel,
            marks: question.marks!=null?new Double(question.marks):null, qType: question.questionType)
            om.save(failOnError: true, flush: true)
        }


        if(gptDefaultCreateLog==null){
            gptDefaultCreateLog = new GptDefaultCreateLog(resId: resourceDtlInstance.id,promptType: requestBody.promptType,prompt: requestBody.prompt,response: "MCQ",
                    readingMaterialResId:new Integer(""+requestBody.resId),username: springSecurityService.currentUser.username,promptLabel:requestBody.promptLabel )
            gptDefaultCreateLog.save(failOnError: true, flush: true)
        }
        redisService.("quiz_"+resourceDtlInstance.id)= null
        return returnResponse
    }

    def getQuestionAndAnswers(params){
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(params.resId))
        List answers = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink))
        if(params.noOfQuestions) {
            Collections.shuffle(answers)
            int noOfQuestions = params.noOfQuestions.toInteger()
            answers = answers.take(noOfQuestions)
        }

        List jsonAnswers = answers.collect { quiz ->
            return [id: quiz.id, question: quiz.question, answer: quiz.answer, difficultyLevel: quiz.difficultylevel]
        }

        return jsonAnswers
    }

    //write a function to create qna and mcq test. Input will b readingMaterialResId,number of questions for each type and difficulty level. From readingmaterial res id get the redIds from defaultGptCreateLog and then get the questions from the respective tables. Then add two lists to one hashmap and return it.
    def createTest(params){

        def difficultyMapping = getDifficultyMapping()

        def expandedDifficultyLevels = difficultyMapping.collectEntries { key, values ->
            [(key.toLowerCase()): [key.toLowerCase()] + values.collect { it.toLowerCase() }]
        }

        List defaultGptCreateLogs = GptDefaultCreateLog.findAllByReadingMaterialResIdAndPromptTypeInList(
                new Integer(params.readingMaterialResId),
                ["mcq", "qna"]
        )
        HashMap testMap = new HashMap()

        defaultGptCreateLogs.each { logEntry ->
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer("" + logEntry.resId))
            def answers = []

            if (resourceDtl.resType in ["Multiple Choice Questions", "QA"]) {
                def isMcq = resourceDtl.resType == "Multiple Choice Questions"
                def hasRelevantParams = params.keySet().any { paramName ->
                    (isMcq && paramName.endsWith('_mcq')) || (!isMcq && paramName.endsWith('_qna'))
                }

                if (hasRelevantParams) {
                    params.each { paramName, paramValue ->
                        if ((isMcq && paramName.endsWith('_mcq')) || (!isMcq && paramName.endsWith('_qna'))) {
                            def difficultyLevel = paramName.replace(isMcq ? '_mcq' : '_qna', '')
                            def targetCount = paramValue.toInteger()
                            def difficultyValues = expandedDifficultyLevels[difficultyLevel] ?: [difficultyLevel]

                            if (difficultyValues) {
                                def availableQuestions = ObjectiveMst.createCriteria().list {
                                    eq("quizId", new Integer(resourceDtl.resLink))
                                    'in'("difficultylevel", difficultyValues)
                                }

                                Collections.shuffle(availableQuestions)
                                answers.addAll(availableQuestions.take(targetCount))
                            }
                        }
                    }
                } else {
                    def availableQuestions = ObjectiveMst.createCriteria().list {
                        eq("quizId", new Integer(resourceDtl.resLink))
                    }

                    Collections.shuffle(availableQuestions)
                    answers.addAll(availableQuestions)
                }

                Collections.shuffle(answers)

                def jsonAnswers = answers.collect { question ->
                    if (isMcq) {
                        return [
                                id: question.id,
                                question: question.question,
                                option1: question.option1,
                                option2: question.option2,
                                option3: question.option3,
                                option4: question.option4,
                                answer1: question.answer1,
                                answer2: question.answer2,
                                answer3: question.answer3,
                                answer4: question.answer4,
                                answerDescription: question.answerDescription,
                                difficultyLevel: question.difficultylevel
                        ]
                    } else {
                        return [
                                id              : question.id,
                                question        : question.question,
                                answer          : question.answer,
                                difficultyLevel : question.difficultylevel
                        ]
                    }
                }

                if (isMcq) {
                    testMap.mcqs = jsonAnswers
                    testMap.mcqQuizId=resourceDtl.resLink
                    testMap.mcqResId=logEntry.resId
                    testMap.mcqLanguages =  [language1:resourceDtl.language1, language2:resourceDtl.language2]
                } else {
                    testMap.qna = jsonAnswers
                    testMap.qnaLanguages =  [language1:resourceDtl.language1, language2:resourceDtl.language2]
                }
            }
        }
        return testMap
    }

    //gptlog domain has the details of all the user activities. Function to get user activities based on username and optional resId and it should support pagination.
    def findGptLogsForUser(String username, int max, int offset, String resId) {
        List gptLogs
        if(resId!=null)
            gptLogs = GptLog.findAllByUsernameAndReadingMaterialResId(username, resId, [max: max, offset: offset, sort: 'id', order: 'desc'])
        else
            gptLogs = GptLog.findAllByUsername(username, [max: max, offset: offset, sort: 'id', order: 'desc'])
        List gptLogsWithUser = gptLogs.collect { gptLog ->
            def user = dataProviderService.getUserMst(gptLog.username)
            def resourceDtl = dataProviderService.getResourceDtl(gptLog.readingMaterialResId)
            def chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
            def bookMst = dataProviderService.getBooksMst(chaptersMst.bookId)

            return [id: gptLog.id, username: user.username, userPrompt: gptLog.userPrompt, systemPrompt: gptLog.systemPrompt,
                    response: gptLog.response, resId: gptLog.resId, readingMaterialResId: gptLog.readingMaterialResId,
                    promptType: gptLog.promptType, feedbackType: gptLog.feedbackType, feedback: gptLog.feedback,
                    dateCreated: gptLog.dateCreated, response2: gptLog.response2,name: user.name,bookTitle: bookMst.title,chapterName: chaptersMst.name,resName: resourceDtl.resourceName,
                    bookId: bookMst.id,dateCreated:utilService.convertAndFormatDateToIST(gptLog.dateCreated)]
        }

      return gptLogsWithUser
    }

    //function to get log for all users based readingMaterialResId and support pagination with date filtering.
    def findGptLogsForResId(String resId, int max, int offset, String startDate = null, String endDate = null) {
        def criteria = GptLog.createCriteria()
        List gptLogs

        if(resId!=null&&!"".equals(resId)&&!"null".equals(resId)) {
            gptLogs = criteria.list(max: max, offset: offset) {
                eq('readingMaterialResId', resId)

                if(startDate && endDate) {
                    def startDateObj = Date.parse('yyyy-MM-dd', startDate)
                    def endDateObj = Date.parse('yyyy-MM-dd', endDate) + 1
                    between('dateCreated', startDateObj, endDateObj)
                }

                order('id', 'desc')
            }
        } else {
            gptLogs = criteria.list(max: max, offset: offset) {
                if(startDate && endDate) {
                    def startDateObj = Date.parse('yyyy-MM-dd', startDate)
                    def endDateObj = Date.parse('yyyy-MM-dd', endDate) + 1
                    between('dateCreated', startDateObj, endDateObj)
                }

                order('id', 'desc')
            }
        }

         String name
        String bookTitle
        String chapterName
         List gptLogsWithUser = gptLogs.collect { gptLog ->
             name="Guest user"
             bookTitle="Not available"
             chapterName="Not available"
             def user = dataProviderService.getUserMst(gptLog.username)
             if(user!=null){
                 name = user.name
             }
             ResourceDtl resourceDtl = ResourceDtl.findById(gptLog.readingMaterialResId)
             //if resourceDtl is null skip this iteration
                if(resourceDtl==null) return null
             else {
                    def chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                    if (chaptersMst != null) {
                        chapterName = chaptersMst.name
                        def bookMst = dataProviderService.getBooksMst(chaptersMst.bookId)
                        if (bookMst != null) {
                            bookTitle = bookMst.title
                        }
                    }

                    return [id          : gptLog.id, name: name, userPrompt: gptLog.userPrompt, systemPrompt: gptLog.systemPrompt, response: gptLog.response,
                            resId       : gptLog.resId, readingMaterialResId: gptLog.readingMaterialResId, promptType: gptLog.promptType,
                            feedbackType: gptLog.feedbackType, feedback: gptLog.feedback, dateCreated: gptLog.dateCreated, response2: gptLog.response2,
                            username    : gptLog.username, dateCreated: utilService.convertAndFormatDateToIST(gptLog.dateCreated), bookTitle: bookTitle, chapterName: chapterName]
                }
        }
        return gptLogsWithUser
    }

    // Function to count total logs for pagination
    def countGptLogsForResId(String resId, String startDate = null, String endDate = null) {
        def criteria = GptLog.createCriteria()
        def count

        if(resId!=null&&!"".equals(resId)&&!"null".equals(resId)) {
            count = criteria.count {
                eq('readingMaterialResId', resId)

                if(startDate && endDate) {
                    def startDateObj = Date.parse('yyyy-MM-dd', startDate)
                    def endDateObj = Date.parse('yyyy-MM-dd', endDate) + 1
                    between('dateCreated', startDateObj, endDateObj)
                }
            }
        } else {
            count = criteria.count {
                if(startDate && endDate) {
                    def startDateObj = Date.parse('yyyy-MM-dd', startDate)
                    def endDateObj = Date.parse('yyyy-MM-dd', endDate) + 1
                    between('dateCreated', startDateObj, endDateObj)
                }
            }
        }

        return count
    }

    //report page for userfeedack. Function to get all the feedbacks based on feedbackType and support pagination.
    def getFeedbacks(int max, int offset) {
        List  gptLogs = GptLog.findAllByFeedbackIsNotNullOrFeedbackTypeIsNotNull([max: max, offset: offset, sort: 'id', order: 'desc'])
        List gptLogsWithUser = gptLogs.collect { gptLog ->
            def user = dataProviderService.getUserMst(gptLog.username)
            def resourceDtl = dataProviderService.getResourceDtl(gptLog.readingMaterialResId)
            def chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
            def bookMst = dataProviderService.getBooksMst(chaptersMst.bookId)

            return [id: gptLog.id, username: user.username, userPrompt: gptLog.userPrompt, systemPrompt: gptLog.systemPrompt,
                    response: gptLog.response, resId: gptLog.resId, readingMaterialResId: gptLog.readingMaterialResId,
                    promptType: gptLog.promptType, feedbackType: gptLog.feedbackType, feedback: gptLog.feedback,
                    dateCreated: gptLog.dateCreated, response2: gptLog.response2,name: user.name,bookTitle: bookMst.title,chapterName: chaptersMst.name,resName: resourceDtl.resourceName,
                    bookId: bookMst.id,dateCreated:utilService.convertAndFormatDateToIST(gptLog.dateCreated)]
        }

        return gptLogsWithUser
    }

    //api to get user specific data. If the resId is null, it should be considered as normal requests. If resId is not null, it should be considered as doubt. It should return number of both normal and doubt requests seperately in a hashmap. Username will be input to this api.
    //the suggested approach is not good. Use a direct sql to get the count, as we are interested in only the count.
    def getUserData(String username){
        def normalRequests = GptLog.countByUsernameAndResIdIsNotNull(username)
        def doubtRequests = GptLog.countByUsernameAndResIdIsNull(username)
        HashMap userData = new HashMap()
        userData.normalRequests = normalRequests
        userData.doubtRequests = doubtRequests
        return userData
    }


    def sendPdfToApi(File pdfFile, String fileName,request) {
        def boundary = "*****"
        def lineEnd = "\r\n"
        def twoHyphens = "--"
        URL url
        if("true".equals(request.getAttribute("gptAdmin"))){
            url = new URL(getGPTServerUrl(request)+"/processAdminPDFVector")
        }
        else  url = new URL(getGPTServerUrl(request)+"/processPDFVectorNew")
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        // Set the request method to POST
        connection.setRequestMethod("POST")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.setUseCaches(false)
        connection.setRequestProperty("Connection", "Keep-Alive")
        connection.setRequestProperty("Content-Type", "multipart/form-data;boundary=$boundary")

        OutputStream outputStream = connection.getOutputStream()
        outputStream.write((twoHyphens + boundary + lineEnd).bytes)
        outputStream.write("Content-Disposition: form-data; name=\"file\"; filename=\"${fileName}\"".bytes)
        outputStream.write(lineEnd.bytes)
        outputStream.write("Content-Type: application/pdf".bytes)
        outputStream.write(lineEnd.bytes)
        outputStream.write(lineEnd.bytes)
        // Write the PDF file bytes
        outputStream.write(pdfFile.bytes)
        outputStream.write(lineEnd.bytes)
        outputStream.write((twoHyphens + boundary + twoHyphens + lineEnd).bytes)

        outputStream.flush()
        outputStream.close()

        // Get the response
        int responseCode = connection.getResponseCode()
         println("responseCode "+responseCode)
        return responseCode
    }

    def getGPTServerUrl(request){
        String gptServerURL = "http://0.0.0.0:8000/api"
        String url = request.getRequestURL()
        if (url.indexOf("qa.wonderslate.com") > -1) {
            gptServerURL = "http://************:8000/api";  // QA environment
        }else if (url.indexOf("publish.gptsir.ai") > -1) {
            gptServerURL = "http://*************:8000/api";  // GPTSir Publish AI environment
        }else if (url.indexOf("publish.wonderslate.com") > -1) {
            gptServerURL = "http://*************:8000/api";  // WS Publish AI environment
        } else if(url.indexOf("qa.wonderslate.com") == -1 && url.indexOf("http://localhost") == -1 &&  url.indexOf("publish.gptsir.ai") == -1 &&  url.indexOf("publish.wonderslate.com") == -1){
            gptServerURL = "http://*************:8000/api";  // Live environment
        }
        return gptServerURL
    }

    def checkAndStorePDF(request,namespace,ResourceDtl resourceDtl){
  // create a get method call to gptserver for the method checkPDFExists sending the parameter as namespace. The return response will be in json
        URL url = new URL(getGPTServerUrl(request)+"/checkAdminPDFExists?namespace="+namespace)
        println(url)
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("GET")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.connect()
        def responseCode = connection.getResponseCode()
        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            def jsonResponse = jsonSlurper.parseText(response)
            if(jsonResponse.isExist){
                return "alreadyExists"
            }else{
                def file = new File(resourceDtl.resLink)
                try {
                    def resCode = promptService.sendPdfToApi(file,namespace,request)
                     return "uploaded"

                }catch(Exception e){
                    println(e.toString())
                   return "failed"
                }
            }
        }else{
            println("******** response code is "+responseCode)
            return "failed"
        }
    }

    def createAndUpdateGPTContent(String namespace,String readingMaterialResId,request,Prompts prompts,session){
        def query = prompts.basePrompt
        def promptLabel = prompts.promptLabel
        def promptType = prompts.parentPromptType!=null?prompts.parentPromptType:prompts.promptType

        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(""+readingMaterialResId))
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
        BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(chaptersMst.bookId)
        if(booksTagDtl!=null&&"School".equals(booksTagDtl.level)){
            //first check if booksTagDtl.grade is number
            if(booksTagDtl.grade.isNumber()) {
                int age = 6 + Integer.parseInt(booksTagDtl.grade)
                query = query + " The age of user to which this content is created is around  " + age  +" years old. And also this will be used for students from India. So please consider this when creating response." //append the grade to the query
            }

        }
        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
        if(booksMst!=null&&!"English".equals(booksMst.language)&&!"".equals(booksMst.language)&&booksMst.language!=null){
            query = query + " The response should be in "+booksMst.language+" language."
        }

        // call retrieveDataAdmin method from gptserver with the parameters namespace,query,promptType,readingMaterialResId. The input request will be in json format
        String filePath = resourceDtl.resLink
        URL url = new URL(getGPTServerUrl(request)+"/retrieveDataAdminNew")
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))

        def json = new JsonBuilder([namespace: namespace, query: query, resType: promptType, username: springSecurityService.currentUser.username,filePath: filePath])
         writer.write(json.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()

        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            def jsonResponse = jsonSlurper.parseText(response)
            println("***** the response is "+jsonResponse)

            String returnResponse = autoAddGptContent(session,promptType,readingMaterialResId,request,jsonResponse,promptLabel,query,prompts.parentPromptType)
            return returnResponse
        }else{  return null}
    }

    def autoAddGptContent(session,String promptType,String readingMaterialResId,request,jsonResponse,promptLabel,prompt,parentPromptType) {
        String returnResponse = "Created"
        try {
            Prompts prompts = Prompts.findByPromptType(promptType)
            parentPromptType = prompts.parentPromptType
            Prompts parentPrompt = prompts.parentPromptType != null ? Prompts.findByPromptType(prompts.parentPromptType) : null
            HashMap params = new HashMap()
            HashMap requestBody = new HashMap()
            //jsonResponse.answer is the string which contains the content in json format. How to convert this to

            String llmAnswer
            if("".equals(jsonResponse.answer)) {
                llmAnswer = "Not able to generate content"
            }else{
                llmAnswer = jsonResponse.answer
            }
            requestBody.put("answer", llmAnswer)
            requestBody.put("promptType", parentPromptType != null ? parentPromptType : promptType)
            requestBody.put("promptLabel", parentPromptType != null ? parentPrompt.promptLabel : prompts.promptLabel)
            requestBody.put("resId", readingMaterialResId)
            requestBody.put("prompt",  parentPromptType != null ? parentPrompt.promptLabel : prompts.promptLabel)
            if(parentPromptType!=null){
                println("promptLabel="+parentPrompt.promptLabel)
            }
            if ("mcqs".equals(promptType) || "mcq".equals(promptType) || "mcqs".equals(parentPromptType) || "mcq".equals(parentPromptType)) {

                 String responseAnswer = jsonResponse.answer[0]
                responseAnswer = responseAnswer.replaceAll("`", "")
                responseAnswer = responseAnswer.replaceAll("json", "")
                def answers = new JsonSlurper().parseText(responseAnswer)
                // Find the first list in the JSON object
                def firstListKey = null
                def firstList = null

                answers.each { key, value ->
                    if (value instanceof List) {
                        firstListKey = key
                        firstList = value
                        return // Exit the loop once the first list is found
                    }
                }
                returnResponse = createMCQResource(request, session, firstList, requestBody)


            } else if ("qna".equals(promptType) || "pns".equals(promptType) || "qna".equals(parentPromptType) || "pns".equals(parentPromptType)) {
                String responseAnswer = jsonResponse.answer[0]
                responseAnswer = responseAnswer.replaceAll("`", "")
                responseAnswer = responseAnswer.replaceAll("json", "")
                def answers = new JsonSlurper().parseText(responseAnswer)
                // Find the first list in the JSON object
                def firstListKey = null
                def firstList = null

                answers.each { key, value ->
                    if (value instanceof List) {
                        firstListKey = key
                        firstList = value
                        return // Exit the loop once the first list is found
                    }
                }

                returnResponse = createQAResource(request, session, firstList, requestBody)
            } else if ("flashcards".equals(promptType)) {
                String responseAnswer = jsonResponse.answer[0]
                responseAnswer = responseAnswer.replaceAll("`", "")
                responseAnswer = responseAnswer.replaceAll("json", "")
                def answers = new JsonSlurper().parseText(responseAnswer)
                returnResponse = createFlashCard(request, requestBody, answers)
            } else {
                returnResponse = createReadingResource(request, session, params, requestBody)
            }
            return returnResponse
        } catch (Exception e) {
            println("******* Exception in autoAddGptContent "+e.toString())
            returnResponse = "Failed"
            return returnResponse
        }
        return returnResponse

    }
    static String[] indexListClient =null
    static String[] indexListAdmin =null

    def getIndex(String userType){
        if("users".equals(userType)){
        if(indexListClient==null){
            KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("indexListClient",1)
            if(keyValueMst!=null){
                indexListClient = keyValueMst.keyValue.split(",")
            }
            }
        }
        else {
            if (indexListAdmin == null) {
                KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("indexListAdmin", 1)
                if (keyValueMst != null) {
                    indexListAdmin = keyValueMst.keyValue.split(",")
                }
            }
        }
        if("users".equals(userType)) {
            if(redisService.("currentClientIndex")==null){
                redisService.("currentClientIndex")="-1"
            }
            int index = Integer.parseInt(redisService.("currentClientIndex"))
            index++
            index =  index % indexListClient.size()
            redisService.("currentClientIndex") = ""+index
            return indexListClient[index]
        }else{
            if(redisService.("currentAdminIndex")==null){
                redisService.("currentAdminIndex")="-1"
            }
            int index = Integer.parseInt(redisService.("currentAdminIndex"))
            index++
            index =  index % indexListAdmin.size()
            redisService.("currentAdminIndex") = ""+index
            return indexListAdmin[index]
        }


    }

    def newUserPDFCreation(request,filePath,namespace){
        URL url = new URL(getGPTServerUrl(request)+"/processPDFVectorNew")
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))

        def json = new JsonBuilder([namespace: namespace, filePath: filePath])
        writer.write(json.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        return responseCode
    }
    def drivePdfVectorCreation(String urlStr,String filePath,String namespace){
        URL url = new URL(urlStr+"/uploadDrivePdf")
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
        def json = new JsonBuilder([namespace: namespace, filePath: filePath])
        writer.write(json.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        return responseCode
    }

    def savesnippedImg(path, requestBody){
        def dataURL = requestBody.imgData
        def bookId = requestBody.bookId
        def chapterId = requestBody.chapterId
        def resId = requestBody.resId
        def imgLink
        String uploadParentDir="supload"
        def parts = dataURL.split(",")
        if (parts.length != 2) throw new IllegalArgumentException("Invalid data URL format")
        def imageBytes = Base64.decoder.decode(parts[1])
        def filename = generateUUIDFileName()
        def dirPath = "${uploadParentDir}/gptChatSnips/${bookId}/${chapterId}/${resId}/extract/OEBPS/Images"
        imgLink = "${dirPath}/${filename}"
        File uploadDir = new File(path, dirPath)
        if (!uploadDir.exists()) uploadDir.mkdirs()
        FileUtils.writeByteArrayToFile(new File(uploadDir, filename), imageBytes)
        def imgUrl = requestBody.site+"/funlearn/downloadEpubImage?source="+imgLink
        requestBody.putAt("imgData", imgUrl)
        requestBody.putAt("imgLink", imgLink)
        return requestBody
    }
    def generateUUIDFileName() {
        def uuid = UUID.randomUUID().toString()
        return "image_${uuid}.png"
    }

    def groupMcqByLevels(resLink){
        def results = ObjectiveMst.createCriteria().list {
            eq("quizId", new Integer(resLink))
            projections {
                groupProperty("difficultylevel") // Group by difficultyLevel
                rowCount() // Count the occurrences of each difficulty level
            }
        }
        return results
    }

    def mapGroupedMcqs(results, reverseMapping){
        def transformedMap = reverseMapping.collectEntries { key, value ->
            [(key.toLowerCase()): value.toLowerCase()]
        }

        def transformedResult = results.collect { pair ->
            [pair[0].toLowerCase(), pair[1]]
        }

        def difficultyLevels = [:] // Initialize an empty map for difficulty levels
        transformedResult.each { result ->
            def groupedLevel = transformedMap[result[0]] ?: result[0] // Map to grouped level or keep as is
            difficultyLevels[groupedLevel] = (difficultyLevels[groupedLevel] ?: 0) + result[1] // Accumulate counts for grouped level
        }
        return difficultyLevels
    }

    def getDifficultyMapping() {
        def mappings = KeyValueMst.createCriteria().list {
            eq("siteId", new Integer(-1))
            like("keyName", "%_difficulty")
        }
        def difficultyMapping = [:]
        mappings.each { mapping ->
            def values = mapping.keyValue.split(',').collect { it.trim() }
            def keyNameWithoutSuffix = mapping.keyName.replace('_difficulty', '')
            difficultyMapping[keyNameWithoutSuffix] = values
        }
        return difficultyMapping
    }

    def checkIsTeacher(String siteId, request, params){
        boolean isTeacher = false
        String ipAddress
        if("yes".equals(params.app) && !"".equals(params.ipAddress)){
            ipAddress=params.ipAddress
        }else{
            ipAddress = utilService.getIPAddressOfClient(request)
        }
        if(springSecurityService.currentUser!=null){
            User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            if(user.teacher!=null) isTeacher = true
        }

        List usersInstituteDtl = userManagementService.getInstitutesForUser(new Integer(siteId),ipAddress)
        if(springSecurityService.currentUser!=null) {
            for (int i = 0; i < usersInstituteDtl.size(); i++) {
                if("true".equals(""+usersInstituteDtl[i].isInstructor)){
                    isTeacher = true
                    break
                }
            }
        }
        return isTeacher
    }

    def getTokenDetails(bookId, session){
        boolean purchasedGPT = false
        boolean showUpgrade = false

        int freeTokenCount = 0
        int paidTokenCount = 0
        boolean paidUser = false
        boolean previewMode = false
        BooksMst booksMst
        String bookType
        if(springSecurityService.currentUser != null && bookId!=null) {
            booksMst = dataProviderService.getBooksMst(new Long(bookId))
            bookType = booksMst.bookType
            BooksPermission booksPermission = gptLogService.getBooksPermission(new Integer(bookId),springSecurityService.currentUser.username)
            if(booksPermission!=null&&!"ADDEDFROMINSTITUTE".equals(booksPermission.poType)) paidUser = true
            User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            if(user.teacher!=null) isTeacher = true
            if(user.chatTokensBalance!=null) {
                paidTokenCount = user.chatTokensBalance.intValue()
            }
            if(booksPermission!=null&&booksPermission.chatTokensBalance!=null) {
                freeTokenCount = booksPermission.chatTokensBalance.intValue()
                if(booksPermission.bookType!=null&&("bookgpt".equals(booksPermission.bookType.toLowerCase()) || "ebookgptupgrade".equals(booksPermission.bookType.toLowerCase())||"ibookgptpro".equals(booksPermission.bookType.toLowerCase()))) purchasedGPT = true
                else{
                    BookPriceDtl bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(bookId), "ebookGPTUpgrade")
                    if(bookPriceDtl!=null) showUpgrade = true
                }
            }

            if(session["userdetails"]!=null&&session["userdetails"].authorities.any { it.authority == "ROLE_GPT_MANAGER" }) {
                purchasedGPT=true
                previewMode=false
                freeTokenCount=100
                bookType="bookgpt"
            }
        }else{
            if(session["previewFreeTokens"] ==null) session["previewFreeTokens"] = 10
            freeTokenCount = session["previewFreeTokens"]
        }

        return [
                freeTokenCount:freeTokenCount,
                paidTokenCount:paidTokenCount,
                previewMode:previewMode,
                bookType:bookType,
                purchasedGPT:purchasedGPT,
                showUpgrade:showUpgrade,
                paidUser:paidUser
        ]
    }


    def runPrompt(String filePath,String namespace,String query,String promptType,request){
        URL url = new URL(getGPTServerUrl(request)+"/retrieveDataAdminNew")
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))

        def json = new JsonBuilder([namespace: namespace, query: query, resType: promptType, username: springSecurityService.currentUser.username,filePath: filePath])
        writer.write(json.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()

        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            def jsonResponse = jsonSlurper.parseText(response)
            println("***** the response is "+jsonResponse)
            return jsonResponse
        }else{
            return "failed"
        }
    }
}
