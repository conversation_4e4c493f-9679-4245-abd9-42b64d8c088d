package com.wonderslate.cache

class JobTestController {
    def secondDataProviderService
    
    def index() {
        def result = secondDataProviderService.tellTheTime()
        render "Manual test of SecondDataProviderService.tellTheTime(): ${result}"
    }
    
    def testJob() {
        // This will manually execute the job's logic
        def currentTime = secondDataProviderService.tellTheTime()
        println "\n\n*** Manual execution of job logic: ${currentTime} ***\n\n"
        System.out.println("Manual execution of job logic: ${currentTime}")
        
        render "Manual execution of job logic: ${currentTime}"
    }
}
