//.true {
//  color:$orange-color;
//}
@media screen and (min-width: 320px) and (max-width: 767px) {
  .slide-left {
    margin-left: -600px;
  }
  .flash-card-slider {
    .item {
      min-width: 100%;
    }
  }
  .card-wrapper{
    width: 100%;
    padding: 0;
    .rprice-tag .complte-book{
      margin-left: 5px;
    }
  }
  .navbar-default .navbar-toggle:hover.collapsed, .navbar-default .navbar-toggle:focus.collapsed{
    background:#f05a2a;
  }
  .flip-container {
    width: 100%;
  }
  .flipper {
    width: 100%;
    .front {
      width: 100%;
    }
    .back {
      width: 100%;
    }
  }
  .notes-creation-wrapper {
    top: 0;
    height: calc(100vh - 85px);
    width: 100%;
    height: 100%;
    z-index: 1100;
  }
  .notes-creation-header {
    padding: 8px 8px 16px;
  }
  .notes-created-by-user {
    max-width: 100%;
    padding-right: 24px;
  }
  .notes-close-mobile {
    color: $black-color-dark;
    position: absolute;
    left: 0;
    top: 20px;
    &:hover {
      color: $black-color-dark;
    }
    &:active {
      color: $black-color-dark;
    }
    &:focus {
      color: $black-color-dark;
    }
  }
  .preview-book-name {
    font-size: 16px;
  }
  .marketplace-rating-wrapper {
    padding: 0;
    margin-top: 0;
  }
  .marketplace-rating-link:first-child {
    font-size: 14px;
    display: block;
    padding-right: 0;
    margin-bottom: 8px;
    border-right: 0;
  }
  .marketplace-rating-link:last-child {
    font-size: 14px;
    display: block;
    padding-left: 0;
  }
  #drift-widget-container {
    display: none;
  }
  .fixed-tabs-catos-mobile {
    position: fixed;
    width: 100%;
    background-color: $white-color;
    z-index: 99;
    animation: smoothScroll .2s forwards;
  }

  .tab-margin {
    margin-top: 60px;
  }
  .fixed-header {
    top: 0;
  }
  #materialtabspanel {
    margin-top: 40px;
  }
  .wonderslate-navbar {
    .navbar-container {
      .navbar-nav.header-menus {
        text-align: center;
        margin-left: 0;
      }
      .navbar-right {
        margin-left: 0;
        li {
          text-align: center;
        }
      }
      .navbar-collapse {
        padding-left: 0;
      }
    }
  }
  .r-form-edit{
    margin-top:20px;
  }
  .tabs-section .chapter-tabs{
    padding:4px 10px !important;

  }
  .r-tab .r-tab-child{
    padding-left:0;
  }
 .navbar-default .navbar-toggle {
    background-color: $orange-color;
    .icon-bar {
      border: 1px solid $white-color;
    }
  }

  .tabs-section .chapter-tabs li a:hover{

  }

  .navbar-default .navbar-toggle .icon-bar{
    background:#ffffff;
      }
  .social-login {
    padding: 11px 64px;
  }
  .modal.fade {
  .sign-in-modal-dialog {
    top: 80px;
    -webkit-transform: none;
       -moz-transform: none;
        -ms-transform: none;
         -o-transform: none;
            transform: none;
    }
  }
  .sign-in-modal-content {
    min-height: auto;
    padding: 10px;
  }
  .term-condition {
    padding: 0 10px;
    margin-top: 16px;
  }
  .create-account {
    padding: 0 10px;
  }
  .slider-hash-tag-item {
    margin-right: 10px;
  }
  .slider-books-heading {
    margin: 0;
  }
  .user-greeting {
    margin-left: 0;
  }
  .greeting-user {
    font-size: 30px;
  }
  .book-wrapper {
    margin: 0 0px 20px;
  }
  .books-content-wrapper {
    margin-top: 0;
    justify-content: space-evenly;
  }
  .tabs-holder {
    padding-left: 15px;
    padding-right: 15px;
    box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.25);
  }
  .carousel-showmanymoveone {
    .carousel-control {
      width: 10%;
      background-image: none;
    }
  }
  
  .nav-tabs-wrapper {
    max-width: 100%;
    overflow: hidden;
    overflow-x: auto;
    white-space: nowrap;
    li {
      display: inline-block;
      float: none;
      margin-right: 0;
      margin-left: 0;
      .level-label {
        display: none;
      }
    }
  }
  .tab-sub-categories-wrapper {
    width: 100%;
    padding: 0 5px;
    margin-left: 0;
    white-space: nowrap;
    overflow: hidden;
    overflow-x: auto;
  }
  .tab-sub-categories {
    background-color: #fff;
    padding: 4px 8px;
    padding-left: 15px;
    padding-right: 15px;
    margin-top: 0;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25), 0px 0px 4px rgba(0, 0, 0, 0.25);
    -webkit-transition: all .3s ease;
       -moz-transition: all .3s ease;
        -ms-transition: all .3s ease;
         -o-transition: all .3s ease;
            transition: all .3s ease;
  }
  .tab-sub-categories-item {
    margin-left: 5px;
    margin-bottom: 0;
  }
  .tab-sub-categories-item-btn {
    padding: 6px 15px;
    border: 1px solid rgba(68, 68, 68, 0.24);
  }
  .class-selection-div {
    padding: 10px;
    .btn-group{
      width:50%;
      float: right;
    }
    .class-selection-btn {
      width: 100%;
      text-align: center;
      background: none;
      padding: 8px 0 0 0;
      border: 0;
      -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
      box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    }
  }
  .main-footer {
    padding: 0;
  }
  .download-app-btn {
    max-width: 200px;
    height: 60px;
  }
  .footer-credits {
    text-align: center;
    padding: 10px 0;
  }
  .mobile-app-legend {
    margin: 0;
  }
  .footer-links {
    text-align: center;
    li {
      a {
        padding: 10px 0;
        &:hover {
          color: $orange-color;
          text-decoration: none;
        }
      }
    }
  }
  .preview-book-container {
    margin-top: 8px;
  }
  .preview-book-wrapper {
    margin: 0;
    padding: 24px 0;
  }
  .book-preview-detail {
    padding: 0 16px;
  }
  .preview-book-desc {
    margin-top: 0;
    padding: 16px;
  }
  .preview-book-btn {
    width: 100%;
    max-width: 100%;
  }
  .preview-book-btns {
    margin-top: 0;
  }
  .book-preview-desc {
    padding: 0;
    margin-top: 8px;
  }
  .video-section {
    justify-content: center;
  }
  .read-book-chapters {
    top: 0;
    width: 72%;
    min-height: calc(100vh - 1px);
    max-height: calc(100vh - 1px);
    margin-left: -600px;
    position: fixed;
    z-index: 999;
    overflow: hidden;
    overflow-y: auto;
  }
  #hideShowDivMobile {
    position: fixed;
    top: 35%;
    left: 0;
    width: 24px;
    height: 48px;
    display: block;
    background: url('../images/wonderslate/collapsed.png');
    background-size: 100% 100%;
    background-position: center;
    background-color: #EEEEEE;
    z-index: 68;
  }
  .rotated {
    background: url(wonderslate/collapse.png) !important;
    background-size: 100% 100% !important;
    background-position: center !important;
  }
  .book-read-material {
    width: 100% !important;
    max-height: 100%;
    padding-left: 15px;
  }
  #htmlreadingcontent {
    padding-left: 15px;
    padding-bottom: 70px;
    table {
      width: auto !important;
    }
  }
  #quizModal {
    z-index: 9999;
  }
  .tabs-section {
    width: 100%;
    .chapter-tabs {
      max-width: 100%;
      overflow: auto;
      overflow-y: hidden;
    }
  }
  .user-profile-orders-tab-content {
    float: none;
  }
  .user-profile-orders {
    min-height: calc(100vh - 245px);
    .user-profile-tabs {
      > li {
        margin-right: 8px;
      }
    }
  }
  .user-profile-image {
    float: none;
    margin: 0 auto;
  }
  .user-profile-details {
    width: 100%;
  }
  .user-profile-tab {
    padding: 16px;
  }
  .user-order-tab {
    padding: 16px;
  }
  .users-orders-details {
    padding: 8px;
  }
  .user-purchase-order-detail {
    width: 100%;
    border-right: 0;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(68, 68, 68, 0.2);
  }
  .order-book-image {
    margin-right: 8px;
  }
  .order-book-detail {
    width: 57%;
    padding-top: 0;
    padding-right: 0;
  }
  .order-payment-details {
    width: 100%;
    padding: 16px 0;
    border: 0;
  }
  .order-review-section {
    width: 100%;
    margin-bottom: 16px;
  }
  .book-reviews {
    padding: 0;
    margin: 16px 0 0 0;
  }
  .review-rating-wrapper {
    margin-bottom: 8px;
    padding: 16px
  }
  .rating-review-heading {
    margin: 0;
  }
  .book-overall-rating {
    padding-right: 0;
    padding: 15px;
    margin: 8px 0 0 0;
  }
  .rating-by-user {
    font-size: $font-size-base;
    margin: 0 0 16px;
  }
  .rating-bars-wrapper {
    padding: 0 15px;
    border: 0;
    border-left: 1px solid rgba(68, 68, 68, 0.2);
  }
  .user-write-review {
    padding: 0;
    margin-top: 16px;
  }
  .write-review-label {
    margin-top: 16px;
  }
  .user-reviews {
    padding: 16px;
  }
  .error-page {
    min-height: calc(100vh - 285px);
  }
  .error-head {
    font-size: 40px;
    margin: 0;
  }
  .error-text {
    font-size: 13px;
    line-height: normal;
  }
  .error-image-wrapper {
    margin: 0;
  }
  .publisher-tabs {
    >li {
      >a {
        font-size: 15px;
        padding: 0 8px 0 0;
        margin-right: 0;
      }
      &.active {
        a:after {
          width: 30px;
          border-bottom: 2px solid #F79420;
        }
      }
    }
  }
  .publisher-tabs-content {
    overflow-x: auto;
  }
  .side-rating {
    width: 30%;
  }
  .chapter-test-wise-detail-wrapper {
    width: 100%;
    padding: 0;
    margin: 16px 0 0 0;
  }
  .chapter-test-wise-detail-wrapper:nth-child(3n) {
    margin: 16px 0 0 0;
  }
  .purchase-details {
    width: 100%;
  }
  .purchased-book-info {
    max-width: 212px;
    padding: 8px 0 0 8px;
  }
  .browse-purchase-book {
    width: 100%;
    border: 0;
  }
  .chapters-modal {
    margin-top: 80px;
  }
  .chapter-selection-table {
    min-height: 400px;
    max-height: 400px;
    padding-right: 0;
    margin-bottom: 56px;
    border: 0;
  }
  .chapter-details-aside {
    padding-right: 15px;
    padding-left: 15px;
  }
  .book-unlock {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) -25.75%, rgba(255, 255, 255, 0.91) 36.14%, #FFFFFF 78.54%);
    background-position: top;
    background-size: 100% 100%;
    padding-top: 24px;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 24px;
  }
  .unlock-complete-book-btn {
    width: 60%;
    margin: 0 auto;
  }
  .section-btns {
    width: 100%;
  }
  .wonderslate-navbar {
    width: 100%;
  }
  .mobile-header-brand {
    float: left;
  }
  .navbar-right-mobile {
    float: right;
    width: 30%;
    text-align: right;
  }
  .mobile-header-icon {
    display: inline-block !important;
    width: 28px;
    height: 28px;
  }
  .mobile-header-icon:first-child {
    margin-right: 16px;
  }
  .mobile-search {
    display: block;
    width: 28px;
    height: 28px;
    background: url('../images/wonderslate/search-icon.svg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-indent: -99999px;
  }
  .mobile-user {
    display: block;
    width: 28px;
    height: 28px;
    background: url('../images/wonderslate/user-icon.svg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-indent: -99999px;
  }
  .mobile-bottom-menu-wrapper {
    list-style: none;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: $white-color;
    padding: 6px 0;
    margin: 0;
    box-shadow: 0px -1px 8px rgba(0, 0, 0, 0.25);
    z-index: 5;
  }
  .mobile-bottom-menu-item {
    float: left;
    width: 33.33%;
    font-weight: 500;
    line-height: normal;
    font-size: 11px;
    text-align: center;
    letter-spacing: 0.01em;
  }
  .mobile-bottom-menu-item-link {
    display: block;
    margin: 0 auto 4px;
    text-indent: -9999999px;
  }
  .mobile-store {
    width: 28px;
    height: 28px;
    background: url('../images/wonderslate/icons/wonderslate-shop-inactive.svg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    &.active {
      background: url('../images/wonderslate/icons/wonderslate-shop.svg') !important;
      background-repeat: no-repeat !important;
      background-size: 100% 100% !important;
    }
  }
  .mobile-library {
    width: 28px;
    height: 28px;
    background: url('../images/wonderslate/icons/library-inactive.svg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    &.active {
      background: url('../images/wonderslate/icons/library-active.svg') !important;
      background-repeat: no-repeat !important;
      background-size: 100% 100% !important;
    }
  }
  .mobile-test-gen {
    width: 28px;
    height: 28px;
    background: url('../images/wonderslate/icons/testgen-inactive.svg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    &.active {
      background: url('../images/wonderslate/icons/testgen-active.svg') !important;
      background-repeat: no-repeat !important;
      background-size: 100% 100% !important;
    }
  }
  .profile-dropdown {
    position: absolute !important;
    background-color: $white-color !important;
    top: 48px !important;
    border: 1px solid rgba(68, 68, 68, 0.24) !important;
  }
}

@media screen and (max-width: 920px) {
  .additional-ref-section {
    padding-left: 15px;
  }
  .additional-ref-wrapper {
    width: 100%;
  }
  .additional-ref-img-wrapper {
    width: 30%;
  }
  .additional-ref-img {
    max-width: 100%;
    height: auto;
  }
  .additional-ref-info {
    width: 70%;
    padding: 8px;
  }
}

@media screen and (max-width: 1280px) {
  .slider-book {
    max-width: 135px;
    margin-right: 0;
  }
  .slider-books-holder-down {
    left: 20px;
  }
}

@media screen and (min-width: 320px) and (max-width: 768px) and (orientation: portrait) {
  .top-books-list {
    width: 100%;
  }
  .slider-books-wrapper {
    min-height: 256px;
    top: -15px;
  }
  .slider-books-holder-down {
    left: 15px;
  }
  .slider-book {
    max-width: 128px;
    margin-right: 0;
  }
  .slider-book:last-child {
    margin-left: 16px;
  }
}

@media screen and (min-width: 320px) and (max-width: 768px) and (orientation: landscape) {
  .slider-hash-tag-item {
    margin-right: 30px;
  }
  .book-wrapper {
    margin: 0 8px 20px;
  }
  .video-section {
    justify-content: flex-start;
  }
}

@media screen and (min-width: 769px) {
  .book-read-material {
    overflow: hidden;
    overflow-y: auto;
  }
  .user-profile-orders {
    min-height: calc(100vh - 204px);
    margin-top: 40px;
  }
  .user-profile-orders-tab-content {
    padding-left: 32px;
    padding-right: 32px;
    margin-top: 40px;
  }
  .change-password-modal {
    .modal-sm {
      width: 424px;
    }
  }
  .book-review-modal {
    .modal-sm {
      width: 640px;
    }
  }
  .error-wrapper {
    padding: 0;
  }
  .error-image-wrapper {
    padding: 0;
  }
  .publisher-books-wrapper {
    padding: 0 40px;
  }

}

@media screen and (min-width: 1024px) {
  .user-profile-orders-tab-content {
    width: 100%;
  }
  .total-paid-amt {
    margin-top: 32px;
  }
}

@media screen and (min-width: 640px) {
  .book-review-modal {
    .modal-sm {
      width: 640px;
    }
  }
}

@media screen and (min-width: 768px) and (max-width: 770px) {
  .card-wrapper{
    width: 100%;
  }
  .read-book-chapters {
    width: 50%;
    margin-left: -500px;
    overflow: hidden;
    overflow-y: auto;
    position: fixed;
    z-index: 9999;
  }
  .book-read-material {
    width: 100%;
  }
  #hideShowDivTab {
    display: block !important;
    position: fixed;
    top: 35%;
    left: 0;
    width: 24px;
    height: 48px;
    display: block;
    background: url('../images/wonderslate/collapsed.png');
    background-size: 100% 100%;
    background-position: center;
    background-color: #EEEEEE;
    z-index: 9999;
  }
  .rotated {
    background: url(wonderslate/collapse.png) !important;
    background-size: 100% 100% !important;
    background-position: center !important;
  }
  .no-scroll {
    overflow: hidden;
    position: relative;
    height: 100%;
    width: 100%;
    position: fixed;
  }
  .book-read-material {
    max-height: 100%;
  }
  .chapter-test-wise-detail-wrapper {
    width: 50%;
    padding: 0 16px 0 0;
    margin: 32px 0 0 0;
  }
  .chapter-test-wise-detail-wrapper:nth-child(odd) {
    padding: 0 0 0 16px;
  }
  .section-btns {
    width: 100%;
  }

}

@media screen and (min-width: 768px) and (max-width: 1024px) {
  .purchase-details {
    width: 100%;
  }
  .browse-purchase-book {
    width: 100%;
    border: 0;
  }
  .video-url .modal, .web-url .modal{
    top:37%;
  }
  #cke_1_contents{
    height: 400px !important;
  }
  #additional .link-btn{
    text-align: right;
  }
  .tabs-section .chapter-tabs{
    max-width: 100%;
  }
  #main-navbar{
    display: flex !important;
  }
  .custom-prBtn, .custom-prBtn-bl{
    width: 400px;
  }
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
  .read-book-chapters{
    position: absolute;
    height: 100%;
    z-index: 9;
    width:25%;
  }
  #hideShowDiv{
    left:25%;
    z-index: 10;
  }
}
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .chapter-test-wise-detail-wrapper {
    margin: 32px 47px 0 0;
  }
}

@media screen and (min-width: 768px) {
  .fixed-tabs-holder-desktop {
    position: fixed;
    width: 100%;
    z-index: 99;
  }
}

.hide-btns {
  animation: hideSectionBtns .2s forwards;
}

/* ========== Animations ==========*/

@keyframes smoothScroll {
  0% {
    transform: translateY(-40px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes hideSectionBtns {
  0% {
    transform: translateY(100px);
  }
  100% {
    transform: translateY(100px);
  }
}

/*Tab devices-Raj*/

@media (min-width: 768px) and (max-width: 1024px){
  #book-read-material{
    width:100%;
  }
  .tabs-section .chapter-tabs#chapter-details-tabs{
    padding:4px 10px;
  }
  .r-tab .r-tab-child{
    padding-left:0;
  }
  .user-profile-details{
    width:100%;
  }
  .user-profile-orders-tab-content{
    float: none;
  }
  .r-form-edit{
    margin-top:20px;
  }
}