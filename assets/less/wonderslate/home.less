@import "../variables/responsive.less";
@import "color.less";
@import "common.less";

.moreforyou {
  display: none;
}
.page_main__wrapper {
  @media @extraLarge {
    .container {
      max-width: 1200px;
    }
  }
}
.top_main_mobile__menu {
  margin-bottom: 2rem;
  margin-top: 1rem;
  a.mdl-button {
    text-transform: inherit;
    background: #FFFFFF;
    box-shadow: 0 2px 5px rgba(128, 128, 128, 0.25);
    border-radius: 10px;
    margin: 0 .25rem;
    height: 80px;
    //width: 150px;
    display: flex;
    justify-content: start;
    align-items: center;
    color: rgba(68, 68, 68, 0.85);
    font-size: 12px;
    flex-direction: column;
    padding: 20px 15px;
    line-height: 1.2;
    text-align: center;
    @media @extraSmall {
      height: 90px;
      padding: 18px 15px;
    }
    &:hover {
      text-decoration: none;
      color: #06D781;
    }
    img {
      width: 18px;
      margin-bottom: 7px;
    }
    &:nth-child(6) img {
      width: 20px;
    }
  }
}
.db-main {
  margin-bottom: 15rem;
  @media @extraSmall, @small {
    margin-bottom: 8rem;
  }
  @media @medium {
    margin-bottom: 10rem;
  }
  .welcome-user {
    h3 {
      span {
        color: @purple;
        opacity: 0.3;
      }
      @media @extraSmall, @small {
        font-size: 1.5rem;
      }
    }
  }
  .db-section-title {
    p {
      font-size: 16px;
      font-weight: bold;
      color: rgba(68, 68, 68, 0.85);
      @media @extraSmall, @small {
        font-size: 14px;
      }
    }
  }
  .db-common-info {
    background: #FFFFFF;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    .card {
      border: none;
      position: relative;
      border-right: 1px solid #eee;
      border-bottom: 1px solid #eee;
      border-radius: 0;
      justify-content: center;
      min-height: 150px;
      @media @extraSmall, @small {
        min-height: 100px;
        padding: 2rem 0.5rem;
        &:nth-child(2n) {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
      @media @medium {
        padding: 2rem 1rem;
        &:nth-child(3n) {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
      @media @large {
        padding: 2rem 1rem;
        &:nth-child(4n) {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
      @media @extraLarge {
        &:nth-child(6n) {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
      &:before {
        content: '';
        position: absolute;
        width: 30px;
        height: 30px;
        background: #fff;
        right: -15px;
        top: -20px;
        border-radius: 50px;
        z-index: 1;
      }
      &:after {
        content: '';
        position: absolute;
        width: 30px;
        height: 30px;
        background: #fff;
        right: -15px;
        bottom: -20px;
        border-radius: 50px;
        z-index: 1;
      }
      a {
        img {
          width: 50px;
          height: 50px;
          border-radius: 50px;
          margin: 0 auto;
          padding: 2px;
          @media @extraSmall {
            width: 45px;
            height: 45px;
          }
        }
        p {
          margin-top: 10px;
          //font-size: 15px;
          color: rgba(68, 68, 68, 0.85);
          @media @extraSmall {
            font-size: 12px;
          }
        }
        &:hover {
          text-decoration: none;
          p {
            color: @purple;
            //font-weight: 500;
          }
        }
      }
    }
  }
  .four-cards {
    .card {
      @media @extraLarge {
        &:last-child {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
    }
  }
  .three-cards {
    .card {
      @media @medium, @large, @extraLarge {
        &:last-child {
          border-right: none;
        }
      }
      @media @large, @extraLarge {
        &:last-child {
          &:after, &:before {
            display: none;
          }
        }
      }
    }
  }
  .two-cards {
    .card {
      @media @medium, @large, @extraLarge {
        &:last-child {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
    }
  }
  .single-card {
    .card {
      border: none;
      &:after, &:before {
        display: none;
      }
    }
  }
}
