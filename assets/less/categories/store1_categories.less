.or-text {
  width: 200px;
  border-bottom: 1px solid #dee2e6;
  line-height: 0.1rem;
  font-size: 20px;
  color: rgba(68, 68, 68, 0.7);
  small {
    background: #ffffff;
    padding: 0 15px;
  }
}
.categories1 {
  position: relative;
  z-index: 10;
  .card-columns {
    @media @iPad-portrait {
      -webkit-column-count: 2;
      -moz-column-count: 2;
      column-count: 2;
    }
    @media @iPad-landscape {
      -webkit-column-count: 2;
      -moz-column-count: 2;
      column-count: 2;
    }
  }
  .nav-pills {
    border-radius: 0.25rem;
    border:none;
    /*li+li {
      border-left: 1px solid #dee2e6;
    }*/
    @media @iPhone {
      overflow-x: auto;
      white-space: nowrap;
      flex-wrap: nowrap;
      overflow-y: hidden;
      //border-bottom: 1px solid #dee2e6;
      border-radius: 0;
    }
    @media @iPhone6-landscape {
      overflow-x: auto;
      white-space: nowrap;
      flex-wrap: nowrap;
      overflow-y: hidden;
      //border-bottom: 1px solid #dee2e6;
      border-radius: 0;
    }
    @media @iPhone7-landscape {
      overflow-x: auto;
      white-space: nowrap;
      flex-wrap: nowrap;
      overflow-y: hidden;
      //border-bottom: 1px solid #dee2e6;
      border-radius: 0;
    }
    li:first-child a {
      @media @iPhone {
        padding-left: 0;
      }
      @media @iPhone6-landscape {
        padding-left: 0;
      }
      @media @iPhone7-landscape {
        padding-left: 0;
      }
    }
    li:last-child a {
      @media @iPhone {
        border: none;
      }
      @media @iPhone6-landscape {
        border: none;
      }
      @media @iPhone7-landscape {
        border: none;
      }
    }
  }
  a.nav-link {
    display: flex;
    align-items: center;
    border: none;
    position: relative;
    background-color: #cbeef1;
    @media @iPhone {
      padding: 0 10px;
      margin-bottom: 10px;
      border-right: 1px solid #444;
      border-radius: 0;
      background: none !important;
    }
    @media @iPhone6-landscape {
      padding: 0 10px;
      margin-bottom: 10px;
      border-right: 1px solid #444;
      border-radius: 0;
      background: none !important;
    }
    @media @iPhone7-landscape {
      padding: 0 10px;
      margin-bottom: 10px;
      border-right: 1px solid #444;
      border-radius: 0;
      background: none !important;
    }
    &:hover {
      color: @ws-lightOrange !important;
      @media @iPhone {
        border-color: #444;
      }
      @media @iPhone6-landscape {
        border-color: #444;
      }
      @media @iPhone7-landscape {
        border-color: #444;
      }
    }
    &.active:hover {
      color: @ws-white;
      @media @iPhone {
        color: @ws-lightOrange !important;
      }
      @media @iPhone6-landscape {
        color: @ws-lightOrange !important;
      }
      @media @iPhone7-landscape {
        color: @ws-lightOrange !important;
      }
    }
    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      margin: 0 auto;
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 10px solid @ws-lightOrange;
      opacity: 0;
      visibility: hidden;
      transform: translateY(0);
      -webkit-transform: translateY(0);
      -moz-transform: translateY(0);
      -ms-transform: translateY(0);
      z-index: -1;
      transition: all 0.1s linear;
      -webkit-transition: all 0.1s linear;
      -moz-transition: all 0.1s linear;
      @media @iPhone {
        display: none;
      }
      @media @iPhone6-landscape {
        display: none;
      }
      @media @iPhone7-landscape {
        display: none;
      }
    }
    span {
      margin-left: 0.7rem;
      @media @iPhone {
        margin-left: 0.5rem;
      }
      @media @iPhone6-landscape {
        margin-left: 0.5rem;
      }
      @media @iPhone7-landscape {
        margin-left: 0.5rem;
      }
    }
  }
  .nav-pills {
    a.nav-link.active, a.nav-link:focus {
      background-color: @ws-lightOrange !important;
      @media @iPhone {
        background-color: transparent !important;
        color: @ws-lightOrange !important;
        border-color: #444 !important;
      }
      @media @iPhone6-landscape {
        background-color: transparent !important;
        color: @ws-lightOrange !important;
        border-color: #444 !important;
      }
      @media @iPhone7-landscape {
        background-color: transparent !important;
        color: @ws-lightOrange !important;
        border-color: #444 !important;
      }
      &:after {
        opacity: 1;
        visibility: visible;
        transform: translateY(9px);
        -webkit-transform: translateY(9px);
        -moz-transform: translateY(9px);
        -ms-transform: translateY(9px);
      }
    }
  }
  .card {
    .card-header {
      border-bottom: none;
      background-color: rgba(0,0,0,.08);
    }
    .card-body {
      color: #dddddd;
      position: relative;
      a {
        //padding: 0px 8px;
        //font-weight: 500;
        white-space: normal;
        color: #ffffff;
        display: inline-block;
        /*border: 1px solid;
        border-radius: 50px;
        margin: 0 0 5px;*/
        &:hover, &:focus {
          /*text-decoration: none;
          background-color: #ffffff !important;
          color: @ws-lightOrange;*/
        }
        &:first-child {
          padding-left: 0;
        }
      }
      .divider {
        padding: 0 8px;
      }
      .divider:last-child {
        display: none;
      }
    }
    #goback-btn1, #goback-btn2 {
      position: absolute;
      left: 12px;
      top: 12px;
      padding: 1px 8px;
      display: none;
      font-weight: normal;
      span {
        @media @iPhone {
          display: none;
        }
        @media @iPad-landscape {
          display: none;
        }
        @media @iPad-portrait {
          display: none;
        }
      }
    }
  }
  #allSyllabus, #allStates {
     position: relative;
     /*left: 15px;
     right: 15px;*/
     transform-origin: left;
     transform: scale(1);
     -webkit-transform: scale(1);
     -moz-transform: scale(1);
     -ms-transform: scale(1);
     transition: all 0.5s linear;
     -webkit-transition: all 0.5s linear;
     -moz-transition: all 0.5s linear;
     -ms-transition: all 0.5s linear;
     opacity: 1;
     //display: block;
   }
  #schoolLevel, #stateExams {
    position: absolute;
    left: 15px;
    top: 20px;
    right: 15px;
    bottom: 15px;
    transform: translateY(100%);
    -webkit-transform: translateY(100%);
    -moz-transform: translateY(100%);
    -ms-transform: translateY(100%);
    transition: all 0.3s linear;
    -webkit-transition: all 0.3s linear;
    -moz-transition: all 0.3s linear;
    -ms-transition: all 0.3s linear;
    opacity: 0;
    //display: none;
    /*@media @iPhone {
      position: relative;
    }*/
  }
  .show_exams {
    order: 1;
  }
  .state-job-exams {
    order: 999;
    flex: 0 0 100%;
    max-width: 100%;
    @media @iPhone {
      .card-header {
        padding-left: 45px;
      }
    }
  }
  #pills-school {
    .card-body {
      min-height: 200px;
      overflow: hidden;
      @media @iPhone {
        min-height: 300px;
      }
    }
    @media @iPhone {
      .card-header {
        padding-left: 45px;
      }
    }
  }
  #pills-exams .state-job-exams .card-body {
    min-height: 200px;
    overflow: hidden;
    @media @iPhone {
      min-height: 300px;
    }
  }
}

/* Print Books Management Page */
.printbooks_management {
  .card {
    transition: all 0.3s linear;
    -webkit-transition: all 0.3s linear;
    -moz-transition: all 0.3s linear;
    &:hover {
      box-shadow: 0 .5rem 1rem rgba(0,0,0,.15);
      border-color: @ws-lightOrange;
    }
  }
}

/* Publishing Books Page */
.publishing_sales {
  #content-books {
    .form-group {
      padding: 0 15px;
    }
    input {
      width: 100%;
    }
  }
  .total_sales strong {
    color: @ws-lightOrange;
  }
}

.btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem @ws-transparentOrange !important;
}

/* Statistics */
.statistics {
  .card {
    h4 {
      font-weight: bold;
      color: @ws-lightOrange;
    }
    h6 {
      font-weight: bold;
    }
    .thumbnail {
      width: 90px;
      height: 90px;
      border-radius: 50px;
      border: 1px solid @ws-lightOrange;
      @media @iPhone {
        border: none;
      }
    }
  }
}

/* Testimonials Slider */
#testimonialSlider {
  .card {
    i {
      font-size: 100px;
      position: absolute;
      left: -10px;
      top: -30px;
      transform: rotate(180deg);
      -webkit-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
      color: fade(@ws-lightOrange, 30%);;
      z-index: 1;
    }
    .card-body {
      position: relative;
      z-index: 10;
      border-radius: 10px 50px 10px 50px;
      min-height: 160px;
    }
    h5.card-title {
      color: @ws-lightOrange;
    }
  }
  ul.slick-dots {
    li {
      width: 15px;
      height: 15px;
      button {
        width: 15px;
        height: 15px;
        background-color: fade(@ws-lightOrange, 20%);
        border-radius: 50px;
        &:before {
          display: none;
        }
      }
    }
    li.slick-active {
      button {
        background-color: @ws-lightOrange;
        border-radius: 50px;
      }
    }
  }
  .slick-arrow {
    width: 35px;
    height: 70px;
  }
  .slick-prev {
    left: -40px;
  }
  .slick-next {
    right: -40px;
  }
}