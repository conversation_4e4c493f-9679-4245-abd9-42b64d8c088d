@import "../variables/responsive.less";
@import "./colors.less";

//body{
//  background: @dark-bg-1;
//  //min-height: 100vh;
//  //overflow-y: hidden;
//}
.gamer-profile {
  min-height: 100vh;
  .media-body{
    p{
      color:@black;
    }
    h5{
      color:@black;
    }
  }
  .media {
    height: 50vh;
    img{
      border: 3px solid @white-color;
    }
    h5,p{
      padding: 0;
      margin: 0;
    }
    p{
      font-size: 10px;
    }
    &:first-child{
      padding: 6rem 2rem;
    }
    &.botProfile{
      &:last-child{
        border:none;
        padding: 6rem 2rem;
        h5,p{
          text-align: right;
        }
      }
    }
  }
}

.circle-wrapper{
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 999;
  .roundbg{
    background: url("../../images/prepJoy/roundbg.svg") center no-repeat;
    background-size: contain;
    width: 80px;
    height: 100%;
    margin: 40px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    img{
      width: 45px;
    }
  }
}

.locate{
  color:@black;
  font-size: 18px;
  margin-bottom: 1rem;
}

.playerbg{
  background: url("../../images/prepJoy/playerbg.svg") center no-repeat;
  background-size: cover;
  background-color:@light-red-1;
}
.bot-anim-wrapper{
  display: flex;
}
.bot-anim{
  background: url("../../images/prepJoy/botanim.svg") center no-repeat;
  height: 50px;
  width: 50px;
  animation: mymove 4s infinite;
  position: relative;
}
@keyframes mymove {
  0%   { left:-50px; top:0px;}
  25%   { left:300px; top:0px;}
  50%  { left:-50px; top:0px;}
  75%   { left:300px; top:0px;}
  100%  { left:-50px; top:0px;}
}

.botbg{
  background: url("../../images/prepJoy/botbg.svg") center no-repeat;
  background-size: cover;
}
.botselect{
  height: 45vh;
  display: flex;
  align-items: center;
}

.no-counter{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
#no-counter{
  color:@black;
}
#readyGo {
  position:relative;
}

@-webkit-keyframes count {
  0% {transform: scale(1.5);}
  100% {transform: scale(1);}
}

.nums {
  font-size:5rem;
  height:auto;
  top:0;
  right:0;
  bottom:0;
  left:0;
  margin:auto;
  text-align:center;
}

.three {
  -webkit-animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
  animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
}

.two {
  -webkit-animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
  animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
}

.one {
  -webkit-animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
  animation:count 0.1s cubic-bezier(0.1,0.1,1,1) 1;
}

.quiz-profile{
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-bottom: 0.5rem;
  padding-top: 1rem;
  .media{
    img{
      border:2px solid @white-color;
    }
  }
  .username{
    font-size: 12px;
    color:@black;
    margin-bottom: 0;
  }
  .score{
    font-size: 12px;
    color:@black;
  }
}
.main-timer{
  text-align: center;
  h4{
    font-size: 10px;
    text-transform: uppercase;
    color:@steal-color-1;
    margin-bottom: 0px;
  }
  p{
    font-size: 16px;
    color:@steal-color-2;
    font-weight: 500;
  }
}

.base-timer {
  position: relative;
  width: 40px;
  height: 40px;
}

.base-timer__svg {
  transform: scaleX(-1);
}

.base-timer__circle {
  fill: none;
  stroke: none;
}

.base-timer__path-elapsed {
  stroke-width: 7px;
  stroke: grey;
}

.base-timer__path-remaining {
  stroke-width: 7px;
  stroke-linecap: round;
  transform: rotate(90deg);
  transform-origin: center;
  transition: 1s linear all;
  fill-rule: nonzero;
  stroke: currentColor;
}

.base-timer__path-remaining.green {
  color: @light-green;
}

.base-timer__path-remaining.orange {
  color: orange;
}

.base-timer__path-remaining.red {
  color: red;
}

.base-timer__label{
  position: absolute;
  width: 40px;
  height: 40px;
  top: 0;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  display: none;
}
.time-wrapper{
  width: 40px;
  height: 40px;
  position: relative;
  margin: 5px auto;
}
#countdown {
  position: absolute;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  display: flex;
  top:0;
}
#app{

}


#user-status,.user-status{
  color:@light-red-1;
  font-family:'Righteous', cursive;
  font-size: 26px;
  text-align: center;
}
.playScoreCard{
  border: 1px dashed;
  padding: 0.5rem 2rem;
  border-radius: 10px;

  &__text{
    font-size: 16px;
  }
  &__points{
    margin-bottom: 0;
    text-align: center;
    font-size: 36px;
  }
}
.result-status-text{
  font-size: 16px;
}

.progress{
  position: absolute;
  top:0;
  width:100%;
  border-radius: 0;
  height:100% !important;
  .progress-bar{
    background-color:@steal-color-2;
  }
}

.progress-bar-vertical {
  width: 8px;
  height: 100%;
  display: flex;
  align-items: flex-end;
  position: absolute;
  bottom:0;
  top:initial;
  border-radius:4px ;
  background: @dark-bg-1;
  &.player1{
    left: 0;
  }
  &.player2{
    right: 0;
  }
  &.progress-correct{
    background: rgb(66 181 56 / 50%);
  }
  &.progress-incorrect{
    background: rgb(218 1 1 / 50%);
  }
}

.progress-bar-vertical .progress-bar {
  width: 100%;
  height: 0;
  -webkit-transition: height 0.6s ease;
  -o-transition: height 0.6s ease;
  transition: height 0.6s ease;
}


.gameTimerSelection{
  select{
    border: 1px solid #c8c5c5;
    border-radius: 2px;
    font-size: 12px;
  }
}

.gameSoundToggle{
  position: relative;
  input{
    visibility: hidden;
  }
  .gameSoundToggleBtn{
    width: 50px;
    padding: 2px;
    border-radius: 100px;
    margin-bottom: 0 !important;
    cursor: pointer;
    .gameSoundToggleBtnBall{
      background: red;
      width: 15px;
      height: 15px;
      border-radius: 50%;
      transition: all 0.3s ease-in;
    }
  }
}
.gameSoundToggle input:checked ~ .gameSoundToggleBtn .gameSoundToggleBtnBall{
    transform: translateX(29px);
    background: #00c600;
}

.medalWinningText{
  margin-top: -450px;
  @media (max-width: 768px){
    margin-top: -250px;
  }
}

.gameExit{
  button{
    background: transparent;
    border: 1px solid #fff;
    border-radius: 5px;
    padding: 5px 1rem;
    color: #fff;
    margin-top: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    @media (max-width: 768px){
      margin-right: 10px;
    }
    &:active{
      transform: scale(0.9);
      outline: none;
    }
    &:focus{
      outline: none;
    }
  }
}