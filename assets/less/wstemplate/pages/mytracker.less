@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// My Tracker Styles
.my-tracker {
  .ct-series-a .ct-bar {
    /* Colour of your bars */
    stroke:  @cyan;
    stroke-width: 30;
  }
  #userTimeReport {
    position: relative;
  }
  #emptyData p {
    color: @light-gray;
  }
  .chart-titles {
    h4 {
      @media @extraSmallDevices {
        font-size: 18px;
      }
    }
  }
  #yourForest {
    background: @white;
    border: 1px solid @dark-blue;
    box-shadow: 0 0 10px @gray-light-shadow;
    border-radius: 5px;
    h4 {
      color: @dark-blue;
      font-size: 20px;
    }
  }
  .forest-info {
    li {
      padding: 0 15px;
      border-right: 1px solid @dark-blue;
      color: @dark-blue;
      line-height: normal;
      font-size: 14px;
      @media @extraSmallDevices {
        padding: 0 10px;
      }
      &:last-child {
        border: none;
      }
    }
  }
}